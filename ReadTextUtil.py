import os
import random

import data

class ReadTextUtil:

    def __init__(self):
        current_directory = os.path.dirname(os.path.abspath(__file__))

        text1 = current_directory+"/static/text1.txt"
        self.textLine1List = []

        text1File = open(text1)
        text1FileLines = text1File.readlines()
        for line in text1FileLines:
            self.textLine1List.append(line.replace("\n", "").replace(" ", ""))
        text1File.close()

        text2 = current_directory+"/static/text2.txt"
        self.textLine2List = []

        text2File = open(text2)
        text2FileLines = text2File.readlines()
        for line in text2FileLines:
            self.textLine2List.append(line.replace("\n", "").replace(" ", ""))
        text2File.close()

        self.text1Length = len(self.textLine1List)
        self.text2Length = len(self.textLine2List)


    def getText1Length(self):
        return self.text1Length

    def getText2Length(self):
        return self.text2Length

    def getTextLine1List(self):
        return self.textLine1List

    def getTextLine2List(self):
        return self.textLine2List

    def getStringsValue(self):
        name_shrinks_count = random.randint(2, 15)
        shrinks_name = ""

        for i in range(name_shrinks_count):
            tag = random.randint(0, 1)
            index1 = random.randint(0, (self.getText1Length() - 1))
            index2 = random.randint(0, (self.getText2Length() - 1))
            if tag == 0:
                nameValue = self.getTextLine1List()[index1]
            else:
                nameValue = self.getTextLine2List()[index2]

            shrinks_name = shrinks_name + " " + nameValue
            # if isAddSingleChar > 4 and not isHasAddNumberChar:
            #     singleNumberChar = str(random.randint(0, 9)) + random.choice(data.lowercaseLetters)
            #     shrinks_name = shrinks_name + "_" + nameValue + "_" + singleNumberChar
            #     isHasAddNumberChar = True
            # else:

        return shrinks_name

    def getShrinkName(self):
        isAddSingleChar = random.randint(0, 5)
        name_shrinks_count = random.randint(2, 4)
        shrinks_name = ""

        for i in range(name_shrinks_count):
            tag = random.randint(0, 1)
            index1 = random.randint(0, (self.getText1Length() - 1))
            index2 = random.randint(0, (self.getText2Length() - 1))
            if tag == 0:
                nameValue = self.getTextLine1List()[index1]
            else:
                nameValue = self.getTextLine2List()[index2]

            shrinks_name = shrinks_name + "_" + nameValue
            # if isAddSingleChar > 4 and not isHasAddNumberChar:
            #     singleNumberChar = str(random.randint(0, 9)) + random.choice(data.lowercaseLetters)
            #     shrinks_name = shrinks_name + "_" + nameValue + "_" + singleNumberChar
            #     isHasAddNumberChar = True
            # else:

        return shrinks_name

    def getClassName(self):
        name_shrinks_count = random.randint(2, 4)
        shrinks_name = ""

        for i in range(name_shrinks_count):
            tag = random.randint(0, 1)
            index1 = random.randint(0, (self.getText1Length() - 1))
            index2 = random.randint(0, (self.getText2Length() - 1))
            if tag == 0:
                nameValue = self.getTextLine1List()[index1].capitalize()
            else:
                nameValue = self.getTextLine2List()[index2].capitalize()

            shrinks_name = shrinks_name + nameValue

        return shrinks_name

    def getTextValue(self):
        name_shrinks_count = random.randint(1, 3)
        shrinks_name = ""

        for i in range(name_shrinks_count):
            tag = random.randint(0, 1)
            index1 = random.randint(0, (self.getText1Length() - 1))
            index2 = random.randint(0, (self.getText2Length() - 1))
            if tag == 0:
                nameValue = self.getTextLine1List()[index1].capitalize()
            else:
                nameValue = self.getTextLine2List()[index2].capitalize()

            shrinks_name = shrinks_name + " " + nameValue

        return shrinks_name

    def getSingleText(self):
        index1 = random.randint(0, (self.getText1Length() - 1))
        return self.getTextLine1List()[index1].capitalize()

    def getPropertyName(self):
        name_shrinks_count = random.randint(1, 3)
        shrinks_name = ""

        for i in range(name_shrinks_count):
            tag = random.randint(0, 1)
            index1 = random.randint(0, (self.getText1Length() - 1))
            index2 = random.randint(0, (self.getText2Length() - 1))
            if i == 0:
                if tag == 0:
                    nameValue = self.getTextLine1List()[index1]
                else:
                    nameValue = self.getTextLine2List()[index2]
            else:
                if tag == 0:
                    nameValue = self.getTextLine1List()[index1].capitalize()
                else:
                    nameValue = self.getTextLine2List()[index2].capitalize()

            shrinks_name = shrinks_name + nameValue

        return shrinks_name

    def getMethodName(self):
        name_shrinks_count = random.randint(2, 5)
        shrinks_name = ""

        for i in range(name_shrinks_count):
            tag = random.randint(0, 1)
            index1 = random.randint(0, (self.getText1Length() - 1))
            index2 = random.randint(0, (self.getText2Length() - 1))
            if i == 0:
                if tag == 0:
                    nameValue = self.getTextLine1List()[index1]
                else:
                    nameValue = self.getTextLine2List()[index2]
            else:
                if tag == 0:
                    nameValue = self.getTextLine1List()[index1].capitalize()
                else:
                    nameValue = self.getTextLine2List()[index2].capitalize()

            shrinks_name = shrinks_name + nameValue

        return shrinks_name

    def getProguardValue(self):
        name_shrinks_count = random.randint(3, 7)
        shrinks_name = ""

        for i in range(name_shrinks_count):
            tag = random.randint(0, 1)
            index1 = random.randint(0, (self.getText1Length() - 1))
            index2 = random.randint(0, (self.getText2Length() - 1))
            if i == 0:
                if tag == 0:
                    nameValue = self.getTextLine1List()[index1]
                else:
                    nameValue = self.getTextLine2List()[index2]
            else:
                if tag == 0:
                    nameValue = self.getTextLine1List()[index1].capitalize()
                else:
                    nameValue = self.getTextLine2List()[index2].capitalize()

            shrinks_name = shrinks_name + nameValue

        return shrinks_name
