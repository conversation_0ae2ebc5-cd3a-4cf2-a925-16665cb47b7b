plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.plugin.serialization' version '1.8.21'
}

apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'kotlin-parcelize'

def STRING = "String"
android {
    compileSdkVersion Versions.compileSdkVersion
    buildToolsVersion Versions.buildToolsVersion

    defaultConfig {
        applicationId Versions.applicationId
        minSdkVersion Versions.minSdkVersion
        targetSdkVersion Versions.targetSdkVersion
        versionCode Versions.versionCode
        versionName Versions.versionName

        multiDexEnabled true

        vectorDrawables.useSupportLibrary = true

        javaCompileOptions {
            annotationProcessorOptions {
//                includeCompileClasspath = true
//                arguments = [moduleName: project.getName()]
            }
        }

        ndk {
            abiFilters 'armeabi' , 'armeabi-v7a',  'arm64-v8a'
        }

        signingConfigs {
            debug {
                keyAlias 'debug'
                keyPassword '123456789'
                storeFile file('../keystore/debug.jks')
                storePassword '123456789'
            }

            atest {//p01 packageName
                keyAlias 'p01103'//p01 keyAlias
                keyPassword '123456789'
                storeFile file('../keystore/p01103.keystore')//p01 storeFile
                storePassword '123456789'
            }
        }
    }

    packagingOptions {
        exclude "META-INF/gradle/incremental.annotation.processors"
        exclude "META-INF/INDEX.LIST"
        exclude 'META-INF/DEPENDENCIES'
        exclude 'AndroidManifest.xml'
    }

    buildTypes {
        debug {
            minifyEnabled false
        }

        release {
            shrinkResources true
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    buildFeatures {
        dataBinding = true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    sourceSets {
        main {
            // @formatter:off
            res.srcDirs = [
                    'src/main/res',
                    'src/main/res-main',
                    'src/main/res-image',
                    'src/main/res-selecter',
                    'src/main/res-marqueeview'
            ]
            // @formatter:on
        }
    }

    flavorDimensions "fenqi"
    //all versionCode 103
    productFlavors{
        atest{
            applicationId "com.anju.youmihua"
            versionCode 103//versionCode
            versionName "1.0.3"//versionName
            signingConfig signingConfigs.atest

            proguardFiles getDefaultProguardFile('proguard-android.txt'),'src/atest/proguard-rules.pro','proguard-rules.pro'
        }

    }

    android.applicationVariants.all { variant ->
        variant.outputs.all {
            if (project.hasProperty('pname')){
                outputFileName = "01.apk"
            } else {
                outputFileName = "${variant.flavorName}_${variant.versionCode}_${variant.buildType.getName().toLowerCase()}.apk"
            }
        }
    }
}

dependencies {
    implementation 'net.java.dev.jna:jna:5.11.0'
    implementation fileTree(dir: 'libs', include: ['*.jar','*.aar'])
    implementation Libs.appcompat
    implementation Libs.material
    implementation Libs.constraintlayout
    implementation Libs.core_ktx

    implementation Libs.okhttp3,{
        exclude group: "com.squareup.okio"
    }
    implementation Libs.okhttp3_logging_interceptor,{
        exclude group: "com.squareup.okio"
    }
    // implementation Libs.rxPermission,{
    //     exclude group: "org.reactivestreams"
    // }
    implementation Libs.rxJava3,{
        exclude group: "org.reactivestreams"
    }
    implementation Libs.refreshview

    implementation Libs.eventbus
    implementation Libs.glide4

    //implementation Libs.gson
    implementation Libs.kotlinx_coroutines

    implementation Libs.retrofit2,{
        exclude group: "com.squareup.okio"
    }
    implementation Libs.retrofit2_converter_gson,{
        exclude group: "com.squareup.okio"
        exclude group: "com.google.code.gson"
    }

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21"
    implementation "com.android.support:support-annotations:28.0.0"
    implementation 'cn.hutool:hutool-crypto:5.8.16'
    implementation "com.squareup.retrofit2:converter-scalars:2.0.0",{
        exclude group: "com.squareup.okio"
    }

    implementation "androidx.lifecycle:lifecycle-livedata-ktx:2.3.0"

    implementation "io.github.lucksiege:pictureselector:v3.10.6"
    implementation "io.github.lucksiege:compress:v3.10.6"

    implementation 'com.aliyun.dpa:oss-android-sdk:2.9.19',{
        exclude group: "com.squareup.okio"
    }

    implementation 'com.afollestad.material-dialogs:core:3.3.0'
    implementation 'com.afollestad.material-dialogs:bottomsheets:3.2.1'
    implementation 'com.afollestad.material-dialogs:datetime:3.2.1'
    implementation 'com.github.loper7:DateTimePicker:0.6.3'


    implementation ('com.amazonaws:aws-android-sdk-s3:2.73.0'){
        exclude group: 'com.google.code.gson', module: 'gson'
    }

    implementation 'com.android.support.constraint:constraint-layout:2.0.4'
    implementation 'androidx.multidex:multidex:2.0.1'

    //1. 云刷脸SDK
    implementation files('libs/WbCloudFaceLiveSdk-face-v6.0.0-5785db84.aar')
    //2. 云common SDK
    implementation files('libs/WbCloudNormal-v5.1.10-4e3e198.aar')
    implementation files('libs/EsignFaceSDK.aar')

    // refresh
    implementation  'io.github.scwang90:refresh-layout-kernel:2.1.0'
    implementation  'io.github.scwang90:refresh-header-classics:2.1.0'

    //toast
    implementation 'com.github.getActivity:Toaster:12.6'

    //banner
    implementation 'io.github.youth5201314:banner:2.2.3'
    implementation "org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.3"

    kapt Libs.glide4_compiler
    kapt Libs.databinding
}
repositories {
    mavenCentral()
}