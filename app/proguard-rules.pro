# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in C:\Users\<USER>\AppData\Local\Android\Sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
#-assumenosideeffects class android.util.Log {
#    public static *** d(...);
#    public static *** v(...);
#    public static *** i(...);
#    public static *** e(...);
#}

#noinspection ShrinkerUnresolvedReference

#混淆时采用的算法
#-optimizations !code/simplification/cast,!field/*,!class/merging/*
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-keepattributes SourceFile,LineNumberTable

# Preserve some attributes that may be required for reflection.
-keepattributes *Annotation*,Signature,InnerClasses,EnclosingMethod

#---------------------------------默认保留区---------------------------------
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class * extends android.view.View
-keep public class * extends android.widget.PopupWindow
-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService
-keep class android.support.** { *; }
-keep interface android.support.** { *; }
-dontnote android.support.**
-dontwarn android.support.**

# For native methods, see http://proguard.sourceforge.net/manual/examples.html#native
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
    public <init>(android.content.Context);
        public <init>(android.content.Context, android.util.AttributeSet);
        public <init>(android.content.Context, android.util.AttributeSet, int);
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}
-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}
-keep class **.R$* {
 *;
}
-keepclassmembers class * {
    void *(**On*Event);
}
# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# Understand the @Keep support annotation.
-keep class androidx.annotation.Keep

-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

# For enumeration classes, see http://proguard.sourceforge.net/manual/examples.html#enumerations
-keepclassmembers enum * {  # 使用enum类型时需要注意避免以下两个方法混淆，因为enum类的特殊性，以下两个方法会被反射调用，
    public static **[] values();
    public static ** valueOf(java.lang.String);
}


# 对于带有回调函数的onXXEvent、**On*Listener的，不能被混淆
-keepclassmembers class * {
    void *(**On*Event);
    void *(**On*Listener);
}
#----------------------------------------------------------------------------

#---------------------------------webview------------------------------------
-keepclassmembers class fqcn.of.javascript.interface.for.Webview {
   public *;
}
-keepclassmembers class * extends android.webkit.WebViewClient {
    public void *(android.webkit.WebView, java.lang.String, android.graphics.Bitmap);
    public boolean *(android.webkit.WebView, java.lang.String);
}

-keep public class * extends android.app.Activity
-keep public class * extends android.app.Appliction
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class * extends android.view.View
-keep public class * extends android.widget.PopupWindow

-keep class com.shile.gancaoyuan.doctor.adapter.**{*;}

-keep class com.shile.gancaoyuan.doctor.base.**{*;}
-keep class com.shile.gancaoyuan.doctor.bean.**{*;}
-keep class com.shile.gancaoyuan.doctor.fragment.**{*;}
-keep class com.shile.gancaoyuan.doctor.views.popup.**{*;}
-keep class com.shile.gancaoyuan.doctor.common.service.**{*;}
-keep class com.shile.gancaoyuan.doctor.views.**{*;}


-keep class com.nineoldandroids.** { *; }

-dontwarn javax.inject.**
################OkHttp3################
-dontwarn com.squareup.okhttp3.**
-keep class com.squareup.okhttp3.** { *;}

# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# OkHttp platform used only on JVM and when Conscrypt dependency is available.
-dontwarn okhttp3.internal.platform.ConscryptPlatform
################okio################
-dontwarn okio.**
-keep class okio.**{*;}
-keep interface okio.**{*;}



################retrofit###############

# Retain service method parameters when optimizing.
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

# Ignore annotation used for build tooling.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Ignore JSR 305 annotations for embedding nullability information.
-dontwarn javax.annotation.**

-dontwarn retrofit2.**
-keep class retrofit2.** { *; }


# Retrofit does reflection on generic parameters. InnerClasses is required to use Signature and
# EnclosingMethod is required to use InnerClasses.
-keepattributes Signature, InnerClasses, EnclosingMethod

# Retrofit does reflection on method and parameter annotations.
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations

# Keep annotation default values (e.g., retrofit2.http.Field.encoded).
-keepattributes AnnotationDefault

# Retain service method parameters when optimizing.
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

# Ignore annotation used for build tooling.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Ignore JSR 305 annotations for embedding nullability information.
-dontwarn javax.annotation.**

# Guarded by a NoClassDefFoundError try/catch and only used when on the classpath.
-dontwarn kotlin.Unit

# Top-level functions that can only be used by Kotlin.
-dontwarn retrofit2.KotlinExtensions

# With R8 full mode, it sees no subtypes of Retrofit interfaces since they are created with a Proxy
# and replaces all potential values with null. Explicitly keeping the interfaces prevents this.
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface <1>

# Keep generic signature of Call, Response (R8 full mode strips signatures from non-kept items).
-keep,allowobfuscation,allowshrinking interface retrofit2.Call
-keep,allowobfuscation,allowshrinking class retrofit2.Response

# With R8 full mode generic signatures are stripped for classes that are not
# kept. Suspend functions are wrapped in continuations where the type argument
# is used.
-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

################retrofit  end ###############

################glide###############
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class com.bumptech.glide.** { *; }
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
    **[] $VALUES;
    public *;
}
################gson###############
-keep class sun.misc.Unsafe { *; }
#-keep class com.google.gson.stream.** { *; }
-keep class com.google.gson.** { *; }
# Application classes that will be serialized/deserialized over Gson
-keep class com.sunloto.shandong.bean.** { *; }

#-keep class com.john.testlog.** {*;}
-keep class org.joda.time.** {*;}
-keep class org.apache.commons.lang3.** {*;}
-keep class org.apache.http.entity.mime.** {*;}
#-keep class com.jwdev.library.** {*;}
#-dontwarn com.aft.fastrepair.data.repository.**
-dontwarn org.joda.time.**
-dontwarn com.jwdev.library.**
-dontwarn com.rey.material.**
-dontwarn com.ta.utdid2.**
-dontwarn org.apache.http.**

-keep class android.support.** {*;}
-keep class com.android.support.test.** {*;}

-keep class org.aspectj.** {*;}
-keep class de.hdodenhof.** {*;}
-keep class dagger.** {*;}
-keep class com.google.dexmaker.** {*;}
-keep class com.fourmob.datetimepicker.** {*;}
-keep class com.sleepbot.datetimepicker.** {*;}
-keep class com.wefika.flowlayout.** {*;}
-keep class org.hamcrest.** {*;}
-keep class javax.** {*;}
-keep class fr.tvbarthel.lib.blurdialogfragment.** {*;}
-keep class com.rengwuxian.materialedittext.** {*;}
-keep class com.nineoldandroids.** {*;}
-keep class com.rey.material.** {*;}
-keep class org.mockito.** {*;}
-keep class org.objenesis.** {*;}

-dontwarn okio.**
-dontwarn com.squareup.**
-keep class okio.** {*;}
-keep class com.squareup.okhttp.** {*;}
-keep interface com.squareup.okhttp.** {*;}
#-keep class com.squareup.okhttp.internal.** {*;}
-keep class com.squareup.otto.** {*;}
-keep class com.squareup.phrase.** {*;}

##---------------Begin: proguard configuration for Gson  ----------
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
-keep class sun.misc.Unsafe {*;}
#-keep class com.google.gson.** {*;}
#-keep class com.google.gson.stream.** {*;}

-keep class com.facebook.stetho.**{ *; }

# Glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
    **[] $VALUES;
    public *;
}
# nineoldandroids
-keep interface com.nineoldandroids.view.** { *; }
-dontwarn com.nineoldandroids.**
-keep class com.nineoldandroids.** { *; }

# support-design
-dontwarn android.support.design.**
-keep class android.support.design.** { *; }
-keep interface android.support.design.** { *; }
-keep public class android.support.design.R$* { *; }

#保持自定义控件类不被混淆
-keepclasseswithmembers class * {
   public <init>(android.content.Context, android.util.AttributeSet);
}

#PictureSelector
-keep class com.luck.picture.lib.** { *; }
#// 如果引入了Camerax库请添加混淆
-keep class com.luck.lib.camerax.** { *; }

#// 如果引入了Ucrop库请添加混淆
-dontwarn com.yalantis.ucrop**
-keep class com.yalantis.ucrop** { *; }
-keep interface com.yalantis.ucrop** { *; }

#AWS S3
# Class names are needed in reflection
-keepnames class com.amazonaws.**
-keepnames class com.amazon.**

# Enums are not obfuscated correctly in combination with Gson
-keepclassmembers enum * { *; }

# Request handlers defined in request.handlers
-keep class com.amazonaws.services.**.*Handler

# The following are referenced but aren't required to run
-dontwarn com.fasterxml.jackson.**

# Android 6.0 release removes support for the Apache HTTP client
-dontwarn org.apache.http.**

# The SDK has several references of Apache HTTP client
-dontwarn com.amazonaws.http.**
-dontwarn com.amazonaws.metrics.**

# AGP 8 enables R8 full-mode optimization, which will remove constructors of classes that are only
# instantiated via reflection. These classes are instantiated via reflection in the SignerFactory.
-keep class com.amazonaws.auth.AWS4Signer { *; }
-keep class com.amazonaws.auth.QueryStringSigner { *; }
-keep class com.amazonaws.auth.NoOpSigner { *; }

# Face Live
-dontwarn com.dfsdk.**
-keep class com.dfsdk.** {*;}

# Toast
-keep class com.hjq.toast.** {*;}

#实体类跳过混淆
-keep class com.fenqi.main.bean.** { *; }
-keep class com.fenqi.request.** { *; }


#Serialization
# Keep `Companion` object fields of serializable classes.
# This avoids serializer lookup through `getDeclaredClasses` as done for named companion objects.
-if @kotlinx.serialization.Serializable class **
-keepclassmembers class <1> {
    static <1>$Companion Companion;
}

# Keep `serializer()` on companion objects (both default and named) of serializable classes.
-if @kotlinx.serialization.Serializable class ** {
    static **$* *;
}
-keepclassmembers class <2>$<3> {
    kotlinx.serialization.KSerializer serializer(...);
}

# Keep `INSTANCE.serializer()` of serializable objects.
-if @kotlinx.serialization.Serializable class ** {
    public static ** INSTANCE;
}
-keepclassmembers class <1> {
    public static <1> INSTANCE;
    kotlinx.serialization.KSerializer serializer(...);
}

# @Serializable and @Polymorphic are used at runtime for polymorphic serialization.
-keepattributes RuntimeVisibleAnnotations,AnnotationDefault

# Don't print notes about potential mistakes or omissions in the configuration for kotlinx-serialization classes
# See also https://github.com/Kotlin/kotlinx.serialization/issues/1900
-dontnote kotlinx.serialization.**

# Serialization core uses `java.lang.ClassValue` for caching inside these specified classes.
# If there is no `java.lang.ClassValue` (for example, in Android), then R8/ProGuard will print a warning.
# However, since in this case they will not be used, we can disable these warnings
-dontwarn kotlinx.serialization.internal.ClassValueReferences

# disable optimisation for descriptor field because in some versions of ProGuard, optimization generates incorrect bytecode that causes a verification error
# see https://github.com/Kotlin/kotlinx.serialization/issues/2719
-keepclassmembers public class **$$serializer {
    private ** descriptor;
}
#Serialization end

#实体类跳过混淆
-keep class com.fenqi.main.postentity.*
-keep class com.fenqi.main.postentity.** { *; }




# 指定混淆时采用的算法，后面的参数是一个过滤器
# 这个过滤器是谷歌推荐的算法，一般不改变
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*

# 抛出异常时保留代码行号
-keepattributes SourceFile,LineNumberTable
#不混淆内部类
-keepattributes InnerClasses

# 枚举类不能被混淆
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

#人脸识别sdk的混淆规则
-include kyc-cloud-face-consumer-proguard-rules.pro


######################云产品依赖的第三方库 混淆规则-BEGIN###########################

## support:appcompat-v7
-keep public class android.support.v7.widget.** { *; }
-keep public class android.support.v7.internal.widget.** { *; }
-keep public class android.support.v7.internal.view.menu.** { *; }

-keep public class * extends android.support.v4.view.ActionProvider {
    public <init>(android.content.Context);
}

##########################云产品依赖的第三方库 混淆规则-END##############################


#apk 包内所有 class 的内部结构
-dump class_files.txt
#未混淆的类和成员
-printseeds seeds.txt
#列出从 apk 中删除的代码
-printusage unused.txt

#不混淆内部类
-keepattributes InnerClasses

#不混淆jni调用类
-keepclasseswithmembers class *{
    native <methods>;
}

###################### faceverify-BEGIN ###########################
-ignorewarnings
-keep public class com.tencent.ytcommon.**{*;}

-keep class com.tencent.turingface.sdk.*.TNative$aa { public *; }
-keep class com.tencent.turingface.sdk.*.TNative$aa$bb { public *; }
-keep class com.tencent.turingcam.** {*;}

-keep public class com.tencent.youtu.ytagreflectlivecheck.jni.**{*;}
-keep public class com.tencent.youtu.ytagreflectlivecheck.YTAGReflectLiveCheckInterface{
    public <methods>;
}
-keep public class com.tencent.youtu.ytposedetect.jni.**{*;}
-keep public class com.tencent.youtu.ytposedetect.data.**{*;}
-keep public class com.tencent.youtu.liveness.YTDeviceInfo{*;}
-keep public class com.tencent.youtu.liveness.YTFaceTracker{*;}
-keep public class com.tencent.youtu.liveness.YTFaceTracker$*{*;}
-keep public class com.tencent.youtu.sdkkitframework.liveness.framework.YtSDKKitFrameworkTool{
   public *;
}
-keep public class com.tencent.youtu.sdkkitframework.liveness.common.YTImageData{
   *;
}
-keep public class com.tencent.cloud.huiyansdkface.facelight.net.*$*{
    *;
}
-keep public class com.tencent.cloud.huiyansdkface.facelight.net.**{
    *;
}
-keep public class com.tencent.cloud.huiyansdkface.facelight.provider.WbDeviceRiskInfo{
    public <fields>;
}
-keep public class com.tencent.cloud.huiyansdkface.facelight.provider.WbUiTips{
    *;
}

#================数据上报混淆规则 start===========================
#实体类
-keep class com.tencent.cloud.huiyansdkface.analytics.EventSender{
    *;
}
-keep class com.tencent.cloud.huiyansdkface.analytics.EventSender$*{
    *;
}
-keep class com.tencent.cloud.huiyansdkface.analytics.WBSAEvent{
     *;
}
-keep class com.tencent.cloud.huiyansdkface.analytics.WBSAParam{
     *;
}
#================数据上报混淆规则 end===========================

#######################faceverify-END#############################

####################### normal混淆规则-BEGIN#############################
#不混淆内部类
-keepattributes InnerClasses
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes Exceptions

-keep public class com.tencent.cloud.huiyansdkface.normal.net.*$*{
    *;
}
-keep public class com.tencent.cloud.huiyansdkface.normal.net.*{
    *;
}
#bugly
-keep class com.tencent.bugly.idasc.**{
    *;
}
#wehttp混淆规则
-dontwarn com.tencent.cloud.huiyansdkface.okio.**
-keep class com.tencent.cloud.huiyansdkface.okio.**{
    *;
}
-dontwarn com.tencent.cloud.huiyansdkface.okhttp3.OkHttpClient$Builder

####################### normal混淆规则-END#############################

##---------------Begin: proguard configuration for Gson  ----------
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
-keep class sun.misc.Unsafe { *; }
#-keep class com.google.gson.stream.** { *; }

# Application classes that will be serialized/deserialized over Gson
-keep class com.google.gson.examples.android.model.** { *; }
# Gson
-keep class com.google.gson.** { *; }
-keep class sun.misc.Unsafe { *; }
-keep class com.google.gson.stream.** { *; }

##---------------End: proguard configuration for Gson  ----------

# Retrofit
-keep class com.esign.retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions

# RxJava
-dontwarn rx.**
-keep class rx.schedulers.Schedulers {
    public static <methods>;
}
-keep class rx.schedulers.ImmediateScheduler {
    public <methods>;
}
-keep class rx.internal.util.RxRunnable {
    public <methods>;
}

# RxAndroid
-keep class rx.android.schedulers.AndroidSchedulers {
    public static <methods>;
}
-keep class rx.android.schedulers.HandlerScheduler {
    public <methods>;
}
-keep class rx.android.schedulers.LooperScheduler {
    public <methods>;
}

# OkHttp
-keep class com.esign.okhttp3.** { *; }
-keep interface com.esign.okhttp3.** { *; }
-dontwarn com.esign.okhttp3.**

-keep public class con.esign.esignsdk.R$*{
public static final int *;
}

-keep class cn.esign.base.base.**{*;}
-keep public class * extends android.app.Activity{
	public <fields>;
	public <methods>;
}
-keep class com.esign.base.net.RetrofitManager { *; }
-keep class com.esign.esignsdk.data.** { *; }
-keep class com.google.gson.stream.** { *; }




-keep class com.esign.base.base.BaseActivity {
    *;
}



# Application classes that will be serialized/deserialized over Gson 下面替换成自己的实体类

-keep class com.esign.facesdk.EsignSdk {
 public static int REQUEST_CODE_H5;
 public static com.esign.facesdk.EsignSdk getInstance();
 public void init(android.app.Activity, com.esign.facesdk.data.Config,com.esign.facesdk.EsignSDK.InitCallback);
 public void faceVerify(java.lang.String, com.esign.facesdk.EsignSDK.FaceVerifyCallback);
}
-keep class com.esign.facesdk.data.** { *; }
-keep class com.esign.facesdk.net.request.** {*;}
-keep class com.esign.facesdk.net.response.** {*;}
-keep class com.esign.facesdk.net.BaseDTO {*;}

-dontwarn sun.misc.**

-keepclassmembers class rx.internal.util.unsafe.*ArrayQueue*Field* {
   long producerIndex;
   long consumerIndex;
}

-keepclassmembers class rx.internal.util.unsafe.BaseLinkedQueueProducerNodeRef {
    rx.internal.util.atomic.LinkedQueueNode producerNode;
}

-keepclassmembers class rx.internal.util.unsafe.BaseLinkedQueueConsumerNodeRef {
    rx.internal.util.atomic.LinkedQueueNode consumerNode;
}

-dontnote rx.internal.util.PlatformDependent

-keep class io.reactivex.** { *; }
-keep class retrofit.** { *; }
-keep class com.squareup.** { *; }
-keep class rx.** { *; }
-keepclassmembers class * implements io.reactivex.annotations.Nullable { *; }
-keepclassmembers class * extends io.reactivex.annotations.Nullable { *; }
-keepclassmembers class * implements io.reactivex.annotations.CheckReturnValue { *; }
-keepclassmembers class * extends io.reactivex.annotations.CheckReturnValue { *; }
-keepclassmembers class * implements io.reactivex.functions.Function { *; }
-keepclassmembers class * extends io.reactivex.functions.Function { *; }
-keepclassmembers class * implements io.reactivex.functions.Consumer { *; }
-keepclassmembers class * extends io.reactivex.functions.Consumer { *; }
-keepclassmembers class * extends io.reactivex.android.** { *; }
