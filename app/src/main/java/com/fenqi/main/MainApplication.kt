package com.fenqi.main

import android.app.Application
import android.view.Gravity
import com.fenqi.main.constant.CommonData
import com.hjq.toast.Toaster

public class MainApplication: Application() {

    companion object{
        var application:MainApplication? = null

        @Synchronized
        public fun getInstance(): MainApplication? {
            return application
        }
    }

    override fun onCreate() {
        super.onCreate()
        application = this
        Toaster.init(this)
    }
}
