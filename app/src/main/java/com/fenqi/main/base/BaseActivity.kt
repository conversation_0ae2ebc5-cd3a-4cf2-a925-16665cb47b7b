package com.fenqi.main.base

import android.annotation.SuppressLint
import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.databinding.DataBindingUtil.setContentView
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import com.fenqi.main.presenter.base.MvvmView
import com.fenqi.platformtools.customerui.LoadingView
import com.fenqi.platformtools.utils.ActivityManager
import com.fenqi.platformtools.utils.TitleBar
import com.fenqi.main.R
import com.hjq.toast.Toaster

import org.greenrobot.eventbus.EventBus

abstract class BaseActivity<API,V:MvvmView, VM : BaseViewModel<API,V>, DB> :AppCompatActivity(),
  MvvmView,LifecycleOwner{

  companion object{
    const val RECEIVER_REFRESH_API_SERVICE = "RECEIVER_REFRESH_API_SERVICE"
  }

  protected var activity:Activity = this
  protected var dataBinding:DB?=null
  protected var viewModel:VM? = null
  protected var eventBus: EventBus = EventBus.getDefault()
  var loadingView:LoadingView = LoadingView(this)

  protected abstract fun onShowViewById():Int

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    ActivityManager.getActivityManager().addActivity(this)
    if(attachTitleText() == null || attachTitleText() == ""){
      dataBinding = setContentView(this, onShowViewById())
    } else{
      setTitleModel()
    }

    val viewModelClass = this.attachViewModel()
    viewModel = ViewModelProvider(this, ViewModelProvider.AndroidViewModelFactory(application)).get(
      viewModelClass
    )

    viewModel?.bindModel(this,this,attachMvvmView())

    loadingView.initDialog()

    register()

    initTitleBar()

    kinit()
  }

  abstract fun kinit()

  protected abstract fun attachViewModel():Class<VM>
  protected abstract fun attachMvvmView():V

  fun bindTargetApiService(){
    viewModel?.bindTargetApiService()
  }

  @SuppressLint("InflateParams") fun setTitleModel(){
    val viewGroup = LayoutInflater.from(this).inflate(R.layout.layout_base_container,null)
    val frameLayout:FrameLayout = viewGroup.findViewById(R.id.base_container_framelayout)

    val textViewTitle:TextView = viewGroup.findViewById(R.id.text_base_title)
    val relateArrow:RelativeLayout = viewGroup.findViewById(R.id.view_base_title_return_back)
    textViewTitle.text = attachTitleText()
    relateArrow.setOnClickListener {
      finish()
    }

    val viewActivity = LayoutInflater.from(this).inflate(onShowViewById(),null)

    dataBinding = DataBindingUtil.bind(viewActivity)

    frameLayout.addView(viewActivity)
    setContentView(viewGroup)
  }

  abstract fun attachTitleText():String?

  private fun initTitleBar(){
    setTitleBarColor(R.color.color_white)
    TitleBar.statusBarLMode(this)
  }

  @SuppressLint("ObsoleteSdkInt")
  private fun setTitleBarColor(colorId: Int) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M
      ) {
        val activityWindowConfig = window
        activityWindowConfig.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        activityWindowConfig.statusBarColor = ContextCompat.getColor(this, colorId)
      } else {
        val activityWindowConfig = window
        activityWindowConfig.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        activityWindowConfig.statusBarColor =ContextCompat.getColor(this, R.color.color_white)
      }
    }
  }

  fun showToast(toast: String){
    Toaster.show(toast)
  }
  fun showToastShort(toast: String){
    Toaster.showShort(toast)
  }

  fun showLoading(message: String, cancel: Boolean){
    if(activity.isFinishing || activity.isDestroyed){
      return
    }
    loadingView.showDialog(message, cancel)
  }
  fun showLoading(message: String){
    if(activity.isFinishing || activity.isDestroyed){
      return
    }
    loadingView.showDialog(message)
  }
  fun showLoading(cancel: Boolean){
    if(activity.isFinishing || activity.isDestroyed){
      return
    }
    loadingView.showDialog(cancel)
  }

  fun showLoading(){
    if(activity.isFinishing || activity.isDestroyed){
      return
    }
    loadingView.showDialog()
  }

  fun dismissLoading(){
    loadingView.dismissDialog()
  }

  private var receiver:RefreshApiReceiver?=null

  private fun register() {
    val intentFilter= IntentFilter()
    intentFilter.addAction(RECEIVER_REFRESH_API_SERVICE)
    receiver=RefreshApiReceiver()
    registerReceiver(receiver,intentFilter)
  }

  inner class RefreshApiReceiver : BroadcastReceiver(){
    override fun onReceive(context: Context?, intent: Intent?) {
      bindTargetApiService()
    }
  }

  override fun onDestroy() {
    super.onDestroy()
    unregisterReceiver(receiver)
  }
}