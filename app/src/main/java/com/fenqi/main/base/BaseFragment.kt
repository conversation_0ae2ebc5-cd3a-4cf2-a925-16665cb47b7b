package com.fenqi.main.base

import android.app.Activity
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.ViewModelProvider
import com.fenqi.main.presenter.base.MvvmView
import com.fenqi.platformtools.customerui.LoadingView
import com.hjq.toast.Toaster

import org.greenrobot.eventbus.EventBus

abstract class BaseFragment<API,V:MvvmView,VM : BaseViewModel<API,V>, DB:ViewDataBinding>: Fragment(),
    MvvmView,
  LifecycleOwner {

  protected var activity: Activity?=null
  protected var lifeCycleRegister = LifecycleRegistry(this)
  protected var dataBinding:DB?=null
  protected var viewModel:VM? = null
  protected var eventBus: EventBus = EventBus.getDefault()
  var loadingView: LoadingView? = null

  override fun onCreateView(
    inflater: LayoutInflater,
    container: ViewGroup?,
    savedInstanceState: Bundle?
  ): View? {
    dataBinding = DataBindingUtil.inflate(inflater,onShowViewById(),container,false)
    return dataBinding?.root
  }

  override fun onActivityCreated(savedInstanceState: Bundle?) {
    super.onActivityCreated(savedInstanceState)

    lifeCycleRegister.handleLifecycleEvent(Lifecycle.Event.ON_START)
    activity = requireActivity()

    loadingView = LoadingView(activity)
    loadingView?.initDialog()

    viewModel = ViewModelProvider(this, ViewModelProvider.AndroidViewModelFactory(activity?.application!!)).get(
      this.attachViewModel()
    )

    viewModel?.bindModel(getActivity() as AppCompatActivity,this,attachMvvmView())

    kfragmentInit()
  }

  abstract fun onShowViewById():Int

  abstract fun kfragmentInit()

  protected abstract fun attachViewModel():Class<VM>
  protected abstract fun attachMvvmView():V

  fun showToast(toast: String){
    Toaster.show(toast)
  }
  fun showToastShort(toast: String){
    Toaster.showShort(toast)
  }

  fun showLoading(message: String, cancel: Boolean){
    activity?.let {
      if(it.isFinishing || it.isDestroyed){
        return
      }
      loadingView?.showDialog(message, cancel)
    }
  }
  fun showLoading(message: String){
    activity?.let {
      if(it.isFinishing || it.isDestroyed){
        return
      }
      if(loadingView!=null){
        loadingView?.showDialog(message)
      }
    }
  }
  fun showLoading(cancel: Boolean){
    activity?.let {
      if(it.isFinishing || it.isDestroyed){
        return
      }
      if(loadingView!=null){
        loadingView?.showDialog(cancel)
      }
    }
  }
  fun showLoading(){
    activity?.let {
      if(it.isFinishing || it.isDestroyed){
        return
      }
      if(loadingView!=null){
        loadingView?.showDialog()
      }
    }
  }
  fun dismissLoading(){
    activity?.let {
      if(it.isFinishing || it.isDestroyed){
        return
      }
      if(loadingView!=null){
        loadingView?.dismissDialog()
      }
    }
  }

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    lifeCycleRegister.handleLifecycleEvent(Lifecycle.Event.ON_CREATE)
  }

  override fun onResume() {
    super.onResume()
    lifeCycleRegister.handleLifecycleEvent(Lifecycle.Event.ON_RESUME)
  }

  override fun onPause() {
    super.onPause()
    lifeCycleRegister.handleLifecycleEvent(Lifecycle.Event.ON_PAUSE)
  }

  override fun onDestroy() {
    super.onDestroy()
    lifeCycleRegister.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    loadingView = null
  }

}