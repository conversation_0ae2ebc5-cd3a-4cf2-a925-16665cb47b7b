package com.fenqi.main.base

import com.fenqi.main.R

data class BaseTitleData(var _backType:Int, var titleText: String?){

  var backType = _backType

  var backResource:Int = R.drawable.arrow_left_black
    get() = when(backType){
      NORMAL_BACK -> R.drawable.arrow_left_black
      else -> R.drawable.arrow_left_black
    }
    set(value) {
      field = value
    }

  companion object{
    const val NORMAL_BACK = 1
  }
}