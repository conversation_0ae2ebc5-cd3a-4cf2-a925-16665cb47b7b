package com.fenqi.main.base

import android.annotation.SuppressLint
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import com.fenqi.main.presenter.base.MvvmView
import kotlinx.serialization.json.Json

abstract class BaseViewModel<API,V:MvvmView>: ViewModel() {

    @SuppressLint("StaticFieldLeak")
    protected var activity: AppCompatActivity?=null

    protected var lifecycleOwner:LifecycleOwner?=null

    protected var mService:API?=null

    protected var mvvmView:V?=null

    protected val json = Json{
        ignoreUnknownKeys = true
    }

    public fun bindModel(_activity: AppCompatActivity,_lifecycleOwner: LifecycleOwner,_mvvmView:V){
        activity = _activity
        lifecycleOwner = _lifecycleOwner
        mService = bindService()
        mvvmView = _mvvmView
    }

    abstract fun bindService():API

    public fun bindTargetApiService(){
        mService = bindService()
    }
}