package com.fenqi.main.bean

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class AppCodeConfigEntity(
    var password: Int = 0,
    var prepareWrap: Int = 0,
    var fbId: String? = "",
    var murder: String? = "",
    var substitutionSplashWood: Boolean = false,
    var forceUpdate: Boolean = false,
    var fileUploadType: Int = 0,
    var fbUrl: String? = "",
    var calendar: String? = "",
    var hasFactorsCompress: Boolean = false,
    var seniorNoteHere: Int = 0,
    var middleLanguage: Int = 0,
    var guestId: String? = "",
    var therefore: String? = "",
    var apkHash: String? = "",
    var showingCapImmigrant: Int = 0,
    var smokeFighterEscape: Boolean = false,
    val customerServiceUrl: String? = "",
    var exceedTwice: Boolean = false,
    var library: Boolean = false,
    var fbToken: String? = "",
    var laterDeepSafe: Int = 0,
    var reviewUrl: String? = "",
    var properlyAlphabetically: String? = "",
    var forceUpdateUrl: String? = "",
    var unformattedMembershipArchive: String? = "",
    var adviceOddRush: Boolean = false,
    var forceUpdateDesc: String? = "",
    var scream: Boolean = false,
    var autoWeekendMr: Boolean = false,
    var appCode: String? = "",
    var apkVersion: String? = "",
    var picture: String? = "",
    var woodCpu: Int = 0,
)
