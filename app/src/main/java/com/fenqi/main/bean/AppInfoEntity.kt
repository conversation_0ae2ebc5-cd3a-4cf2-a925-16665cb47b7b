package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class AppInfoEntity(
    var entirelyWine: String? = "",
    var unfortunatelySeveral: Boolean = false,
    var get: String? = "",
    var appPackageName: String? = "",
    var entirelySayMarriage: Int = 0,
    var asynchronousComeAbroad: String? = "",
    var ingredientWhicheverExclamation: Boolean = false,
    var pureOfficeFeeling: Int = 0,
    var pageSimilarlyClipper: Boolean = false,
    var restriction: String? = "",
    var join: Boolean = false,
    var lifetime: Int = 0,
    var attitudeIncompatibleMorning: String? = "",
    var organicApply: Boolean = false,
    var firstInstallTime: String? = "",
    var passage: String? = "",
    var indianSeparatelySolution: Int = 0,
    var affectedWorkshopPart: Boolean = false,
    var versionName: String? = "",
    var appName: String? = "",
    var numerousInitiate: Boolean = false,
    var widespreadRunning: Int = 0,
    var printTwice: String? = "",
    var lastUpdateTime: String? = "",
)
