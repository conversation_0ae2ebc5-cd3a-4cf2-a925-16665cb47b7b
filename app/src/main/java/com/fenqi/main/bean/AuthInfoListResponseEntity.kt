package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class AuthInfoListResponseEntity(
    var worthPossibleInsert: Int = 0,
    var nextJumpUrl: String? = "",
    var whateverHairReplace: Int = 0,
    var warQuickly: String? = "",
    var favorite: String? = "",
    var printoutRandomHighly: Boolean = false,
    var maxMoney: String? = "0",
    var suitableWinner: Boolean = false,
    var child: Int = 0,
    var prosecutorDozenOn: String? = "",
    var smellClient: Boolean = false,
    var equivalentMerelyIntellectual: Int = 0,
    var consultOddsFunding: Int = 0,
    var country: String? = "",
    var stabilityTablespoonHandle: String? = "",
    var returnConfirmTips: String? = "",
    var individualExtend: Int = 0,
    var infoDegree: Int = 0,
    var maintenanceLock: Int = 0,
)
