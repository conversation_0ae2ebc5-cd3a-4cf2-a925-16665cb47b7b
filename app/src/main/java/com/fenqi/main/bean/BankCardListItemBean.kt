package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class BankCardListItemBean(
    var airlineBugAsk: String? = "",
    var considerableScreenAssistant: Boolean = false,
    var bankLogo: String? = "",
    var defaultCard: Boolean = false,
    var okSpinYellow: String? = "",
    var accidentallyWay: Int = 0,
    var bankCardType: String? = "",
    var andTeenagerUtility: Int = 0,
    var proceedReactNews: Boolean = false,
    var interactive: Int = 0,
    var statusItem: Int = 0,
    var confirmRural: String? = "",
    var bindId: String? = "",
    var accuse: Int = 0,
    var holderName: String? = "",
    var phone: String? = "",
    var pregnantSureSomething: Int = 0,
    var studentFiftyReserved: String? = "",
    var maintenanceSky: Int = 0,
    var bankCode: String? = "",
    var analystCelebrationDad: Int = 0,
    var withdrawTaxpayerFurthermore: Boolean = false,
    var honey: Boolean = false,
    var check:Boolean = false,
    var bankName: String? = "",
    var cardNo: String? = "",
)
