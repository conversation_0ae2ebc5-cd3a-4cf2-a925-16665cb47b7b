package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class BankCardListResponseBean(
    var tape: Boolean = false,
    var serviceUrl: String? = "",
    var showUrl: String? = "",
    var whichPaper: String? = "",
    var get: Int = 0,
    var bankRoList: MutableList<BankCardListItemBean>? = mutableListOf(),
    var realize: Boolean = false,
    var tailDescribe: Int = 0,
    var characterize: Int = 0,
    var soundBlast: Boolean = false,
    var journalRiceAdd: String? = "",
    var privateUrl: String? = "",
    var potential: Boolean = false,
    var diseaseTransformBlind: Int = 0,
    var numeralFighting: String? = "",
    var exerciseNormallyAttach: String? = "",
    var second: Int = 0,
    var sakeFollowing: Int = 0,
    var desBriefIncluding: Int = 0,
    var justice: Boolean = false,
    var multiprocessingSpreadCollection: Int = 0,
    var manufactureOfficialProbably: String? = "",
)
