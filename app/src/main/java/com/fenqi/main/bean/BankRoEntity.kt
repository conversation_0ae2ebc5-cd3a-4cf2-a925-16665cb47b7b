package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class BankRoEntity(
    var essay: Boolean = false,
    var hat: String? = "",
    var mixed: Boolean = false,
    var get: Int = 0,
    var bankCode: String? = "",
    var errorIndependently: String? = "",
    var allowed: Int = 0,
    var indirectlyBeauty: Boolean = false,
    var bankType: String? = "",
    var organization: Boolean = false,
    var chunk: String? = "",
    var especiallyGreatly: Boolean = false,
    var bankName: String? = "",
    var awayHi: Int = 0,
    var agree: String? = "",
    var touchJuniorStandard: Int = 0,
    var beginGalaxySteps: Boolean = false,
    var brownShort: Int = 0,
    var legendWeapon: Boolean = false,
    var mealThereforeScience: Int = 0,
    var bankLogo: String? = "",
    var programmable: String? = "",
    var raise: Int = 0,
)
