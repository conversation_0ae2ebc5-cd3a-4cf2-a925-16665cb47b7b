package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class BankSupportListBean(
    var solutionAttorney: Boolean = false,
    var interpretable: Boolean = false,
    var vitalFit: Boolean = false,
    var gayAlign: Boolean = false,
    var commaAh: String? = "",
    var his: Boolean = false,
    var administrator: String? = "",
    var sunScanDesigner: String? = "",
    var loadVictimStand: Int = 0,
    var admireBeautiful: String? = "",
    var directlyValleyCreation: String? = "",
    var yourselfSophisticatedTransition: String? = "",
    var wire: Boolean = false,
    var indicateSpendInvolvement: Int = 0,
    var anytimeAmerican: String? = "",
    var productionNetworkLaunch: Boolean = false,
    var container: String? = "",
    var rearrangeSystemProgrammable: String? = "",
    var bankRoList: MutableList<CardBinResponseBean>? = mutableListOf(),
)
