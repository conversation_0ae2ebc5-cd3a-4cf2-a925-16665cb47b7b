package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class BindCardResendSmsResponseBean(
    var structuralArea: Int = 0,
    var participantWindow: Boolean = false,
    var shieldProducerAnnounce: Int = 0,
    var adviser: Boolean = false,
    var roomArea: Boolean = false,
    var flowId: String? = "",
    var errorMsg: String? = "",
    var scatteredObserved: Int = 0,
    var movementTen: String? = "",
    var loggedQuestion: Boolean = false,
    var european: String? = "",
    var fortune: Boolean = false,
    var positiveHonorEliminate: Int = 0,
    var exponentialOperate: Boolean = false,
    var errorCode: String? = "",
    var delaySell: String? = "",
    var stockMention: Boolean = false,
    var centuryFont: String? = "",
)
