package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class BindCardResponseBean(
    var illegalZonePresent: Int = 0,
    var causeSunWay: Boolean = false,
    var religion: String? = "",
    var errorMsg: String? = "",
    var customizeAnimalMind: String? = "",
    var signStatus: String? = "",
    var runLessonBond: Boolean = false,
    var materialReturnNest: String? = "",
    var flowId: String? = "",
    var typical: String? = "",
    var smsNum: Int = 0,
    var whatever: Boolean = false,
    var separate: String? = "",
    var errorCode: String? = "",
    var layerNumeralStructure: String? = "",
    var halfwayBunchVarious: String? = "",
    var sceneUnpackRelevant: String? = "",
    var joy: String? = "",
    var infinite: Boolean = false,
    var viewHope: Int = 0,
)
