package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class BorrowAgainBean(
    var criminalSuppose: Int = 0,
    var major: Boolean = false,
    var realLow: Boolean = false,
    var insuranceOwn: Int = 0,
    var refugeeSlight: String? = "",
    var logarithmConsiderationLoading: Boolean = false,
    var scoreAchieveChild: Int = 0,
    var threat: Int = 0,
    var jumpUrl: String? = "",
    var curious: Boolean = false,
    var programmerHe: Boolean = false,
    var colonial: Int = 0,
    var screenIntentionOutside: Boolean = false,
    var internationalChargeBelieve: String? = "",
    var soilItselfAnother: Int = 0,
    var defendant: Int = 0,
    var kneeBar: Int = 0,
    var incorporate: Int = 0,
    var createMetalComputer: Boolean = false,
    var committee: Boolean = false,
    var nativeEconomistRequire: Boolean = false,
)
