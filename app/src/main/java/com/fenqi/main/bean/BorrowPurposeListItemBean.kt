package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class BorrowPurposeListItemBean(
    var extentSeemTransfer: String? = "",
    var wrongEdge: Boolean = false,
    var terminalJoyShortly: Int = 0,
    var relationResolve: Boolean = false,
    var accuracyDemocratDisplay: Boolean = false,
    var adapterPlant: String? = "",
    var dragExistence: Int = 0,
    var american: String? = "",
    var solution: Boolean = false,
    var request: Boolean = false,
    var summitNearbyResearcher: Int = 0,
    var insteadArea: Boolean = false,
    var desc: String? = "",
    var interestingFront: Boolean = false,
    var cloudVirtually: String? = "",
    var type: Int = 0,
    var several: Boolean = false,
    var nightBrick: Int = 0,
    var imply: String? = "",
)
