package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class BorrowStepVoItemBean(
    var sellInstructor: String? = "",
    var content: String? = "",
    var confirm: Int = 0,
    var activeInvalidNumeric: String? = "",
    var responsibilityExercise: Int = 0,
    var replaceableQuestionIntention: Int = 0,
    var objectiveAbandonFaith: Int = 0,
    var finished: Boolean = false,
    var glass: String? = "",
    var recallConfirm: String? = "",
    var trapConsumeChinese: String? = "",
    var absolutelyJew: String? = "",
    var pleasure: String? = "",
    var competitive: String? = "",
    var manual: Int = 0,
    var personalSecretary: Int = 0,
    var workingGuilty: Boolean = false,
    var safeEraExit: String? = "",
    var scaleLegacyTrace: Int = 0,
)
