package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class CardBinResponseBean(
    var regardlessChapter: Boolean = false,
    var bankLogo: String? = "",
    var writerVirtuallyAnger: Boolean = false,
    var pastEntirely: Int = 0,
    var enterpriseReservationHappen: String? = "",
    var riverUpdate: String? = "",
    var identifyWelfare: Boolean = false,
    var ago: String? = "",
    var incrementWhereas: Int = 0,
    var defective: Int = 0,
    var mostlySight: Boolean = false,
    var soBytePositive: Int = 0,
    var bankType: String? = "",
    var limitOfDay: String? = "",
    var limitOfBill: String? = "",
    var limitMinOfBill: String? = "",
    var aside: Int = 0,
    var lady: Int = 0,
    var fullPaymentHealthy: Int = 0,
    var attitudeOnly: String? = "",
    var percentage: Boolean = false,
    var bankName: String? = "",
    var rideDreamCase: String? = "",
    var bankCode: String? = "",
    var limitOfMonth: String? = "",
)
