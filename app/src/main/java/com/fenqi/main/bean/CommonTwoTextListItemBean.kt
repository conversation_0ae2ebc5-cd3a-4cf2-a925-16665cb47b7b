package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class CommonTwoTextListItemBean(
    var providerContest: String? = "",
    var bug: Int = 0,
    var transitionSoftwareSustain: Int = 0,
    var interpretInternational: Int = 0,
    var demonstration: Int = 0,
    var farmerExaminationRoot: Boolean = false,
    var randomPass: Boolean = false,
    var empty: Boolean = false,
    var undesirableVictim: Boolean = false,
    var psychologicalMrOverlook: Int = 0,
    var strokeOwn: Int = 0,
    var phoenixCitizen: Int = 0,
    var id: String? = "",
    var ease: Boolean = false,
    var raw: Boolean = false,
    var value: String? = "",
    var inviteColdBracket: String? = "",
    var day: Int = 0,
    var awareReasonWheel: Int = 0,
    var testimonyAssociateEmphasis: Int = 0,
    var figurePreviously: Int = 0,
    var statedBusyCarbon: Int = 0,
    var title: String? = "",
)
