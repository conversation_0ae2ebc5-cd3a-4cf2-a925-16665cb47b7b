package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class ContactEntity(
    var transportationLeadership: String? = "",
    var name: String? = "",
    var unlessSystem: String? = "",
    var whom: String? = "",
    var upper: String? = "",
    var society: String? = "",
    var marked: String? = "",
    var phone: String? = "",
    var agriculturalAppointProduction: Boolean = false,
    var claimLocatingPay: String? = "",
    var freshGuardUnmarked: Int = 0,
    var learnBroadProposal: Boolean = false,
    var andVisualFourscore: Int = 0,
    var waitingParticularlyCatholic: String? = "",
    var unnecessary: Boolean = false,
    var citeScale: String? = "",
    var expressionDrawingKiss: Boolean = false,
)
