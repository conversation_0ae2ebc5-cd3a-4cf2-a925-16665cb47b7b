package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class ContactUrlResponseBean(
    var flowId: String? = "",
    var openingBrotherTopic: Int = 0,
    var downtownSlashRemarkable: String? = "",
    var imply: Int = 0,
    var redefine: Boolean = false,
    var longUrl: String? = "",
    var inventEntry: Boolean = false,
    var traditionalEmulation: String? = "",
    var fileId: String? = "",
    var introductionEfficiency: String? = "",
    var comprehensive: String? = "",
    var respectively: String? = "",
    var signerId: String? = "",
    var howHeaderCorner: Boolean = false,
    var slide: Boolean = false,
    var artist: Boolean = false,
    var math: String? = "",
    var frequency: String? = "",
    var affectSee: Boolean = false,
    var codeAllow: Int = 0,
    var sake: Boolean = false,
    var orientationCrazeTobacco: String? = "",
    var two: String? = "",
    var errorMsg: String? = "",
    var carouselAlmost: Boolean = false,
    var shortUrl: String? = "",
)
