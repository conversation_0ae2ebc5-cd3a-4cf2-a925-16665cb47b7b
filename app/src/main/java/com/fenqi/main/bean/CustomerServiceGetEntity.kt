package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class CustomerServiceGetEntity(
    var worryCompileDisappear: Boolean = false,
    var volumeIncorporate: String? = "",
    var underPreviouslyRule: String? = "",
    var majoritySomewhereDesk: Int = 0,
    var depictSenateVia: Boolean = false,
    var hold: Int = 0,
    var identification: Boolean = false,
    var enoughRelation: String? = "",
    var quitMaintainPrepare: String? = "",
    var customerServiceUrl: String? = "",
    var fitInstitutionStupid: Boolean = false,
    var crackRatedEfficient: Int = 0,
    var sixRoot: Boolean = false,
    var occasionally: Boolean = false,
    var dimensionDepictWindow: Boolean = false,
    var governor: Boolean = false,
    var clearCenter: String? = "",
    var entertainment: Int = 0,
    var importancePot: Int = 0,
    var jobConsciousness: Int = 0,
    var tournamentSpellRoute: Int = 0,
)
