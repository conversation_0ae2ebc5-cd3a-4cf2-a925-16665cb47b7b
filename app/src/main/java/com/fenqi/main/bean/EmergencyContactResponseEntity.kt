package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class EmergencyContactResponseEntity(
    var decision: Int = 0,
    var finished: Boolean = false,
    var familiarizeFlushBeauty: Int = 0,
    var performQuicklyEveryone: Int = 0,
    var dutyAbsolutelyPeer: Int = 0,
    var readEvaluate: Int = 0,
    var cite: Int = 0,
    var soft: Boolean = false,
    var restrictHall: Boolean = false,
    var tapeHistory: Int = 0,
    var columnFit: String? = "",
    var entryRating: Boolean = false,
    var unitTaste: Boolean = false,
    var userInputInfoVOs: MutableList<UserInfoVoEntity>? = mutableListOf(),
    var absenceConcentrateSort: String? = "",
    var programmable: Int = 0,
    var safelySignalGovernment: Boolean = false,
    var stuffOutcome: Int = 0,
    var motifWhether: Boolean = false,
    var environmentConfirm: Boolean = false,
    var channelActionText: String? = "",
)
