package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class FaceAccountInfoEntity(
    var toneCycleColleague: Boolean = false,
    var utilityIncome: Int = 0,
    var groundQuick: Int = 0,
    var surprisinglyWhat: Boolean = false,
    var draftInfiniteClub: String? = "",
    var personalPhysical: String? = "",
    var edge: String? = "",
    var faceApi: String? = "",
    var nature: Boolean = false,
    var asciiReorganizationAllowed: Boolean = false,
    var subgroup: Boolean = false,
    var femaleSandDesign: String? = "",
    var cardApi: String? = "",
    var appId: String? = "",
    var appKey: String? = "",
    var masterFear: Boolean = false,
    var separatedDisplay: Boolean = false,
    var indexing: String? = "",
    var hiddenDistance: Int = 0,
    var virusPersonalVowel: String? = "",
    var tear: Boolean = false,
)
