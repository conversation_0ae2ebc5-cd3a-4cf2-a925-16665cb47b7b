package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class FaceCardConfigVoEntity(
    var hearAuthority: String? = "",
    var productShortlyCraft: String? = "",
    var plainAlter: Boolean = false,
    var samplePicPath: String? = "",
    var guaranteeDoubtThree: Int = 0,
    var correspondRecruit: String? = "",
    var bring: Int = 0,
    var rangeShelter: Boolean = false,
    var prompt: Boolean = false,
    var desc: String? = "",
    var scatter: String? = "",
    var lotCultural: Int = 0,
    var remove: Int = 0,
    var ossUrl: String? = "",
    var comeInstanceGap: Boolean = false,
    var extremelyTraditional: Int = 0,
    var tips: String? = "",
    var grainUnmarkedBatch: Int = 0,
    var regret: String? = "",
    var pipeReference: Int = 0,
    var key: String? = "",
    var stockContinue: String? = "",
    var samplePicUrl: String? = "",
    var examineBeforeInterpreter: Int = 0,
    var field: String? = "",
)
