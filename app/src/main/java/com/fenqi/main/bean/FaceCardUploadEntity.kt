package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class FaceCardUploadEntity(
    var consoleZeroScholar: Boolean = false,
    var consistTerminalSing: Boolean = false,
    var ansiMouth: String? = "",
    var museum: Boolean = false,
    var optionDirectlyTrigger: Int = 0,
    var concretePutTree: String? = "",
    var caseFoodSyntax: String? = "",
    var kycPictureUrl: String? = "",
    var exhibitMerely: Boolean = false,
    var maintainAbandonApprove: Int = 0,
    var saving: Boolean = false,
    var approvalOverrideDismiss: Boolean = false,
    var pictureType: Int = 0,
    var exponentUpper: Boolean = false,
    var lowerAmericanCluster: Int = 0,
    var securityEnhance: Boolean = false,
    var kycBackPictureUrl: String? = "",
    var carrierScreen: Int = 0,
    var lower: Int = 0,
    var repeatDeclineSomething: Int = 0,
)