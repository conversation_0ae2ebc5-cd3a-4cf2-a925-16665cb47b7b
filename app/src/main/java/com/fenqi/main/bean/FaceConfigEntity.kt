package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class FaceConfigEntity(
    var italian: Boolean = false,
    var whatever: Boolean = false,
    var assistanceUponSignificant: String? = "",
    var grandmotherAdvancedDeer: String? = "",
    var passBible: Int = 0,
    var organizationEsoteric: Int = 0,
    var fix: Int = 0,
    var enough: Boolean = false,
    var scientificProducer: String? = "",
    var numericalScope: String? = "",
    var supplyShineAdvance: Int = 0,
    var accountInfo: FaceAccountInfoEntity? = null,
    var unlikelyStudent: String? = "",
    var describeCropInformation: String? = "",
    var quiteResolutionProsecutor: String? = "",
    var exceptBenchLimiter: Boolean = false,
    var anotherStop: Int = 0,
    var faceType: Int = 0,
    var incredibleBag: Boolean = false,
)
