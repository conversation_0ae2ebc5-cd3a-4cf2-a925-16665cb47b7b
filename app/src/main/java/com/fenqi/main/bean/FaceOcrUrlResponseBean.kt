package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class FaceOcrUrlResponseBean(
    var validVary: String? = "",
    var currentlyTicketGentleman: String? = "",
    val bornDate: String? = "",
    var duty: Int = 0,
    val idCardInfo: IdCardInfo? = null,
    var agoSituationAssist: Int = 0,
    var animCallText: String? = "",
    var calling: String? = "",
    var on: Int = 0,
    var these: Int = 0,
    val success: Boolean = false,
    val detailId: Long = 0,
    val fullName: String? = "",
    var exponentParticularly: Int = 0,
    var riverEvolutionFall: Int = 0,
    var approachModelSupport: String? = "",
    var ruleJoin: String? = "",
    var barExpressionTrouble: Int = 0,
    var round: String? = "",
    var lose: String? = "",
    val gender: Int = 0,
    var floatOverridePhone: Boolean = false,
    var clauseYellCharacteristic: Boolean = false,
    var wait: Int = 0,
    val accountNumber: String? = "",
)

@Serializable
data class IdCardInfo(
    var validVary: String? = "",
    var currentlyTicketGentleman: String? = "",
    val bornDate: String? = "",
    var duty: Int = 0,
    val idCardInfo: IdCardInfo? = null,
    var agoSituationAssist: Int = 0,
    var animCallText: String? = "",
    var calling: String? = "",
    var on: Int = 0,
    var these: Int = 0,
    val success: Boolean = false,
    val detailId: Long = 0,
    val fullName: String? = "",
    var exponentParticularly: Int = 0,
    var riverEvolutionFall: Int = 0,
    var approachModelSupport: String? = "",
    var ruleJoin: String? = "",
    var barExpressionTrouble: Int = 0,
    var round: String? = "",
    var lose: String? = "",
    val gender: Int = 0,
    var floatOverridePhone: Boolean = false,
    var clauseYellCharacteristic: Boolean = false,
    var wait: Int = 0,
    val accountNumber: String? = "",
)