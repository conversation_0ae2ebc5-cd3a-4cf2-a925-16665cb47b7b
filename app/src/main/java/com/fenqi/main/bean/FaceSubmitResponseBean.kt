package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class FaceSubmitResponseBean(
    var flowId: String? = "",
    var thatSuccessiveChurch: Int = 0,
    var middleUnlockPoll: String? = "",
    var snapshotRealityControl: String? = "",
    var bombCollection: String? = "",
    var newlyAuth: String? = "",
    var authUrl: String? = "",
    var commercial: Int = 0,
    var nearbyNotice: Int = 0,
    var leagueUnique: Int = 0,
    var faceToken: String? = "",
    var organizationVirus: Boolean = false,
    var reenter: String? = "",
    var originallyPure: String? = "",
    var door: String? = "",
    var startingProtein: String? = "",
    var macrosDifferentiate: String? = "",
    var stillCeiling: String? = "",
    var operating: String? = "",
)
