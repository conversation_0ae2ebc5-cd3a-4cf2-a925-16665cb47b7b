package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class FileUploadResponseBean(
    var indefinitelySoughtPublic: String? = "",
    var snapshotBut: String? = "",
    var commitQuit: Int = 0,
    var enemyAcknowledgment: String? = "",
    var democracySurroundSir: Boolean = false,
    var switch: String? = "",
    var put: Int = 0,
    var optionUserCalculation: Boolean = false,
    var personnel: Int = 0,
    val filePath: String? = "",
    var youSection: String? = "",
    var involvementRespondent: Boolean = false,
    var otherwiseTransportationAssociate: Boolean = false,
    var reaction: Int = 0,
    var storeSoftPermit: Boolean = false,
    var floppySweep: String? = "",
    var wellEmergency: Int = 0,
    var next: String? = "",
    var iceBoom: Int = 0,
    var sure: String? = "",
)
