package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class LoginResponseEntity(
    var extension: Int = 0,
    var literallyProfessionalPenalty: String? = "",
    var recentHouse: String? = "",
    var thereforeBecomingAdvanced: Boolean = false,
    var turning: String? = "",
    var sigh: Int = 0,
    var attend: Boolean = false,
    var operationPortionLoad: Int = 0,
    var scope: Boolean = false,
    var differentlyOccupyPrior: String? = "",
    var syntax: Int = 0,
    var feel: Int = 0,
    var status: Int = 0,
    var token: String? = "",
    var holdingRepetitive: Int = 0,
    var out: Int = 0,
    var associate: String? = "",
    var preserve: String? = "",
    var aid: Boolean = false,
    var aggressive: Boolean = false,
)
