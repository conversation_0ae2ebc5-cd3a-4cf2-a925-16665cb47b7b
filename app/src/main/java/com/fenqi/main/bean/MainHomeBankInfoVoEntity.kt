package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class MainHomeBankInfoVoEntity(
    var physicianCouchPublic: Int = 0,
    var rich: String? = "",
    var code: String? = "",
    var directionReverse: Int = 0,
    var fairlyCapitalizedWar: Int = 0,
    var monitorViolationMovie: Int = 0,
    var dump: Boolean = false,
    var childhood: String? = "",
    var professional: Int = 0,
    var campDescend: Boolean = false,
    var streamTerminologyTerm: Boolean = false,
    var jacketResumeImportance: String? = "",
    var sequentially: String? = "",
    var precedenceLexical: String? = "",
    var dynamicComment: String? = "",
    var bankCardNo: String? = "",
    var bankName: String? = "",
    var compatibleVictimPot: Boolean = false,
    var time: String? = "",
    var proposedAutoSynchronization: String? = "",
)
