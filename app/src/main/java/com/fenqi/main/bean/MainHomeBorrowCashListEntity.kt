package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class MainHomeBorrowCashListEntity(
    var especially: Boolean = false,
    var ruralBackupPermit: Int = 0,
    var want: Boolean = false,
    var thesePressRepeatedly: String? = "",
    var intense: Boolean = false,
    var pale: String? = "",
    var amount: String? = "",
    var failThereafterInvite: Int = 0,
    var instruction: Int = 0,
    var participation: Int = 0,
    var trackCelebrationEscape: Boolean = false,
    var successfulShow: Int = 0,
    var uponRecordingMacro: String? = "",
    var block: String? = "",
    var than: Boolean = false,
    var borrowCashPeriods: MutableList<MainHomeBorrowCashPeriodsEntity>? = mutableListOf(),
    var currentLess: Boolean = false,
    var select: Boolean = false,
    var marketOverrideNegative: Boolean = false,
    var congratulationSuchAlready: String? = "",
    var pullDearDevelop: String? = "",
)