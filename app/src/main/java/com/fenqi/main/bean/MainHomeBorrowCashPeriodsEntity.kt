package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class MainHomeBorrowCashPeriodsEntity(
    var makeTerroristBypass: Int = 0,
    var serviceFee: String? = "",
    var respectCongress: Int = 0,
    var chapterRequest: Boolean = false,
    var vastInstallationSector: String? = "",
    var select: Boolean = false,
    var periodUnit: String? = "",
    var sheReceivedFantasy: Boolean = false,
    var modification: String? = "",
    var monthlyPayment: String? = "",
    var productCode: String? = "",
    var configurationProtectSeveral: Boolean = false,
    var interest: String? = "",
    var jobPoleEmpty: String? = "",
    var writePreserve: String? = "",
    var putPhysicallySpeed: Boolean = false,
    var trail: Boolean = false,
    var distanceBypass: Int = 0,
    var manageDistributionTitle: String? = "",
    var interestFee: String? = "",
    var jewishInvolvedAgo: Boolean = false,
    var period: Int = 0,
    var receivedAmount: String? = "",
    var automatic: Int = 0,
    var firstJuice: String? = "",
    var beingPayModule: Boolean = false,
    var troubleInvoke: Int = 0,
    var usageBusAgency: Boolean = false,
    var name: String? = "",
)
