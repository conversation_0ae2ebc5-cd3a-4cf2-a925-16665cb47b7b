package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class MainHomeBorrowPurposeListEntity(
    var thusVerifySubject: String? = "",
    var assignmentBuy: String? = "",
    var essentialTeachExplore: String? = "",
    var jewDirectoryEntry: String? = "",
    var storageFeedDifference: Int = 0,
    var unfortunatelyEssentiallyAccording: String? = "",
    var weatherPracticeConnect: String? = "",
    var desc: String? = "",
    var supply: Int = 0,
    var limitationSmallControversial: Int = 0,
    var finishArchitecture: String? = "",
    var bellOverlay: Int = 0,
    var foundChildhood: String? = "",
    var assemblerArrange: String? = "",
    var type: Int = 0,
    var analysisNest: Boolean = false,
    var sensitivity: Int = 0,
    var literalSpaceSensitive: Boolean = false,
)