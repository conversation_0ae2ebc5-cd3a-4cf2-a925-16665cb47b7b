package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class MainHomeCenterVoEntity(
    var precedenceHorseTwice: Int = 0,
    var buttonTxt: String? = "",
    var miniTips: String? = "",
    var leftBorrowStatusLogo: String? = "",
    var borrowStatus: Int = 0,
    var templateCode: String? = "",
    var centerBorrowStatusText: String? = "",
    val maxAmount: String? = "",
    var checkChampionReverse: Boolean = false,
    var boyPsychologist: Int = 0,
    var combo: Int = 0,
    var auditCountDown: Int = 0,
    var borrowStatusText: String? = "",
    var interestSequence: Boolean = false,
    var refuseSlideshow: RefuseSlideShowBean? = null,
    var fundamentalDry: String? = "",
    var unfreezeDays: Int = 0,
    var duringGifted: String? = "",
    var slideshowVOList: MutableList<SlideshowVOBean>? = mutableListOf(),
    var protocolUrls: ProtocolUrlEntity? = null,
    var leg: String? = "",
    var borrowPurposeList: MutableList<MainHomeBorrowPurposeListEntity>? = mutableListOf(),
    var buttonJumpUrl: String? = "",
    var errorTips: String? = "",
    var borrowStepVOList: MutableList<BorrowStepVoItemBean>? = mutableListOf(),
    var bankInfoVo: MainHomeBankInfoVoEntity? = null,
    var tips: String? = "",
    var centerBorrowStatusLogo: String? = "",
    var productLikeUrl: String? = "",
    var beliefDiscussion: Int = 0,
    var forceDialogTitle: String? = "",
    var secondButtonJumpUrl: String? = "",
    var increasedOpponentYours: Int = 0,
    var buttonBottomLinkJumpUrl: String? = "",
    var remarkOwe: String? = "",
    var rightBorrowStatusText: String? = "",
    var minAmount: String? = "",
    val showMaxProduct: Int = 0,
    var buttonBottomLinkTxt: String? = "",
    var prayer: String? = "",
    var forceDialogContent: String? = "",
    var leftBorrowStatusText: String? = "",
    var quality: Boolean = false,
    var minorException: String? = "",
    var homeMessages: List<String>? = ArrayList(),
    var portRuleSession: Boolean = false,
    var rightBorrowStatusLogo: String? = "",
    var bigTips: String? = "",
    var borrowCashList: MutableList<MainHomeBorrowCashListEntity>? = mutableListOf(),
    var secondButtonTxt: String? = "",
    var borrowProtocolUrl: String? = "",
    var productDescTxt: String? = "",
    var tradeNo:String? = "",
)
