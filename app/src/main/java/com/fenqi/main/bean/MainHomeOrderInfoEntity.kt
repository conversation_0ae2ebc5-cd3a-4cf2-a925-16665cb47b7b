package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class MainHomeOrderInfoEntity(
    var repaymentAmount: String? = "",
    var borrowRatio: String? = "",
    var markGraduateCalling: String? = "",
    var carryAppearanceMovie: String? = "",
    var overdueFeeRate: String? = "",
    var habitDirectory: String? = "",
    var quietly: Int = 0,
    var wildcardFollow: Int = 0,
    var borrowDate: String? = "",
    var totalPeriod: Int = 0,
    var periodLength: Int = 0,
    var bankNo: String? = "",
    var directorHardlyEffort: Boolean = false,
    var overdue: Boolean = false,
    var penalty: String? = "",
    var currentPeriod: Int = 0,
    var toApplyDays: Int = 0,
    var overdueFee: String? = "",
    var license: String? = "",
    var unit: String? = "",
    var surround: Int = 0,
    var suspensionCapitalizedSit: Boolean = false,
    var ktpNo: String? = "",
    var speechAbandonAbuse: Int = 0,
    var six: Int = 0,
    var phone: String? = "",
    var overdueDays: Int = 0,
    var chemicalGoRetain: Boolean = false,
    var toApplyDate: String? = "",
    var tradeNo: String? = "",
    var productCode: String? = "",
    var show: String? = "",
    var increasingBox: String? = "",
    var bankName: String? = "",
    var orderAmount: String? = "",
    var relationship: Int = 0,
    var repaymentDays: Int = 0,
    var borrowerName: String? = "",
    var repaymentDate: String? = "",
    var borrowProtocolUrl: String? = "",
    var everythingAgainst: String? = "",
    var repaySchedule: String? = "",
)
