package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class MainHomeResponseEntity(
    var amountThereafter: Int = 0,
    var mindTurn: Int = 0,
    var absenceIndentationTraditional: String? = "",
    var healthyTransitionRelative: Int = 0,
    var thoughPause: Boolean = false,
    var busSalary: Int = 0,
    var experimentationOffense: Boolean = false,
    var sign: Int = 0,
    var centerVo: MainHomeCenterVoEntity? = null,
    var acquire: String? = "",
    var hmm: String? = "",
    var collectionFixed: String? = "",
    var lifestyleParentAlcohol: String? = "",
    var spotMeasurement: Boolean = false,
    var brieflyRoom: String? = "",
    var palmCriticismJust: String? = "",
    var intellectualFoundFrench: Int = 0,
    var resultRangeTranslation: Int = 0,
)
