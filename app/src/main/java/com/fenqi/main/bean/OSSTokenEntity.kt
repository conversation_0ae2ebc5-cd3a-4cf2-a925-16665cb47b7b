package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class OSSTokenEntity(
    var baseUrl: String? = "",
    var whetherAdvance: String? = "",
    var fifthHandle: String? = "",
    val region: String? = "",
    var valuable: Boolean = false,
    var expiration: String? = "",
    var objectName: String? = "",
    var photographer: Boolean = false,
    var permission: Boolean = false,
    var precedenceMusicBoss: String? = "",
    var blame: Int = 0,
    var holy: String? = "",
    var securityToken: String? = "",
    val clientType: Int = 0,
    var albumNoteEngine: Boolean = false,
    var accessKeySecret: String? = "",
    val urlPrefix: String? = "",
    var wingNative: Boolean = false,
    var toggleAbilityRose: Int = 0,
    var fatherSuspension: String? = "",
    var architectPossibilityFreedom: Boolean = false,
    var boot: Int = 0,
    var accessKeyId: String? = "",
    var continueSki: String? = "",
    var chamberInstead: Boolean = false,
    var relationWoundDesign: Int = 0,
    var bucketName: String? = "",
    var hairPoetSize: Boolean = false,
    var opinion: Boolean = false,
)
