package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class OfflineRepayDataEntity(
    var formYeahDemonstrate: String? = "",
    var requestNo: String? = "",
    var guideUrl: String? = "",
    var delivery: Int = 0,
    var paymentConfirmationCode: String? = "",
    var poundKindCombine: Boolean = false,
    var decreaseQuarter: Int = 0,
    var amountCarePeriod: Boolean = false,
    var roughlyPrepareCreative: Int = 0,
    var subgroupYoursDevelop: Int = 0,
    var approveEntry: String? = "",
    var holdName: String? = "",
    var bankCode: String? = "",
    var leadingMassLoud: String? = "",
    var closelyReinforceSomewhere: String? = "",
    var asciiExactly: Boolean = false,
    var amount: String? = "",
    var accountNo: String? = "",
    var decreaseSeekRepresent: String? = "",
    var exampleTalentColleague: Boolean = false,
    var hungryRecoverable: String? = "",
    var drive: String? = "",
    var onlineMoving: String? = "",
    var layoutRestrictingInteger: Boolean = false,
    var reduce: Boolean = false,
    var hair: Boolean = false,
    var bankList: MutableList<BankListEntity>? = mutableListOf(),
)

@Serializable
data class BankListEntity(
    var formYeahDemonstrate: String? = "",
    var requestNo: String? = "",
    var guideUrl: String? = "",
    var delivery: Int = 0,
    var paymentConfirmationCode: String? = "",
    var poundKindCombine: Boolean = false,
    var decreaseQuarter: Int = 0,
    var amountCarePeriod: Boolean = false,
    var roughlyPrepareCreative: Int = 0,
    var subgroupYoursDevelop: Int = 0,
    var approveEntry: String? = "",
    var holdName: String? = "",
    var bankCode: String? = "",
    var leadingMassLoud: String? = "",
    var closelyReinforceSomewhere: String? = "",
    var asciiExactly: Boolean = false,
    var amount: String? = "",
    var accountNo: String? = "",
    var decreaseSeekRepresent: String? = "",
    var exampleTalentColleague: Boolean = false,
    var hungryRecoverable: String? = "",
    var drive: String? = "",
    var onlineMoving: String? = "",
    var layoutRestrictingInteger: Boolean = false,
    var reduce: Boolean = false,
    var hair: Boolean = false,
    var bankList: MutableList<BankListEntity>? = mutableListOf(),
)
