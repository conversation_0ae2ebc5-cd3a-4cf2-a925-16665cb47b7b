package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class OrderAuthConfirmRequestResponseBean(
    var startSessionFashion: Int = 0,
    var prayerYearEager: String? = "",
    var additiveGrey: Boolean = false,
    var pairHaveThroughout: Boolean = false,
    var aboveboardCommander: Int = 0,
    var combination: Boolean = false,
    var anniversaryUnusualMap: String? = "",
    var ticket: String? = "",
    var splash: Boolean = false,
    var ropeAdequateOrder: String? = "",
    var employ: String? = "",
    var nameDos: String? = "",
    var taskCombinationAssure: Boolean = false,
    var jumpUrl: String? = "",
    var reduceQuotation: Boolean = false,
    var capitalized: Int = 0,
    var detailed: Int = 0,
    var victoryFactLast: Boolean = false,
)
