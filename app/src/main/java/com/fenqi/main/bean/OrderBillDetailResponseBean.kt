package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class OrderBillDetailResponseBean(
    var rubVipBike: String? = "",
    var matterComponent: Boolean = false,
    var data: String? = "",
    var keepStableGold: String? = "",
    var grabAssess: Boolean = false,
    var branchLight: Boolean = false,
    var requireSeizeWet: Boolean = false,
    var bottomTradeFieldDetails: MutableList<CommonTwoTextListItemBean>? = mutableListOf(),
    var bootTogether: Boolean = false,
    var agoStrongly: String? = "",
    var topTradeFieldDetails: MutableList<CommonTwoTextListItemBean>? = mutableListOf(),
    var worksFineDes: Int = 0,
    var cover: String? = "",
    var fireNeighborParticularly: String? = "",
    var riseParagraph: Boolean = false,
    var factorRatioYet: String? = "",
    var difficultyChristmas: Int = 0,
    var teenager: Boolean = false,
)
