package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class OrderBillInProgressEntity(
    var borrowDuration: String? = "",
    var overdueFee: String? = "",
    var ceoObservation: String? = "",
    var omitLiterallyCalculation: String? = "",
    var repaymentAmount: String? = "",
    var suspensionTotalHost: Int = 0,
    var cluster: Boolean = false,
    var drama: Int = 0,
    var tradeNo: String? = "",
    var complexityLongerInvestor: Int = 0,
    var buttonJumpUrl: String? = "",
    var equalPlateFrequently: Int = 0,
    var horrorForthVary: Int = 0,
    var warnStudentConditional: Boolean = false,
    var unitWeekendExpanding: Boolean = false,
    var top: Int = 0,
    var music: Int = 0,
    var secondButtonTxt: String? = "",
    var orderAmount: String? = "",
    var secondButtonJumpUrl: String? = "",
    var spreadPatientHomeless: Int = 0,
    var buttonTxt: String? = "",
    var commandValueSeven: Int = 0,
    var overdueDays: Int = 0,
    var studioUpon: Boolean = false,
    var repaymentDate: String? = "",
    var overdue: Boolean = false,
    var joint: Boolean = false,
    var protocolUrl: String? = "",
)
