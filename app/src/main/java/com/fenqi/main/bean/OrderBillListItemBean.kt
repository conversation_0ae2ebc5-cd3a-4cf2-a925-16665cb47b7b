package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class OrderBillListItemBean(
    var reserveExpenseAllowed: String? = "",
    var pledgePayType: Int = 0,
    var horseProspect: Int = 0,
    var pledgeRepaymentCapital: String? = "",
    var orderStatusDesc: String? = "",
    var repaymentCapital: String? = "",
    var repaymentDate: String? = "",
    var compression: Int = 0,
    var tradeNo: String? = "",
    var needPayCapital: String? = "",
    var tooth: Boolean = false,
    var repaymentDays: Int = 0,
    var pledgePaidStatusDesc: String? = "",
    var teamCold: Boolean = false,
    var beyond: Int = 0,
    var marketEffortDance: Int = 0,
    var billNo: String? = "",
    var unfortunatelyTrust: Boolean = false,
    var test: Int = 0,
    var connectionLawn: String? = "",
    var blue: String? = "",
    var turnDirectlyOrganization: Boolean = false,
    var paidStatusDesc: String? = "",
    var currentBorrowCapital: String? = "",
    var religiousCopyright: String? = "",
    var orderStatus: Int = 0,
    var pledgeNeedPayCapital: String? = "",
    var initiallyBillionAnywhere: Int = 0,
    var pledgeOrderStatusDesc: String? = "",
    var recognizeProgress: String? = "",
    var regulationShowWindow: String? = "",
    var characterize: Boolean = false,
    var pledgeRepaymentDate: String? = "",
    var paidStatus: Int = 0,
    var cycleStartDate: String? = "",
    var cycleDate: String? = "",
    var payType: Int = 0,
    var overdueDays: Int = 0,
    var pledgeOverdueDays: Int = 0,
    var removeSwitch: Int = 0,
    var pledgePaidStatus: Int = 0,
    var pledgeRepaymentDays: Int = 0,
    var societyTypewriterPolitics: Boolean = false,
    var currentPeriodDesc: String? = "",
    var paidOrderStatusDesc: String? = "",
    var plantHearing: Int = 0,


    )
