package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class OrderBillPaidListResponseBean(
    var despite: String? = "",
    var transformWrite: Boolean = false,
    var under: String? = "",
    var alternate: Int = 0,
    var buy: Int = 0,
    var german: Boolean = false,
    var skeleton: Int = 0,
    var paidBillList: MutableList<OrderBillListItemBean>? = mutableListOf(),
    var toRangeInternet: Int = 0,
    var dustRepeatedlyLonger: Boolean = false,
    var offenseSubject: Boolean = false,
    var neighbor: String? = "",
    var attemptFourth: Boolean = false,
    var valleyBoth: Boolean = false,
    var cutThisStay: String? = "",
    var legalUnmarked: Boolean = false,
    var evaluatePlatform: Int = 0,
    var distributionThirdDesign: String? = "",
)
