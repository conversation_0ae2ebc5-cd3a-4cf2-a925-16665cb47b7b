package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class OrderBillRepaymentListResponseBean(
    var dagerMinuteIntention: String? = "",
    var singleAdapter: Boolean = false,
    var pool: String? = "",
    var asideNeitherEnd: Int = 0,
    var last: Boolean = false,
    var gap: Int = 0,
    var achieveVision: Int = 0,
    var breakfastPrideBeside: String? = "",
    var panelWhatever: String? = "",
    var proofMeterHeavily: Int = 0,
    var repaymentBillList: MutableList<OrderBillListItemBean>? = mutableListOf(),
    var decreaseDoctorGlobal: Int = 0,
    var pace: String? = "",
    var authorCompletelyAnyone: String? = "",
    var electronicHorizonBlue: Int = 0,
    var towardSwitch: Int = 0,
    var projectImportant: Int = 0,
    var pole: String? = "",
    var dailyClear: Int = 0,
)
