package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class OrderConfirmBillDetailEntity(
    var experimentationShootingRunning: Int = 0,
    var touchHistory: Boolean = false,
    var wanderUndefined: Int = 0,
    var productInfo: OrderConfirmProductInfoEntity? = null,
    var question: String? = "",
    var operationTitle: String? = "",
    var variousFrequently: Boolean = false,
    var suddenRisk: Boolean = false,
    var crucialAdditionally: String? = "",
    var bracketCongratulationMaybe: Boolean = false,
    var officePredictBeginning: Boolean = false,
    var orderFieldDetails: MutableList<OrderConfirmBillItemEntity>? = mutableListOf(),
    var alphaCommunication: Int = 0,
    var recent: Int = 0,
    var connectionOfficeRecover: Boolean = false,
    var tableBelieved: Int = 0,
    var imposeCriticism: Boolean = false,
    var residentCapitalHusband: Int = 0,
    var interfere: String? = "",
    var borrowProtocolUrl: String? = "",
    var barOccupyFile: Int = 0,
    var efficientSortLetter: String? = "",
    var usJusticeBelt: Int = 0,
)
