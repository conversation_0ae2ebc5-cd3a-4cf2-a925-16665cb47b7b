package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class OrderConfirmDetailResponseBean(
    var middleTradeFieldDetails: MutableList<CommonTwoTextListItemBean>? = mutableListOf(),
    var beOutMainly: Int = 0,
    var topTradeFieldDetails: MutableList<CommonTwoTextListItemBean>? = mutableListOf(),
    var marginCongressional: Boolean = false,
    var formedArrow: String? = "",
    var pledgeDetailVO: PledgeDetailVOBean? = null,
    var assign: Boolean = false,
    var explanationStaticKiss: Boolean = false,
    var equipmentAlignment: Boolean = false,
    var declaredSorryFeature: Boolean = false,
    var strongFacilitySkill: Int = 0,
    var enoughAction: String? = "",
    var warrantyTraditionalTheory: Int = 0,
    var tradeNo: String? = "",
    var appearanceSustainEmployment: Boolean = false,
    var trainingAutoindexScene: Boolean = false,
    var brownThinking: Int = 0,
    var meaning: Int = 0,
    var boxDistanceMount: Boolean = false,
    var bottomTradeFieldDetails: MutableList<CommonTwoTextListItemBean>? = mutableListOf(),
    var maxAmount: String? = "",
    var ware: String? = "",
    var threeSoMaking: String? = "",
    var orderFlowVOList: MutableList<OrderFlowVoItemBean>? = mutableListOf(),
)
