package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class OrderConfirmProductInfoEntity(
    var variety: String? = "",
    var tobaccoNumeric: Int = 0,
    var banStable: Boolean = false,
    var partExternalRelax: Int = 0,
    var analysisReformNegative: Int = 0,
    var mailNovelDie: Int = 0,
    var fallInterpretableAutomatically: Boolean = false,
    var principalFifthOrder: String? = "",
    var interest: String? = "",
    var name: String? = "",
    var receivedAmount: String? = "",
    var tableMaskComplete: String? = "",
    var machinePlacementCanadian: String? = "",
    var periodUnit: String? = "",
    var productCode: String? = "",
    var abortIdentify: Boolean = false,
    var interestFee: String? = "",
    var pauseBossUgly: String? = "",
    var lowerRemove: String? = "",
    var serviceFee: String? = "",
    var selectInformationDesperate: Int = 0,
    var period: Int = 0,
    var effectLevelReference: String? = "",
    var although: Int = 0,
    var managerPineJump: Int = 0,
    var bigProgressSystem: Int = 0,
    var monthlyPayment: String? = "",
    var winterDrive: Int = 0,
)
