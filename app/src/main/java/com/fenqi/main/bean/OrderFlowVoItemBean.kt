package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class OrderFlowVoItemBean(
    var buttonName: String? = "",
    var ableCertain: String? = "",
    var bill: Int = 0,
    var purchaseEnter: Boolean = false,
    var copFourth: String? = "",
    var oldReorderDoor: String? = "",
    var favoriteNever: String? = "",
    var word: String? = "",
    var galleryStampNetwork: Boolean = false,
    var buttonStatus: Int = 0,
    var cost: Int = 0,
    var errorSameAthletic: Boolean = false,
    var successfullySouthIndexing: String? = "",
    var highlightLeadStuff: Boolean = false,
    var successful: String? = "",
    var programmerCiteAnxiety: Boolean = false,
    var flowLogo: String? = "",
    var lemonConsistPerson: Boolean = false,
    var flowName: String? = "",
)
