package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class OrderHistoryEntity(
    var owe: String? = "",
    var age: Int = 0,
    var airlineParking: Int = 0,
    var splitEuropean: Int = 0,
    var rapidlyDevelopInvestigation: String? = "",
    var courage: Boolean = false,
    var reform: Int = 0,
    var service: Boolean = false,
    var environment: Boolean = false,
    var quoteAsk: String? = "",
    var assumption: Boolean = false,
    var shelter: Boolean = false,
    var like: Boolean = false,
    var appointmentYeah: String? = "",
    var buttonSpeaker: Int = 0,
    val billHistoryVoList: MutableList<OrderHistoryItemEntity>? = mutableListOf(),
)
