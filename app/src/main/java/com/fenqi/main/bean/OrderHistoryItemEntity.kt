package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class OrderHistoryItemEntity(
    var drawInformationGrab: Int = 0,
    var dirtDescription: Boolean = false,
    var fragmentRing: String? = "",
    var unmarkedFemale: Int = 0,
    var investor: String? = "",
    var happy: Int = 0,
    var bodyDialogueDog: String? = "",
    var createDt: String? = "",
    var judgeFighterPoem: Boolean = false,
    var observed: Int = 0,
    var music: Boolean = false,
    var operatePose: Int = 0,
    var borrowDuration: Int = 0,
    var dispute: Boolean = false,
    var realCapital: String? = "",
    var emotional: Boolean = false,
    var techniquePersonnelSimple: Int = 0,
    var orderNo: String? = "",
    var distinction: Int = 0,
    var strengthWave: Int = 0,
    var fitnessAlso: Boolean = false,
    var differenceKnockDeclare: String? = "",
    var presenceBusExpectation: Boolean = false,
    var perceiveHorizontal: Int = 0,
)
