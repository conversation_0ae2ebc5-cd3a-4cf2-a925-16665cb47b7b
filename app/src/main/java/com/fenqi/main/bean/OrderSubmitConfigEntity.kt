package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class OrderSubmitConfigEntity(
    var cropAmericanChoose: Boolean = false,
    var fileUploadType: Int = 0,
    var disableG: Boolean = false,
    var submitOccupy: Int = 0,
    var disableC: Boolean = false,
    var alternately: Int = 0,
    var constantMarryFlame: Boolean = false,
    var opposite: String? = "",
    var turn: String? = "",
    var issueTypicalAnd: Int = 0,
    var boardUnique: Int = 0,
    var withdrawAuthChoose: Int = 0,
    var kindCharacteristic: String? = "",
    var maxCount: Int = 1000,
    var table: Int = 0,
    var absorbContentHerself: String? = "",
    var discoveryAddressAppoint: Int = 0,
    var disableW: Boolean = false,
    var disableS: Boolean = false,
    var ceoVary: String? = "",
    var impressLet: Int = 0,
    var underlyingGerman: String? = "",
    var disableR: Boolean = false,
    var disableD: Boolean = false,
    var months: Int = 2,

    )
