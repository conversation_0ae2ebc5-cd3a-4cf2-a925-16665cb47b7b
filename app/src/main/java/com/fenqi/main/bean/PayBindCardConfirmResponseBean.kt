package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class PayBindCardConfirmResponseBean(
    var obviousSpeechInstall: Int = 0,
    var bindId: String? = "",
    var addition: Boolean = false,
    var wildcard: Boolean = false,
    var translate: Boolean = false,
    var slightly: Boolean = false,
    var channelOrderNo: String? = "",
    var errorCode: String? = "",
    var pot: Int = 0,
    var helicopterInstruction: Boolean = false,
    var prefixHousingWelcome: Int = 0,
    var increaseDisasterThree: Int = 0,
    var managePowder: String? = "",
    var layoutStick: Boolean = false,
    var label: Boolean = false,
    var edgeEmptyWindow: Boolean = false,
    var fictionFreedom: Boolean = false,
    var errorMsg: String? = "",
    var signStatus: Int = 0,
    var grabFineAbort: Boolean = false,
    var capabilityReasonableOffense: String? = "",
    var createScientistChase: <PERSON>olean = false,
)
