package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class PayBindCardRequestResponseBean(
    var aboveboardSteal: Int = 0,
    var winnerShadeIndex: Int = 0,
    var signStatus: String? = "",
    var analysis: Boolean = false,
    var numeral: Int = 0,
    var flowId: String? = "",
    var reduceMention: String? = "",
    var anytimeCat: String? = "",
    var missile: String? = "",
    var loadConditionAlignment: Int = 0,
    var violence: String? = "",
    var big: String? = "",
    var bean: String? = "",
    var smsNum: Int = 0,
    var headIdentificationContemporary: Boolean = false,
    var errorCode: String? = "",
    var absolutelyThus: Boolean = false,
    var exception: String? = "",
    var composeCowTime: Boolean = false,
    var parseChapter: Boolean = false,
    var errorMsg: String? = "",
)
