package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class PayClientChargesEntity(
    var numericJournalistAlpha: String? = "",
    var conclusionAdjustmentDisplay: Boolean = false,
    var cashFreeToken: String? = "",
    var equationTribe: Boolean = false,
    var verifyPointCommon: Int = 0,
    var tip: String? = "",
    var choiceEdge: Int = 0,
    var repaymentJson: String? = "",
    var lengthArrive: Boolean = false,
    var spinShape: Int = 0,
    var copePerformanceMath: String? = "",
    var event: Int = 0,
    var payKey: String? = "",
    var push: String? = "",
    var heroMultiple: Int = 0,
    var payClientType: Int = 0,
    var trendCycleCharacterize: Boolean = false,
    var voucherTips: String? = "",
    var hardwareInteractive: String? = "",
    var probablyCorrectlyInjury: String? = "",
    var processBeepSure: Boolean = false,
    var proprietary: Boolean = false,
    var editionPacific: String? = "",
    var assertNeverTension: Int = 0,
    var holeAggressive: String? = "",
    var cashFreeContent: String? = "",
    var nice: Boolean = false,
)
