package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class PayClientEntity(
    var partPayMinAmount: Int = 0,
    var pet: Boolean = false,
    var rareBan: Int = 0,
    var pollCurrent: String? = "",
    var lockingCompile: String? = "",
    val payWayList: MutableList<PayClientMethodIEntity>? = mutableListOf(),
    var payAmount: String? = "",
    var regex: String? = "",
    var partPayFlag: Boolean = false,
    var layerMeet: Int = 0,
    var partPayMaxAmount: Int = 0,
    var fleshForestObligation: Boolean = false,
    var everyone: Boolean = false,
    var deriveWho: Boolean = false,
    var write: Boolean = false,
    var described: Int = 0,
    var backPortion: Int = 0,
    var go: Boolean = false,
    var sink: String? = "",
    var allyEquivalent: Boolean = false,
)
