package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class PayClientMethodIEntity(
    var lineSuccessful: String? = "",
    var rocketStatementPine: String? = "",
    var usAfricanNeither: Boolean = false,
    var year: String? = "",
    var pale: Int = 0,
    var image: Boolean = false,
    var icon: String? = "",
    var mine: Boolean = false,
    var tieApple: Boolean = false,
    var witnessRestrictionHide: Boolean = false,
    var repeatedly: Int = 0,
    var desc: String? = "",
    var wayLimitations: Boolean = false,
    var rootGrowingClinic: String? = "",
    var name: String? = "",
    var assist: Boolean = false,
    var hourCompleteShade: String? = "",
    var throughoutTeaspoonKing: String? = "",
    var loadedPanelFine: String? = "",
    var feature: Boolean = false,
    var discriminationSatisfy: Int = 0,
    var development: Boolean = false,
)
