package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class PledgeDetailVOBean(
    var looseHeightAsleep: Boolean = false,
    var statementEnvironment: Int = 0,
    var survivor: String? = "",
    var butterExact: Int = 0,
    var drug: Boolean = false,
    var magentaSubstituteDistribution: String? = "",
    var massive: String? = "",
    var comparison: Boolean = false,
    var money: Int = 0,
    var tips: String? = "",
    var floatAidBehind: String? = "",
    var contributionNormalLiberal: String? = "",
    var testimonyHiddenInline: String? = "",
    var platformDarkHardware: Boolean = false,
    var union: String? = "",
    var seasonZoom: Boolean = false,
    var reloadTrackMoney: Int = 0,
    var pledgeMoney: String? = "",
    var containerEnergy: String? = "",
    var justice: String? = "",
    var corporate: Int = 0,
    var title: String? = "",
    var presentEvaluate: Int = 0,
)
