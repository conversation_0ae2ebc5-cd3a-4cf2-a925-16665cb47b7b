package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class PrivacyPolicyBean(
    var carrierInputMarking: Int = 0,
    var growingConcertCard: String? = "",
    var studioPainful: Int = 0,
    var fontDensityManagement: String? = "",
    var appName: String? = "",
    var reasonSmell: Boolean = false,
    var commonCheapLovely: Boolean = false,
    var colorFormTea: String? = "",
    var thereafterComputer: String? = "",
    var text: String? = "",
    var joint: Boolean = false,
    var companyName: String? = "",
    var attributeDebt: Int = 0,
    var testIncreasinglyPreset: Int = 0,
    var consecutiveBusyIndicator: Int = 0,
    var fifteenAddress: Boolean = false,
    var customInsureCarefully: Int = 0,
    var title: String? = "",
    var dueHighestEither: Int = 0,
)