package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class ProtocolEntity(
    var key: String? = "",
    var through: String? = "",
    var telephoneHeading: Int = 0,
    var evenToday: String? = "",
    var pressUp: Boolean = false,
    var okfast: String? = "",
    var low: Int = 0,
    var encourage: String? = "",
    var url: String? = "",
    var legal: Boolean = false,
    var she: Int = 0,
    var alcohol: Int = 0,
    var shadow: Boolean = false,
    var especiallyTablespoon: String? = "",
    var ultimately: String? = "",
    var eyeAgreement: Boolean = false,
    var assistanceImageStamp: String? = "",
    var name: String? = "",
)
