package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class ProtocolListEntity(
    var confidential: Int = 0,
    var consensusCompareEdition: Int = 0,
    var containImmigration: Int = 0,
    var list: ProtocolUrlEntity? = null,
    var indianCompose: Boolean = false,
    var attractNeverRope: Boolean = false,
    var fishingNaturally: Int = 0,
    var particularLinkerLow: Int = 0,
    var male: Int = 0,
    var permitUnformatted: Boolean = false,
    var dearSuggest: Boolean = false,
    var terribleChinese: Boolean = false,
    var digitalSeriesPace: String? = "",
    var widespreadPresence: Int = 0,
    var airport: Int = 0,
    var weight: String? = "",
    var starExactly: Int = 0,
    var friend: Boolean = false,
)
