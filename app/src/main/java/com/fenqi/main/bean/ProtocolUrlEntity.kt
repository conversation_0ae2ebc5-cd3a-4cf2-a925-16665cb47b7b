package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class ProtocolUrlEntity(
    var live: Int = 0,
    var rateEfficientlyOutcome: Int = 0,
    var publisherLayer: String? = "",
    var funnyPrescription: Boolean = false,
    var taskBoth: <PERSON>olean = false,
    var officer: Boolean = false,
    var possibility: Boolean = false,
    var club: Boolean = false,
    var symptom: Int = 0,
    var often: Int = 0,
    var fiction: String? = "",
    var substantialFlySpecifically: Boolean = false,
    var countryConnection: Boolean = false,
    var associate: Boolean = false,
    var privacyAgreement: ProtocolEntity? = null,
    var shoppingNewsEssentially: Int = 0,
    var expensiveThroughout: String? = "",
    var pile: Boolean = false,
    var aboutUs: ProtocolEntity? = null,
)
