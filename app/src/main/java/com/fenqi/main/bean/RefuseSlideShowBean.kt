package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class RefuseSlideShowBean(
    var appeal: Boolean = false,
    var serialUser: Boolean = false,
    var growthHouse: Int = 0,
    var spreadCookie: String? = "",
    var tower: Int = 0,
    var terroristMovement: Boolean = false,
    var certainRespondentEngine: Int = 0,
    var distribute: String? = "",
    var secondConformSociety: String? = "",
    var acrossCenter: String? = "",
    var considerationFreedomMenu: Int = 0,
    var judgeChart: Boolean = false,
    var exclamationRecipe: Boolean = false,
    var conventional: Int = 0,
    var backup: Int = 0,
    var united: Boolean = false,
    var aircraftAssure: Boolean = false,
    var salad: Int = 0,
    var parentBible: Int = 0,
    var title: String? = "",
    var imgUrl: String? = "",
    var jumpUrl: String? = "",
    var fifteenFor: String? = "",
)
