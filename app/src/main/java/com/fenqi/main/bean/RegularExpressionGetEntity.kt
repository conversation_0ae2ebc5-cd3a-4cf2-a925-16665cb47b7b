package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class RegularExpressionGetEntity(
    var idCodeRegExp: String? = "",
    var resetFifteenDrop: Boolean = false,
    var versionMatch: Int = 0,
    var urgeTrack: String? = "",
    var operateUniversal: String? = "",
    var happenFactorsSegment: String? = "",
    var retireAway: Boolean = false,
    var edgeRecentYesterday: Boolean = false,
    var fix: Int = 0,
    var float: Boolean = false,
    var caseAdvertising: String? = "",
    var hypothesisDimension: Int = 0,
    var mobileRegExp: String? = "",
    var currently: Boolean = false,
    var originally: Boolean = false,
    var hearElectric: Boolean = false,
    var bvnRegExp: String? = "",
    var poll: String? = "",
    var configure: String? = "",
    var importanceExtraSubscript: Int = 0,
)
