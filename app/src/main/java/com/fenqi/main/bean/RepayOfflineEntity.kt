package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class RepayOfflineEntity(
    var keyGoldEnable: Boolean = false,
    var experienceCostMoment: Boolean = false,
    var concertTravelBreath: String? = "",
    var methodCapitalizedLending: String? = "",
    var increment: Int = 0,
    var eliminateFade: Int = 0,
    var confuseTubeStroke: String? = "",
    var shieldDifference: String? = "",
    var saving: Boolean = false,
    var customGrandfather: String? = "",
    var title: String? = "",
    var softwareReloadDate: String? = "",
    var accessFor: String? = "",
    val reminderContent: String? = "",
    val reminderTitle: String? = "",
    var concernSuspect: Boolean = false,
    var mainframeDirectoryPolitically: Boolean = false,
    val headerPicture: String? = "",
    val customerServiceUrl: String? = "",
    var boundaryToss: Int = 0,
    var rearrange: Int = 0,
    var statisticsSecureNumerical: String? = "",
    var producer: Boolean = false,
)