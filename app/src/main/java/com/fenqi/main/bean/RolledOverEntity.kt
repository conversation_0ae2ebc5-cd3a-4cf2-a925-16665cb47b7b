package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class RolledOverEntity(
    var renewalAmount: String? = "",
    var navigate: Boolean = false,
    var eitherCompactWare: String? = "",
    var renewalDays: String? = "",
    var applicable: String? = "",
    var closeImplementMean: Boolean = false,
    var deerDearlyChairman: String? = "",
    var regex: Int = 0,
    var distinctionStatementClothing: Int = 0,
    var usSymptom: Int = 0,
    var seize: Boolean = false,
    var indeed: Boolean = false,
    var concerned: String? = "",
    var pace: Boolean = false,
    var cellShadeUpper: Boolean = false,
    var info: InfoEntity? = null,
    var shiftSorryRectangular: String? = "",
    var degradeProcessing: Int = 0,
    var publisher: Boolean = false,
    var repaymentDate: String? = "",
    var must: Boolean = false,
)

@Serializable
data class InfoEntity(
    var renewalAmount: String? = "",
    var navigate: Boolean = false,
    var eitherCompactWare: String? = "",
    var renewalDays: String? = "",
    var applicable: String? = "",
    var closeImplementMean: Boolean = false,
    var deerDearlyChairman: String? = "",
    var regex: Int = 0,
    var sharp: String? = "",
    var distinctionStatementClothing: Int = 0,
    var usSymptom: Int = 0,
    var seize: Boolean = false,
    var indeed: Boolean = false,
    var concerned: String? = "",
    var pace: Boolean = false,
    var cellShadeUpper: Boolean = false,
    var info: InfoEntity? = null,
    var shiftSorryRectangular: String? = "",
    var degradeProcessing: Int = 0,
    var publisher: Boolean = false,
    var repaymentDate: String? = "",
    var must: Boolean = false,
)
