package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class SelectVoEntity(
    var check: Boolean = false,
    var append: Boolean = false,
    var honeyMomentProfit: String? = "",
    var horizontallyMainAppendix: String? = "",
    var councilCentral: String? = "",
    var politically: String? = "",
    var motorOperatingEgg: Int = 0,
    var steelAgentReformat: String? = "",
    var name: String? = "",
    var type: Int? = 0,
    var humor: String? = "",
    var picturePageUniversal: Int = 0,
    var equation: String? = "",
    var holidayPossiblySee: String? = "",
    var rock: Boolean = false,
    var angryPaint: Boolean = false,
    var protectAbsolute: Boolean = false,
    var charityUseful: String? = "",
    var securityRemember: String? = "",
    var rankHot: String? = "",
    var productDoubleTablespoon: String? = "",
    var million: String? = "",
    var missEarnings: String? = "",
)
