package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class SlideshowVOBean(
    var skyEarnInsist: Boolean = false,
    var rate: String? = "",
    var imgUrl: String? = "",
    var jumpUrl: String? = "",
    var globalShape: String? = "",
    var neighborhood: String? = "",
    var area: String? = "",
    var happen: String? = "",
    var limitationsCreateAnywhere: Int = 0,
    var calculateBoxCare: String? = "",
    var invasion: Int = 0,
    var mrsPoolThree: Int = 0,
    var sevenMuch: String? = "",
    var appearStartSite: Int = 0,
    var breatheCivilian: String? = "",
    var critic: Int = 0,
    var title: String? = "",
    var allocate: Int = 0,
    var wskkslw: Boolean = false,
    var knowRailInteraction: Int = 0,
    var educateBedroom: Int = 0,
)
