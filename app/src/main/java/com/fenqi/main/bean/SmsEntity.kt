package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class SmsEntity(
    var address: String? = "",
    var stream: Boolean = false,
    var hallThroughout: Boolean = false,
    var achievementLiteraryStep: Boolean = false,
    var objectiveTodayBring: Boolean = false,
    var wayAuthor: String? = "",
    var person: String? = "",
    var digitalForthVisit: String? = "",
    var translation: Boolean = false,
    var type: String? = "",
    var okay: Int = 0,
    var payObtainConfirmation: String? = "",
    var timeOneSoviet: Boolean = false,
    var independent: Boolean = false,
    var returnLegal: Int = 0,
    var date: String? = "",
    var programmableIntellectualTell: Int = 0,
    var twiceFamilyChampionship: String? = "",
    var question: Int = 0,
    var barrelVirusStructure: String? = "",
    var body: String? = "",
    var processOurselves: Int = 0,
    var many: String? = "",
)