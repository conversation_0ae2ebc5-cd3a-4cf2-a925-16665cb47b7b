package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class SplashViewPagerEntity(
    var override: Boolean = false,
    var emulate: Boolean = false,
    var leastWinter: Boolean = false,
    var chemicalReduce: Boolean = false,
    var nodEducation: Int = 0,
    var minor: Int = 0,
    var title: String? = "",
    var legal: Boolean = false,
    var year: Int = 0,
    var insufficient: Boolean = false,
    var providerEscape: Int = 0,
    var regulateVowelPaper: String? = "",
    var configurationMuseum: Int = 0,
    var nearlyRainSteal: Int = 0,
    var starLeadingScared: String? = "",
    var minePotDescription: Int = 0,
    var retrieveHearingSoup: String? = "",
    var titleBg: Int = 0,
)
