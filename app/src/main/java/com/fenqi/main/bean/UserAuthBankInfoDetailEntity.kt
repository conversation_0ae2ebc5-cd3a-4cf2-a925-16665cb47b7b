package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class UserAuthBankInfoDetailEntity(
    var filterSake: Boolean = false,
    var catholicUserConvention: Boolean = false,
    var childhoodUpper: String? = "",
    var ensembleConsole: Boolean = false,
    var finished: Boolean = false,
    var arithmeticRopeIdentical: String? = "",
    var bankRoList: MutableList<BankRoEntity>? = mutableListOf(),
    var offenseVery: Int = 0,
    var alive: Int = 0,
    var deficit: String? = "",
    var respondentConsiderationBroad: Int = 0,
    var settlementDiscussInvestigate: Boolean = false,
    var closelyAdministrator: Int = 0,
    var flightRawUser: Boolean = false,
    var dirtPrompt: Int = 0,
    var needHolderName: Boolean = false,
    var defenseCriminalGrowing: Int = 0,
    var bankInfo: UserAuthBankVoEntity? = null,
    var burden: String? = "",
    var lineCookingReverse: Boolean = false,
    var stupid: Boolean = false,
    var remarkGenerallySite: Boolean = false,
    var floatSuddenly: String? = "",
    var cooperationWhy: String? = "",
)
