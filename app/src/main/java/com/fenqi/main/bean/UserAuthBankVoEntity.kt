package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class UserAuthBankVoEntity(
    var hateGrowHandful: String? = "",
    var name: String? = "",
    var cardNo: String? = "",
    var rootPerformance: Boolean = false,
    var splash: String? = "",
    var feedAdvice: Int = 0,
    var stream: Int = 0,
    var offer: Int = 0,
    var influenceCapture: Int = 0,
    var securityFireVowel: Boolean = false,
    var serve: Boolean = false,
    var bankName: String? = "",
    var birthdayWatchGaze: Boolean = false,
    var occupySevenEconomy: Boolean = false,
    var environmentalRussianTv: Int = 0,
    var labelDeskHouse: String? = "",
    var mobile: String? = "",
    var code: String? = "",
    var surpriseUnsigned: String? = "",
    var presetDesignateIndent: String? = "",
    var glad: String? = "",
    var porch: String? = "",
)
