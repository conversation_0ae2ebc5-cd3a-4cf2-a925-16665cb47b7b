package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class UserAuthConfigGetBean(
    val appId: String? = "",
    var lack: Boolean = false,
    val idCard: String? = "",
    var according: String? = "",
    val frontUrl: String? = "",
    var rather: String? = "",
    val realName: String? = "",
    var finished: Boolean = false,
    var formWall: Int = 0,
    var differentlyTennis: Boolean = false,
    var type: Boolean = false,
    var assistantIncome: String? = "",
    var treatRecycleNetwork: String? = "",
    var agreement: Boolean = false,
    var occasion: String? = "",
    val faceUrl: String? = "",
    var consistMistake: Int = 0,
    var adequateExploreConsistent: String? = "",
    var divorceAcceptMemory: String? = "",
    var regularCommission: Int = 0,
    var focusGuest: Boolean = false,
    var involvementAdmit: Boolean = false,
    var divorceNeck: Boolean = false,
    val accountInfo: AccountInfo? = null,
    val appKey: String? = "",
    var lostReturn: Int = 0,
    var cardApprove: Int = 0,
    val faceType: Int = 0,
)

@Serializable
data class AccountInfo(
    val appId: String? = "",
    var lack: Boolean = false,
    var according: String? = "",
    val frontUrl: String? = "",
    var rather: String? = "",
    val realName: String? = "",
    var finished: Boolean = false,
    var formWall: Int = 0,
    var differentlyTennis: Boolean = false,
    var type: Boolean = false,
    var assistantIncome: String? = "",
    var treatRecycleNetwork: String? = "",
    var agreement: Boolean = false,
    var occasion: String? = "",
    val faceUrl: String? = "",
    var consistMistake: Int = 0,
    var adequateExploreConsistent: String? = "",
    var divorceAcceptMemory: String? = "",
    var regularCommission: Int = 0,
    var focusGuest: Boolean = false,
    var involvementAdmit: Boolean = false,
    var divorceNeck: Boolean = false,
    val accountInfo: AccountInfo? = null,
    val appKey: String? = "",
    var lostReturn: Int = 0,
    var cardApprove: Int = 0,
    val faceType: Int = 0,
)