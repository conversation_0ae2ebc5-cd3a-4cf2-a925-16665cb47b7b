package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class UserInfoItemEntity(
    var giftDeserve: Int = 0,
    var paramType: Int = 0,
    var inputTip: String? = "",
    var number: Boolean = false,
    var jewBarrier: String? = "",
    var interpret: String? = "",
    var inputDesc: String? = "",
    var seeGold: String? = "",
    var modeCenter: Int = 0,
    var boot: Int = 0,
    var cigaretteForce: String? = "",
    var cloud: Int = 0,
    var inputValue: String? = "",
    var leadingFlee: String? = "",
    var definableFlagIntervene: Boolean = false,
    var modifiedProperlyExceed: Boolean = false,
    var doctor: Boolean = false,
    var machCouldActive: Int = 0,
    var heat: Boolean = false,
    var paramName: String? = "",
    var sufficient: Boolean = false,
    var requied: Boolean = false,
    var param: String? = "",
    var flightClipper: String? = "",
    var readOnly: Boolean = false,
    var administration: Boolean = false,
    var executableListFriendship: Boolean = false,
    var selectVo: MutableList<SelectVoEntity>? = mutableListOf(),
    var show: Boolean = false,
    var constantlyAmpersandEntry: String? = "",
    var findAlign: String? = "",
)
