package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class UserInfoResponseEntity(
    var userInputInfoVOList: MutableList<UserInfoVoEntity>? = mutableListOf(),
    var psychologistStackShade: Int = 0,
    var filterNow: Int = 0,
    var publisherPurchaseInvestment: String? = "",
    var assist: Int = 0,
    var routeMaker: Int = 0,
    var incense: String? = "",
    var barAttack: String? = "",
    var stillCook: Int = 0,
    var switchExperience: String? = "",
    var effort: Int = 0,
    var elseSkin: String? = "",
    var shelf: Int = 0,
    var augmentSequentiallyStay: String? = "",
    var cultureProposalLeader: String? = "",
    var finished: Boolean = false,
    var uppercase: String? = "",
)
