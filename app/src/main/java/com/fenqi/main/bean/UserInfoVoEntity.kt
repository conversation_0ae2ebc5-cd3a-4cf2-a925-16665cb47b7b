package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class UserInfoVoEntity(
    var association: Boolean = false,
    var independent: String? = "",
    var spread: Int = 0,
    var memoryInterpret: String? = "",
    var inputParams: MutableList<UserInfoItemEntity>? = mutableListOf(),
    var basisIndicateApproximately: Boolean = false,
    var makeupSort: String? = "",
    var question: String? = "",
    var eventuallyNearEntry: Int = 0,
    var networkAlignCommunication: String? = "",
    var typeShoreOrganic: Boolean = false,
    var interrupt: Boolean = false,
    var victim: Int = 0,
    var applicableOption: Int = 0,
    var containProtocol: Int = 0,
    var enabled: Boolean = false,
    var finished: Boolean = false,
    var assistRetain: Boolean = false,
    var defenseEasy: Boolean = false,
    var name: String? = "",
    var tunnel: Int = 0,
    var shade: Boolean = false,
    var conceptMainframe: Boolean = false,
    var chipDay: Int = 0,
)
