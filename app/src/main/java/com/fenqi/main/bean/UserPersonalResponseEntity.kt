package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class UserPersonalResponseEntity(
    var avatar: String? = "",
    var energyBattle: Boolean = false,
    var east: String? = "",
    var eastern: Int = 0,
    var aboutUrl: String? = "",
    var feedBackSwitch: Boolean = false,
    var professorHas: String? = "",
    var priestCharm: String? = "",
    var tail: String? = "",
    var exactlyHeight: Int = 0,
    var maxAmount: String? = "",
    var parenthesisAbsencePreference: Int = 0,
    var japanese: Int = 0,
    var butFast: Int = 0,
    var slideshowVOList: MutableList<SlideshowVOBean>? = mutableListOf(),
    var manufacturing: String? = "",
    var projectCup: String? = "",
    var modifyPasswordTip: String? = "",
    var dishHandleWidespread: String? = "",
    var mobile: String? = "",
    var terminology: Boolean = false,
    var buttonText: String? = "",
    var realName: String? = "",
    var contentManufacturer: Int = 0,
    var instruct: Int = 0,
    var decideMale: String? = "",
    var defaultAlternative: String? = "",
    var asynchronousOutcomeTerm: Int = 0,
)
