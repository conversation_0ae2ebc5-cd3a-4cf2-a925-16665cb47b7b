package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class VerifyCodeSendResponseEntity(
    var latter: Int = 0,
    var includeChiefMap: Int = 0,
    var master: Int = 0,
    var machineUnless: Boolean = false,
    var floorUnderstand: Int = 0,
    var understandIdentical: Boolean = false,
    var tankChangingWeb: String? = "",
    var beforeAh: String? = "",
    var paper: Int = 0,
    var warm: Int = 0,
    var specialistJacketEdition: Int = 0,
    var prospectRomanticType: Boolean = false,
    var badAssignment: Int = 0,
    var emission: Int = 0,
    var textReorderTablespoon: Int = 0,
    var grocery: String? = "",
    var coverage: String? = "",
    var commercial: Boolean = false,
    var appearanceRebuildPine: String? = "",
    var sendVerifyCodeSuccessVo: VerifyCodeSendVo? = null,
)