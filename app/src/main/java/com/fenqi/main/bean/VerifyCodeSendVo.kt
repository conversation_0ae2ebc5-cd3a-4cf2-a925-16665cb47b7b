package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class VerifyCodeSendVo(
    var kickGuaranteeCustom: Int = 0,
    var directly: Int = 0,
    var sendSuccess: Boolean = false,
    var cropFormationOption: String? = "",
    var tight: Boolean = false,
    var weatherSlow: String? = "",
    var entirely: Boolean = false,
    var homeTotal: Boolean = false,
    var effectiveSeconds: Int = 0,
    var singleOriginallyMovie: String? = "",
    var normal: Boolean = false,
    var piecePrimarilyEllipsis: Boolean = false,
    var distanceElectMarketing: Boolean = false,
    var fileMainlyVisual: String? = "",
    var participationYeahSoft: String? = "",
    var probably: String? = "",
    var politicsGain: Int = 0,
)
