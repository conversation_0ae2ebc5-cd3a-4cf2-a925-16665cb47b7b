package com.fenqi.main.bean

import kotlinx.serialization.Serializable

@Serializable
data class WifiEntity(
    var anywhereSortLeast: Boolean = false,
    var earn: Int = 0,
    var necessarilyWorksPeople: Int = 0,
    var repaintCommission: Int = 0,
    var motor: Boolean = false,
    var lostRelation: Boolean = false,
    var seem: Int = 0,
    var babyHavePrevious: Boolean = false,
    var reductionCare: Int = 0,
    var bssid: String? = "",
    var supreme: Int = 0,
    var rightScene: String? = "",
    var upStuffContiguous: String? = "",
    var generallyExcellentArrival: String? = "",
    var ssid: String? = "",
    var thickModGet: Boolean = false,
    var guard: Boolean = false,
    var cheek: <PERSON>olean = false,
)
