package com.fenqi.main.constant

object CommonConstant {

    const val MAX_INT_TAG = 9999

    const val AREA_CODE = "+86"

    const val DATA_VIEW_STATUS_TEXT = 1
    const val DATA_VIEW_STATUS_PICKER = 2
    const val DATA_VIEW_STATUS_CONTACT_PICKER = 3
    const val DATA_VIEW_STATUS_TIME_PICKER = 6
    const val DATA_VIEW_STATUS_BANK_NAME_SELECTER = 7
    const val DATA_VIEW_STATUS_CONTACT_RELATIONSHIP = 9

    const val DATA_UPLOAD_TYPE_APP_LIST = 1
    const val DATA_UPLOAD_TYPE_WIFI = 2
    const val DATA_UPLOAD_TYPE_CONTACT = 3
    const val DATA_UPLOAD_TYPE_SMS = 4
    const val DATA_UPLOAD_TYPE_CALL_LOG = 5

    const val UPLOAD_DATA_REGISTER = 1
    const val UPLOAD_DATA_AUTH = 2
    const val UPLOAD_DATA_LOAN = 3

    const val PAY_TYPE_REPAYMENT = "2"
    const val PAY_TYPE_EXTEND = "4"

    const val PAY_TYPE_URL = 5
    const val PAY_TYPE_APP = 7
    const val PAY_TYPE_REPAY_OFFLINE = 6

    const val HEADER_TYPE_NORMAL=0
    const val HEADER_TYPE_RSA=1

    const val FILE_UPLOAD_TYPE_CLIENT = 2

    const val OSS_UPLOAD_TYPE_FILE = "oss_file"
    const val OSS_UPLOAD_TYPE_BYTE = "oss_byte"

    const val AUTH_LIST_STATUS_NOT_COMPLETED = 1
    const val AUTH_LIST_STATUS_WAIT = 7

    const val AUTH_LIST_USER_BASE = "AuthUserBase"
    const val AUTH_LIST_USER_CONTACT = "AuthUserContact"
    const val AUTH_LIST_USER_BANK = "AuthUserBank"
    const val AUTH_LIST_USER_LIVE = "AuthUserLive"
    const val AUTH_LIST_USER_CARRIER = "AuthUserCarrier"

    const val STATUS_HOME = "APP/RenderTemplate/Product/Home"
    const val STATUS_HOME_WAIT_REVIEW = "APP/RenderTemplate/Product/Audit"
    const val STATUS_HOME_WAIT_REPAYMENT = "APP/RenderTemplate/Product/RepaymentWatting"
    const val STATUS_HOME_REPAYMENT_OVERDUE = "APP/RenderTemplate/Product/Overdue"
    const val STATUS_HOME_LOAN_REFUSE = "APP/RenderTemplate/Product/AuditFail"
    const val STATUS_HOME_WAIT_FOR_PAY = "APP/RenderTemplate/Product/LoanWaitting"
    const val STATUS_HOME_CONFIRM_TRADE = "APP/RenderTemplate/Product/ConfirmTrade"
    const val STATUS_HOME_WAIT_SIGN = "APP/RenderTemplate/Product/WaitSign"

    const val STATUS_HOME_PRODUCT_SHORT_URL = "URL/JS/HOME_INDEX"
    const val STATUS_HOME_ROUTE_USER_DATA = "APP/CLViewControllerUserDataList"
    const val STATUS_HOME_ROUTE_LOAN_REPAYMENT = "APP/CLViewRepay"
    const val STATUS_HOME_ROUTE_LOAN_EXTEND = "APP/CLViewLoanRenewal"
    const val STATUS_HOME_PRODUCT_CARD_LIST = "APP/Product/CardList"
    const val STATUS_HOME_PRODUCT_CONFIRM_TRADE = "APP/Product/ConfirmTrade"
    const val STATUS_HOME_PRODUCT_SIGN_CONTRACT = "APP/Product/SignContract"


    const val AUTH_USER_PERSONAL_INFO = "APP/PROFILE/PERSONAL_INFO"
    const val AUTH_USER_CONTACT_INFO = "APP/PROFILE/CONTACT_INFO"
    const val AUTH_USER_BANK_CARD = "APP/PROFILE/BANK_CARD"
    const val AUTH_USER_FACE_OCR = "APP/PROFILE/FACE_OCR"
    const val AUTH_USER_USER_CREDIT = "APP/PROFILE/CREDIT"
    const val AUTH_USER_CARD_KYC = "APP/PROFILE/KYC"
    const val AUTH_USER_CARD_KYC2 = "APP/PROFILE/KYC2"
    const val AUTH_USER_CARRIER = "APP/PROFILE/CARRIER"
    const val AUTH_USER_BANK_CARD_NEW="APP/PROFILE/BIND_CARD_CODE"
    
    const val JUMP_URL_PLEDGE_CONFIRM_SMS = "APP/Product/ConfirmTradeSms"
    const val JUMP_URL_PLEDGE_SIGN_CONTRACT = "APP/Product/SignContract"

    const val EMERGENCY_PARAM_NAME = "friendName"
    const val EMERGENCY_PARAM_MOBILE = "friendMobile"

    const val AUTH_STEP_CODE = "AUTH_STEP_CODE"

    const val INTENT_SELECTER_DATA = "INTENT_SELECTER_DATA"
    const val INTENT_SELECTER_NAME = "INTENT_SELECTER_NAME"
    const val INTENT_SELECTER_TYPE = "INTENT_SELECTER_TYPE"
    const val INTENT_SELECTER_LOGO = "INTENT_SELECTER_LOGO"

    const val INTENT_REPAY_OFFLINE_DATA = "INTENT_REPAY_OFFLINE_DATA"

    const val FACE_TYPE_ACCUAUTH = 1
    const val FACE_TYPE_FRONT = 2
    const val FACE_TYPE_BACK = 1

    const val FILE_UPLOAD_TYPE_OSS = 1
    const val FILE_UPLOAD_TYPE_AWS = 2
    const val FILE_UPLOAD_TYPE_NATIVE = 3
    const val FILE_UPLOAD_TYPE_HUAWEI = 4

    // 文本
    const val FILE_UPLOAD_FILE_TYPE_TEXT = 1
    // 图片
    const val FILE_UPLOAD_FILE_TYPE_IMAGE = 2

    //隐私协议
    const val PRIVACY_POLICY_YSZC = "yszc"
    //注册协议
    const val PRIVACY_POLICY_ZCXY = "zcxy"

    //已结清
    const val ORDER_STATUS_PAY_PAID = 2

    const val ORDER_STATUS_ORDER_REPAY = 5
    const val ORDER_STATUS_ORDER_OVERDUE = 6
    const val ORDER_STATUS_ORDER_PAID = 7

    const val BIND_CARD_TYPE_PRINCIPAL_INTEREST = 1
    const val BIND_CARD_TYPE_GUARANTEE = 2

    const val PAY_TYPE_PRINCIPAL_INTEREST = 10
    const val PAY_TYPE_GUARANTEE = 11

    const val HOME_BORROW_STATUS_CAN_BORROW = 13

    const val PAY_ACTION_TYPE_START = 1
    const val PAY_ACTION_TYPE_CONFIRM = 2

    const val PAY_STEPS_TYPE_INIT = 0
    const val PAY_STEPS_TYPE_START = 1
    const val PAY_STEPS_TYPE_ING = 2
    const val PAY_STEPS_TYPE_FINISHED = 3

    //0初始化 1支付处理中 2支付成功 3支付失败
    const val PAY_FINISHED_INIT = 0
    const val PAY_FINISHED_ING = 1
    const val PAY_FINISHED_SUCCESS = 2
    const val PAY_FINISHED_FAILED = 3

    const val WEB_FROM_OCR = "web_from_ocr"
}
