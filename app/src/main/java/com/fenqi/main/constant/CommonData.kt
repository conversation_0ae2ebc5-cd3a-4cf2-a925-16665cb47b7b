package com.fenqi.main.constant

import com.fenqi.main.bean.MainHomeCenterVoEntity
import com.fenqi.main.bean.PrivacyPolicyBean
import com.fenqi.main.page.authinfo.bindbank.bindnew.BindBankNewActivity

object CommonData {

    var privacyPolicyList:MutableList<PrivacyPolicyBean> = mutableListOf()
    var BIND_TYPE:String = BindBankNewActivity.BIND_BANK_TYPE_FIRST_ORDER_SUBMIT

    var HOME_STATUS:String = CommonConstant.STATUS_HOME_PRODUCT_SHORT_URL

    var centerVo: MainHomeCenterVoEntity? = null

    var IS_FIRST_OPEN = false
}