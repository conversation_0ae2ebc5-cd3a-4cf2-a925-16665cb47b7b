package com.fenqi.main.page.apply

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.text.TextUtils
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.postentity.VersusCauseKillDesignEntity
import com.fenqi.main.bean.OrderFlowVoItemBean
import com.fenqi.main.postentity.InterpretabilityLoopArmNothingEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.databinding.ActivityApplyNowBinding
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.view.dialog.AuthBackTipConfirmOrderDialog
import com.fenqi.main.view.dialog.BindCardConfirmSmsDialog
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.platformtools.utils.TitleBar

class ApplyNowActivity :
    BaseActivity<MainApiService, ApplyNowMvvmView, ApplyNowViewModel, ActivityApplyNowBinding>(),
    ApplyNowMvvmView {

    companion object {
        private const val INTENT_KEY_TRADE_NO = "INTENT_KEY_TRADE_NO"
        private const val INTENT_KEY_JUMP_TYPE = "INTENT_KEY_JUMP_TYPE"
        fun callIntent(activity: Activity?, tradeNo: String): Intent {
            val intent = Intent(activity, ApplyNowActivity::class.java)
            intent.putExtra(INTENT_KEY_TRADE_NO, tradeNo)
            return intent
        }
    }

    private var adapterTop: CommonTwoTextAdapter? = null
    private var adapterMiddle: CommonTwoTextAdapter? = null
    private var adapterBottom: CommonTwoTextAdapter? = null
    private var adapterFlow:ConfirmApplyAdapter?=null

    private var dialogBindCardConfirmSms: BindCardConfirmSmsDialog? = null

    override fun onShowViewById(): Int {
        return R.layout.activity_apply_now
    }

    override fun kinit() {
        TitleBar.changeTransparencyBar(activity)

        dataBinding?.click = this
        dataBinding?.viewModel = viewModel

        viewModel?.tradeNo = intent.getStringExtra(INTENT_KEY_TRADE_NO).toString()
        viewModel?.jumpType = intent.getStringExtra(INTENT_KEY_JUMP_TYPE).toString()

        initView()

        if (!TextUtils.isEmpty(viewModel?.tradeNo)) {
            showLoading()
            viewModel?.orderConfirmDetail()
        }
    }

    private fun initView() {
        val statusBarHeight = TitleBar.getStatusBarHeight(activity)
        dataBinding?.viewSbh?.layoutParams?.height = statusBarHeight

        adapterTop = CommonTwoTextAdapter(mutableListOf(), activity, object :
            BaseRecyclerViewAdapter.BaseRecyclerViewCallBack {
            override fun onClickListener(position: Int) {

            }
        })

        adapterMiddle = CommonTwoTextAdapter(mutableListOf(), activity, object :
            BaseRecyclerViewAdapter.BaseRecyclerViewCallBack {
            override fun onClickListener(position: Int) {

            }
        })
        adapterFlow = ConfirmApplyAdapter(mutableListOf(),activity, object :
            BaseRecyclerViewAdapter.BaseRecyclerViewCallBack {
            override fun onClickListener(position: Int) {
                clickOrderSubmit()
            }
        })

        adapterBottom = CommonTwoTextAdapter(mutableListOf(), activity, object :
            BaseRecyclerViewAdapter.BaseRecyclerViewCallBack {
            override fun onClickListener(position: Int) {

            }
        })

        dataBinding?.recyclerviewAnt?.apply {
            layoutManager = LinearLayoutManager(activity)
            setHasFixedSize(true)
            adapter = adapterTop
        }
        dataBinding?.recyclerviewAnm?.apply {
            layoutManager = LinearLayoutManager(activity)
            setHasFixedSize(true)
            adapter = adapterMiddle
        }
        dataBinding?.recyclerviewAnb?.apply {
            layoutManager = LinearLayoutManager(activity)
            setHasFixedSize(true)
            adapter = adapterBottom
        }

        dataBinding?.recyclerviewAnf?.apply {
            layoutManager = LinearLayoutManager(activity)
            setHasFixedSize(true)
            adapter = adapterFlow
        }

        dataBinding?.viewReturnBackAn?.setOnClickListener {
            showReturnBackView()
        }
    }

    private fun showReturnBackView(){
        val authBackTipDialog = AuthBackTipConfirmOrderDialog(activity)
        authBackTipDialog.setConfirmLogic {
            finish()
        }
        authBackTipDialog.showDialog()
    }


    fun clickOrderSubmit() {
        showLoading()
        viewModel?.orderAuthConfirmRequest()
    }

    override fun attachViewModel(): Class<ApplyNowViewModel> {
        return ApplyNowViewModel::class.java
    }

    override fun attachMvvmView(): ApplyNowMvvmView {
        return this
    }

    override fun attachTitleText(): String {
        return ""
    }

    @SuppressLint("SetTextI18n")
    override fun orderConfirmDetailSuccess(flowList:MutableList<OrderFlowVoItemBean>?) {
        dismissLoading()

        if (viewModel?.topTradeFieldDetails?.isNotEmpty() == true) {
            viewModel?.topTradeFieldDetails?.let {
                adapterTop?.addNewList(it)
            }
            dataBinding?.recyclerviewAnt?.visibility = View.VISIBLE
        } else {
            dataBinding?.recyclerviewAnt?.visibility = View.GONE
        }

        if (viewModel?.middleTradeFieldDetails?.isNotEmpty() == true) {
            viewModel?.middleTradeFieldDetails?.let {
                adapterMiddle?.addNewList(it)
            }
            dataBinding?.recyclerviewAnm?.visibility = View.VISIBLE
        } else {
            dataBinding?.recyclerviewAnm?.visibility = View.GONE
        }

        if (viewModel?.bottomTradeFieldDetails?.isNotEmpty() == true) {
            viewModel?.bottomTradeFieldDetails?.let {
                adapterBottom?.addNewList(it)
            }
            dataBinding?.recyclerviewAnb?.visibility = View.VISIBLE
        } else {
            dataBinding?.recyclerviewAnb?.visibility = View.GONE
        }

        flowList?.let {
            if(it.size>0){
                dataBinding?.recyclerviewAnf?.visibility = View.VISIBLE
                adapterFlow?.addNewList(it)
            }
        }
    }

    override fun orderAuthConfirmRequestSuccess() {
        viewModel?.orderConfirmDetail()
    }

    override fun jumpContract() {
        dialogBindCardConfirmSms?.clickDismiss()
        viewModel?.getContactUrl()
    }

    override fun jumpSms() {
        dismissLoading()
        if (TextUtils.isEmpty(viewModel?.flowId)) {
            viewModel?.payBindCardRequest()
        } else {
            val bindCardResendSmsPostBean = VersusCauseKillDesignEntity()
            bindCardResendSmsPostBean.flowId = viewModel?.flowId.toString()
            bindCardResendSmsPostBean.orderType = CommonConstant.BIND_CARD_TYPE_GUARANTEE
            viewModel?.payBindCardResendSms(bindCardResendSmsPostBean)
        }
    }

    override fun payBindCardRequestSuccess() {
        dismissLoading()

        if (dialogBindCardConfirmSms == null) {
            dialogBindCardConfirmSms = BindCardConfirmSmsDialog(activity)
            dialogBindCardConfirmSms?.setCancelAble(false)
            dialogBindCardConfirmSms?.setClickSubmitCallBack {
                showLoading()
                val userAuthBankInfoSavePostBean = InterpretabilityLoopArmNothingEntity()
                userAuthBankInfoSavePostBean.orderType = CommonConstant.BIND_CARD_TYPE_GUARANTEE
                userAuthBankInfoSavePostBean.flowId = viewModel?.flowId.toString()
                userAuthBankInfoSavePostBean.smsCode =
                    dialogBindCardConfirmSms?.getSmsCode().toString()

                viewModel?.payBindCardConfirm(userAuthBankInfoSavePostBean)
            }?.setResendCallBack {
                showLoading()
                val bindCardResendSmsPostBean = VersusCauseKillDesignEntity()
                bindCardResendSmsPostBean.flowId = viewModel?.flowId.toString()
                bindCardResendSmsPostBean.orderType = CommonConstant.BIND_CARD_TYPE_GUARANTEE
                viewModel?.payBindCardResendSms(bindCardResendSmsPostBean)
            }
        }
        dialogBindCardConfirmSms?.startTimer()
        dialogBindCardConfirmSms?.showDialog()
    }

    override fun verifyCodeSendSuccess() {
        dismissLoading()

        dialogBindCardConfirmSms?.startTimer()
        dialogBindCardConfirmSms?.showDialog()
    }

    override fun payBindCardConfirmSuccess() {
        dialogBindCardConfirmSms?.clickDismiss()

        viewModel?.getContactUrl()
    }

    override fun requestError(message: String, code: Int) {
        showToast(message)
        dismissLoading()
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {
        showLoading()
    }

    override fun onDestroy() {
        super.onDestroy()
        dialogBindCardConfirmSms?.stopTimer()
    }

    override fun onBackPressed() {
        showReturnBackView()
        return
    }
}