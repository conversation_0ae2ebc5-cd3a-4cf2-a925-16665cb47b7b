package com.fenqi.main.page.apply

import com.fenqi.main.bean.OrderFlowVoItemBean
import com.fenqi.main.presenter.base.MvvmView

interface ApplyNowMvvmView:MvvmView {
    fun orderConfirmDetailSuccess(flowList:MutableList<OrderFlowVoItemBean>?)
    fun orderAuthConfirmRequestSuccess()
    fun jumpContract()
    fun jumpSms()
    fun payBindCardRequestSuccess()
    fun verifyCodeSendSuccess()
    fun payBindCardConfirmSuccess()
}