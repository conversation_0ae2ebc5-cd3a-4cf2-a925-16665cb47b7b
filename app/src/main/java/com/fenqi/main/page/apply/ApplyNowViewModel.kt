package com.fenqi.main.page.apply

import android.text.TextUtils
import androidx.databinding.ObservableField
import com.fenqi.main.R
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.postentity.SurprisedHomelessEntity
import com.fenqi.main.postentity.VersusCauseKillDesignEntity
import com.fenqi.main.bean.BindCardResendSmsResponseBean
import com.fenqi.main.bean.CommonTwoTextListItemBean
import com.fenqi.main.bean.ContactUrlResponseBean
import com.fenqi.main.postentity.StarEagerEntity
import com.fenqi.main.bean.OrderAuthConfirmRequestResponseBean
import com.fenqi.main.bean.OrderConfirmDetailResponseBean
import com.fenqi.main.bean.PayBindCardConfirmResponseBean
import com.fenqi.main.bean.PayBindCardRequestResponseBean
import com.fenqi.main.postentity.InterpretabilityLoopArmNothingEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.page.web.CommonWebActivity
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.view.dialog.CommonMessageDialog

class ApplyNowViewModel: BaseViewModel<MainApiService, ApplyNowMvvmView>() {

    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }

    var maxAmount:ObservableField<String> = ObservableField<String>().apply { set("") }

    var tradeNo:String = ""
    var jumpType:String = ""

    var topTradeFieldDetails:MutableList<CommonTwoTextListItemBean> = mutableListOf()
    var middleTradeFieldDetails:MutableList<CommonTwoTextListItemBean> = mutableListOf()
    var bottomTradeFieldDetails:MutableList<CommonTwoTextListItemBean> = mutableListOf()

    var pledgeTitle:ObservableField<String?> = ObservableField<String?>().apply { set("") }
    var pledgeMoney:ObservableField<String?> = ObservableField<String?>().apply { set("") }
    var pledgeTips:ObservableField<String?> = ObservableField<String?>().apply { set("") }

    var flowId:String = ""

    var contactUrlResponseBean:ContactUrlResponseBean = ContactUrlResponseBean()

    fun orderConfirmDetail(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val detail: OrderConfirmDetailResponseBean = json.decodeFromString(it)
                    maxAmount.set(detail.maxAmount)
                    tradeNo = detail.tradeNo.toString()

                    detail.topTradeFieldDetails?.let {dataList->
                        topTradeFieldDetails= dataList
                    }
                    detail.middleTradeFieldDetails?.let {dataList->
                        middleTradeFieldDetails= dataList
                    }
                    detail.bottomTradeFieldDetails?.let {dataList->
                        bottomTradeFieldDetails= dataList
                    }

                    detail.pledgeDetailVO?.let { pledgeDetailVOData->
                        pledgeTitle.set(pledgeDetailVOData.title)
                        pledgeMoney.set(pledgeDetailVOData.pledgeMoney)
                        pledgeTips.set(pledgeDetailVOData.tips)
                    }
                    mvvmView?.orderConfirmDetailSuccess(detail.orderFlowVOList)
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.orderConfirmDetail(tradeNo)
        }
    }

    fun orderAuthConfirmRequest(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: OrderAuthConfirmRequestResponseBean = json.decodeFromString(it)
                    val jumpUrl = data.jumpUrl
                    if(jumpUrl == CommonConstant.JUMP_URL_PLEDGE_SIGN_CONTRACT){
                        mvvmView?.jumpContract()
                    }
                    if(jumpUrl == CommonConstant.JUMP_URL_PLEDGE_CONFIRM_SMS){
                        mvvmView?.jumpSms()
                    }
                    mvvmView?.orderAuthConfirmRequestSuccess()
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.orderAuthConfirmRequest(RetrofitBuilder.getInstance().getRequestBody(
                StarEagerEntity(tradeNo=tradeNo)
            ))
        }
    }

    fun payBindCardRequest(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: PayBindCardRequestResponseBean = json.decodeFromString(it)
                    if (!TextUtils.isEmpty(data.errorMsg)){
                        activity?.let { context->
                            val commonMessageDialog = CommonMessageDialog(context)
                            commonMessageDialog.setMessage(data.errorMsg.toString())
                            commonMessageDialog.setSingle()
                            commonMessageDialog.showDialog()
                        }
                        mvvmView?.dismissModelLoading()
                    } else {
                        flowId = data.flowId.toString()
                        mvvmView?.payBindCardRequestSuccess()
                    }
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.payBindCardRequest(RetrofitBuilder.getInstance().getRequestBody(
                SurprisedHomelessEntity(orderType = CommonConstant.BIND_CARD_TYPE_GUARANTEE)
            ))
        }
    }

    fun payBindCardResendSms(bindCardResendSmsPostBean: VersusCauseKillDesignEntity){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val response: BindCardResendSmsResponseBean = json.decodeFromString(it)
                    flowId = response.flowId.toString()
                    if(!TextUtils.isEmpty(response.errorMsg)){
                        mvvmView?.showModelToast(response.errorMsg.toString())
                    } else {
                        mvvmView?.verifyCodeSendSuccess()
                    }
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.payBindCardResendSms(RetrofitBuilder.getInstance().getRequestBody(bindCardResendSmsPostBean))
        }
    }

    fun payBindCardConfirm(dataValue: InterpretabilityLoopArmNothingEntity){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                if(it != null){
                    val data: PayBindCardConfirmResponseBean = json.decodeFromString(it)
                    if(TextUtils.isEmpty(data.errorMsg)){
                        mvvmView?.payBindCardConfirmSuccess()
                    } else {
                        mvvmView?.dismissModelLoading()
                        activity?.let { context->
                            val commonMessageDialog = CommonMessageDialog(context)
                            commonMessageDialog.setMessage(data.errorMsg.toString())
                            commonMessageDialog.setSingle()
                            commonMessageDialog.showDialog()
                        }
                    }
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.payBindCardConfirm(RetrofitBuilder.getInstance().getRequestBody(dataValue))
        }
    }

    fun getContactUrl(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                if(it != null){
                    val data: ContactUrlResponseBean = json.decodeFromString(it)
                    if(!TextUtils.isEmpty(data.errorMsg)){
                        activity?.let { context->
                            val commonMessageDialog = CommonMessageDialog(context)
                            commonMessageDialog.setMessage(data.errorMsg.toString())
                            commonMessageDialog.setSingle()
                            commonMessageDialog.showDialog()
                        }
                    } else {
                        contactUrlResponseBean = data
                        activity?.let { context->
                            if (TextUtils.isEmpty(contactUrlResponseBean.shortUrl)) {
                                mvvmView?.showModelToast(context.getString(R.string.get_contract_url_re_tip))
                            } else {
                                context.startActivity(
                                    CommonWebActivity.callIntent(
                                        context,
                                        context.getString(R.string.sign_contract_title),
                                        contactUrlResponseBean.shortUrl.toString()
                                    )
                                )
                            }
                        }

                    }
                }
                mvvmView?.dismissModelLoading()
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.getContactUrl(tradeNo)
        }
    }

}