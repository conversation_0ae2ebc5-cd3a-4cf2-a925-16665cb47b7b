package com.fenqi.main.page.apply

import android.annotation.SuppressLint
import android.content.Context
import android.view.View
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.bean.BorrowPurposeListItemBean
import com.fenqi.main.bean.CommonTwoTextListItemBean
import com.fenqi.main.databinding.ItemCommonTwoText2Binding

class CommonTwoTextAdapter(
    datas: MutableList<CommonTwoTextListItemBean>,
    context: Context,
    baseRecyclerViewCallBack: BaseRecyclerViewCallBack?
) : BaseRecyclerViewAdapter<CommonTwoTextListItemBean, ItemCommonTwoText2Binding>(
    datas,
    context,
    baseRecyclerViewCallBack
) {

    private var borrowPurposeList:MutableList<BorrowPurposeListItemBean> = mutableListOf()

    @SuppressLint("SetTextI18n", "NotifyDataSetChanged")
    override fun onBaseBindViewHolder(
        holder: BaseRecyclerViewHolder<ItemCommonTwoText2Binding>,
        position: Int
    ) {
        val orderListBean = datas[position]
        val dataBinding = holder.itemDataBinding

        dataBinding.tvCommonSpaceTwoStart.text = orderListBean.title
        dataBinding.tvCommonSpaceTwoEnd.text = orderListBean.value

        if(orderListBean.id == "borrowPurpose"){
            dataBinding.imageArrowItemCommonTwoText.visibility = View.VISIBLE
            dataBinding.viewItemCommonTwoTextContainer.setOnClickListener {
                if(borrowPurposeList.size>0){

                }
            }
        } else {
            dataBinding.imageArrowItemCommonTwoText.visibility = View.GONE
        }
    }

    fun setBorrowPurposeList(borrowPurposeList:MutableList<BorrowPurposeListItemBean>?){
        if(borrowPurposeList!=null){
            this.borrowPurposeList = borrowPurposeList
        }
    }

    override fun attachLayout(): Int {
        return R.layout.item_common_two_text2
    }

    private lateinit var selectCallBack: (desc:String,type:Int) -> Unit

    fun setSelectCallBack(selectCallBack: (desc:String,type:Int) -> Unit): CommonTwoTextAdapter {
        this.selectCallBack = selectCallBack
        return this
    }
}