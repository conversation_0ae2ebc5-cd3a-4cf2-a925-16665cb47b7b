package com.fenqi.main.page.apply

import android.annotation.SuppressLint
import android.content.Context
import androidx.core.content.ContextCompat
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.bean.OrderFlowVoItemBean
import com.fenqi.main.databinding.AdapterConfirmApplyBinding
import com.bumptech.glide.Glide

class ConfirmApplyAdapter(
    datas: MutableList<OrderFlowVoItemBean>,
    context: Context,
    baseRecyclerViewCallBack: BaseRecyclerViewCallBack?
) : BaseRecyclerViewAdapter<OrderFlowVoItemBean, AdapterConfirmApplyBinding>(
    datas,
    context,
    baseRecyclerViewCallBack
) {

    @SuppressLint("SetTextI18n", "NotifyDataSetChanged")
    override fun onBaseBindViewHolder(
        holder: BaseRecyclerViewHolder<AdapterConfirmApplyBinding>,
        position: Int
    ) {
        val flowVoItemBean = datas[position]
        val dataBinding = holder.itemDataBinding

        dataBinding.buttonAdapterConfirmApply.text = flowVoItemBean.flowName
        dataBinding.buttonAdapterConfirmApplySubmit.text = flowVoItemBean.buttonName
        Glide.with(context).load(flowVoItemBean.flowLogo).into(dataBinding.imgAdapterConfirmApply)

        if(flowVoItemBean.buttonStatus == 0){
            dataBinding.viewAdapterConfirmApplyContainer.setOnClickListener {
                baseRecyclerViewCallBack?.onClickListener(position)
            }
            dataBinding.buttonAdapterConfirmApplySubmit.setBackgroundResource(R.drawable.shape_confirm_apply_button)
            dataBinding.buttonAdapterConfirmApplySubmit.setTextColor(ContextCompat.getColor(context,R.color.color_confirm_button))
        } else {
            dataBinding.buttonAdapterConfirmApplySubmit.setBackgroundResource(0)
            dataBinding.buttonAdapterConfirmApplySubmit.setTextColor(ContextCompat.getColor(context,R.color.color_999999))
        }
    }

    override fun attachLayout(): Int {
        return R.layout.adapter_confirm_apply
    }
}