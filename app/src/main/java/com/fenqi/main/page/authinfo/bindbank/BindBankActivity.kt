package com.fenqi.main.page.authinfo.bindbank

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.widget.RelativeLayout
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.Gson
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.bean.SelectVoEntity
import com.fenqi.main.bean.UserInfoItemEntity
import com.fenqi.main.bean.UserInfoVoEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.databinding.ActivityBindBankBinding
import com.fenqi.main.page.authinfo.personal.PersonalInfoAdapter
import com.fenqi.main.page.selecter.SelecterActivity
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.util.AuthJumpUtil
import com.fenqi.main.view.dialog.AuthBackTipDialog

class BindBankActivity :
    BaseActivity<MainApiService, BindBankMvvmView, BindBankViewModel, ActivityBindBankBinding>(),
    BindBankMvvmView {

    companion object {
        fun callIntent(context: Context, code: String): Intent {
            val intent = Intent(context, BindBankActivity::class.java)
            intent.putExtra(CommonConstant.AUTH_STEP_CODE, code)
            return intent
        }
    }

    private lateinit var personalInfoAdapter: PersonalInfoAdapter
    private lateinit var launcherActivity: ActivityResultLauncher<Intent>

    private var firstPosition = 0
    private var subPosition = 0

    override fun onShowViewById(): Int {
        return R.layout.activity_bind_bank
    }

    override fun kinit() {
        dataBinding?.viewModel = viewModel
        dataBinding?.click = this

        val relateArrow: RelativeLayout = findViewById(R.id.view_base_title_return_back)
        relateArrow.setOnClickListener {
            showReturnBackView()
        }

        initLauncher()
        initView()

        viewModel?.code = intent.getStringExtra(CommonConstant.AUTH_STEP_CODE).toString()

        viewModel?.userBankInfoGet()
    }

    private fun showReturnBackView() {
        if (viewModel?.finished == true) {
            finish()
        } else {
            val authBackTipDialog = AuthBackTipDialog(activity)
            authBackTipDialog.setConfirmLogic {
                finish()
            }
            authBackTipDialog.showDialog()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initLauncher() {
        launcherActivity =
            registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
                if (result.resultCode == RESULT_OK) {
                    val intent = result.data
                    if (intent != null) {
                        val name = intent.getStringExtra(CommonConstant.INTENT_SELECTER_NAME)
                        val selectType = intent.getIntExtra(CommonConstant.INTENT_SELECTER_TYPE, 0)
                        val userInfoVoEntityList: MutableList<UserInfoVoEntity> =
                            personalInfoAdapter.datas
                        userInfoVoEntityList[firstPosition].inputParams?.get(subPosition)?.inputValue =
                            name.toString()
                        personalInfoAdapter.notifyDataSetChanged()
                    }
                }
            }
    }

    private fun initView() {
        personalInfoAdapter = PersonalInfoAdapter(mutableListOf(), activity, object :
            BaseRecyclerViewAdapter.BaseRecyclerViewCallBack {
            override fun onClickListener(position: Int) {

            }
        })
        dataBinding?.recyclerviewBb?.apply {
            layoutManager = LinearLayoutManager(activity)
            setHasFixedSize(true)
            adapter = personalInfoAdapter
        }
        personalInfoAdapter.setContactInfoSubsetCallBack { firstPosition, subPosition, type ->
            this.firstPosition = firstPosition
            this.subPosition = subPosition
            val userInfoVoEntityList: MutableList<UserInfoVoEntity> = personalInfoAdapter.datas
            val selectVoEntityList =
                userInfoVoEntityList[firstPosition].inputParams?.get(subPosition)?.selectVo
            launcherActivity.launch(
                SelecterActivity.callIntent(
                    activity,
                    Gson().toJson(selectVoEntityList)
                )
            )
        }
    }

    fun clickSubmit() {
        showLoading()
        viewModel?.userAuthBankSave(personalInfoAdapter.datas)
    }

    override fun attachViewModel(): Class<BindBankViewModel> {
        return BindBankViewModel::class.java
    }

    override fun attachMvvmView(): BindBankMvvmView {
        return this
    }

    override fun attachTitleText(): String {
        return getString(R.string.bind_bank_card)
    }

    override fun userBankInfoGetSuccess(userInfoVoEntityList: MutableList<UserInfoVoEntity>) {
        for (userInfoVoEntity: UserInfoVoEntity in userInfoVoEntityList) {
            val inputParams = userInfoVoEntity.inputParams
            inputParams?.let {
                for (userInfoItemEntity: UserInfoItemEntity in inputParams) {
                    if ((userInfoItemEntity.paramType == CommonConstant.DATA_VIEW_STATUS_PICKER || userInfoItemEntity.paramType == CommonConstant.DATA_VIEW_STATUS_BANK_NAME_SELECTER) && !TextUtils.isEmpty(
                            userInfoItemEntity.inputValue
                        )
                    ) {
                        userInfoItemEntity.selectVo?.let {
                            for (selectVoItem: SelectVoEntity in it) {
                                if (selectVoItem.type.toString() == userInfoItemEntity.inputValue) {
                                    userInfoItemEntity.inputValue = selectVoItem.name
                                    break
                                }
                            }
                        }
                    }
                }
            }
        }
        personalInfoAdapter.addNewList(userInfoVoEntityList)
    }

    override fun userAuthBankSaveSuccess() {
        showToast(getString(R.string.person_info_success_tip))

        AuthJumpUtil.getInstance().setFinishedCallBack {
            dismissLoading()
            finish()
        }.setOnFailed {
            dismissLoading()
        }.judgeJump(activity)
    }

    override fun requestError(message: String, code: Int) {
        dismissLoading()
        showToast(message)
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {
        showLoading()
    }

    override fun onBackPressed() {
        if (viewModel?.finished == false) {
            showReturnBackView()
            return
        }
        super.onBackPressed()
    }
}