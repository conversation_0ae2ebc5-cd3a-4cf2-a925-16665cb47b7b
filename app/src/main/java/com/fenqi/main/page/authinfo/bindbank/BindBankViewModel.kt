package com.fenqi.main.page.authinfo.bindbank

import android.text.TextUtils
import com.google.gson.JsonObject
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.bean.UserInfoItemEntity
import com.fenqi.main.bean.UserInfoVoEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService

class BindBankViewModel: BaseViewModel<MainApiService, BindBankMvvmView>() {
    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }

    lateinit var code:String
    var finished:Boolean = false

    fun userBankInfoGet(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                if(it!=null){
                    val data: MutableList<UserInfoVoEntity> = json.decodeFromString(it)
                    if(data.size>0){
                        var hasNoFinished = false
                        for (item in data){
                            if(!item.finished){
                                hasNoFinished = true
                                break
                            }
                        }
                        finished = !hasNoFinished
                    }
                    mvvmView?.userBankInfoGetSuccess(data)
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.userAuthProfileInfoGet()
        }
    }

    fun userAuthBankSave(dataValue:MutableList<UserInfoVoEntity>){
        var canNext = true
        for(userInfoVoEntity: UserInfoVoEntity in dataValue){
            val inputParams = userInfoVoEntity.inputParams
            var hasError = false
            inputParams?.let {
                for(userInfoItemEntity: UserInfoItemEntity in inputParams){
                    if(userInfoItemEntity.show && userInfoItemEntity.requied && TextUtils.isEmpty(userInfoItemEntity.inputValue)){
                        mvvmView?.showModelToast("${userInfoItemEntity.inputDesc}${userInfoItemEntity.paramName}")
                        hasError = true
                        canNext = false
                        break
                    }
                }
            }
            if(hasError){
                break
            }
        }
        if(canNext){
            val jsonObject = JsonObject()
            for(userInfoVoEntity: UserInfoVoEntity in dataValue){
                val inputParams = userInfoVoEntity.inputParams
                inputParams?.let {
                    for(userInfoItemEntity: UserInfoItemEntity in inputParams){
                        if(userInfoItemEntity.paramType == CommonConstant.DATA_VIEW_STATUS_PICKER || userInfoItemEntity.paramType == CommonConstant.DATA_VIEW_STATUS_BANK_NAME_SELECTER){
                            val selectValue = userInfoItemEntity.inputValue
                            userInfoItemEntity.selectVo?.let {
                                var selectType = ""
                                if(it.size>0){
                                    for(index in it.indices){
                                        if(it[index].name == selectValue){
                                            selectType = it[index].type.toString()
                                        }
                                    }
                                }
                                jsonObject.addProperty(userInfoItemEntity.param,selectType)
                            }
                        } else {
                            jsonObject.addProperty(userInfoItemEntity.param,userInfoItemEntity.inputValue)
                        }
                    }
                }
            }

            RetrofitBuilder.getInstance().start({
                onSuccess {
                    mvvmView?.userAuthBankSaveSuccess()
                }
                onFailure { msg, code ->
                    mvvmView?.requestError(msg,code)
                }
            }) {
                mService?.userAuthBankSave(RetrofitBuilder.getInstance().getRequestBody(jsonObject))
            }
        } else {
            mvvmView?.dismissModelLoading()
        }
    }
}