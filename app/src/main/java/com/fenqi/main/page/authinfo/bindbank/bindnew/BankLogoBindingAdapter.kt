package com.fenqi.main.page.authinfo.bindbank.bindnew

import android.annotation.SuppressLint
import android.text.TextUtils
import android.widget.ImageView
import android.widget.TextView
import androidx.databinding.BindingAdapter
import com.bumptech.glide.Glide

object BankLogoBindingAdapter {

    @BindingAdapter("set_bank_logo")@JvmStatic
    fun setBankLogo(imageView: ImageView,url:String?){
        if(!TextUtils.isEmpty(url)){
            Glide.with(imageView.context).load(url).into(imageView)
        }
    }
}