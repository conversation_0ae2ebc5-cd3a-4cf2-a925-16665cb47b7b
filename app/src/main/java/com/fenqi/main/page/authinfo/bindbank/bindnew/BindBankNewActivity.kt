package com.fenqi.main.page.authinfo.bindbank.bindnew

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.databinding.Observable
import com.google.gson.Gson
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.postentity.AccidentallyPasswordFriendshipAwareEntity
import com.fenqi.main.postentity.MixStrangeGirlfriendEntity
import com.fenqi.main.postentity.InterpretabilityLoopArmNothingEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.databinding.ActivityBindBankNewBinding
import com.fenqi.main.event.MainEvent
import com.fenqi.main.page.selecter.SelecterActivity
import com.fenqi.main.page.webprivacy.WebPrivacyActivity
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.util.LogUtil
import com.fenqi.main.util.event.EventConstants
import com.fenqi.main.view.OtpView
import com.fenqi.main.view.dialog.AuthBackTipDialog

class BindBankNewActivity:
    BaseActivity<MainApiService, BindBankNewMvvmView, BindBankNewViewModel, ActivityBindBankNewBinding>(),BindBankNewMvvmView {

    companion object {

        const val BIND_BANK_TYPE_FIRST_ORDER_SUBMIT = "FIRST_ORDER_SUBMIT"
        const val BIND_BANK_TYPE_ORDER_SUBMIT = "ORDER_SUBMIT"
        const val BIND_BANK_TYPE_BORROW_FAILED_RE_BIND = "BORROW_FAILED_RE_BIND"
        const val BIND_BANK_TYPE_RE_BIND = "RE_BIND"

        private const val INTENT_KEY_BIND_TYPE = "INTENT_KEY_BIND_TYPE"
        private const val INTENT_KEY_BIND_PAY_TYPE = "INTENT_KEY_BIND_PAY_TYPE"

        fun callIntent(context: Context,type:String): Intent {
            val intent = Intent(context, BindBankNewActivity::class.java)
            intent.putExtra(INTENT_KEY_BIND_TYPE,type)
            return intent
        }

        fun callIntent(context: Context,type:String,payType:Int): Intent {
            val intent = Intent(context, BindBankNewActivity::class.java)
            intent.putExtra(INTENT_KEY_BIND_TYPE,type)
            intent.putExtra(INTENT_KEY_BIND_PAY_TYPE,payType)
            return intent
        }
    }

    private lateinit var launcherActivity:ActivityResultLauncher<Intent>

    override fun onShowViewById(): Int {
        return R.layout.activity_bind_bank_new
    }

    override fun kinit() {
        dataBinding?.viewModel = viewModel
        dataBinding?.click = this

        val relateArrow: RelativeLayout = findViewById(R.id.view_base_title_return_back)
        relateArrow.setOnClickListener {
            if(viewModel?.bindType == BIND_BANK_TYPE_BORROW_FAILED_RE_BIND || viewModel?.bindType == BIND_BANK_TYPE_RE_BIND){
                finish()
            } else {
                showReturnBackView()
            }
        }

        initLauncher()
        initView()

        viewModel?.bindType = intent.getStringExtra(INTENT_KEY_BIND_TYPE).toString()
        viewModel?.payType = intent.getIntExtra(INTENT_KEY_BIND_PAY_TYPE,CommonConstant.BIND_CARD_TYPE_PRINCIPAL_INTEREST)

        if(viewModel?.bindType == BIND_BANK_TYPE_BORROW_FAILED_RE_BIND || viewModel?.bindType == BIND_BANK_TYPE_RE_BIND){
            val textView:TextView = findViewById(R.id.text_base_title)
            textView.text = getString(R.string.re_bind_bank_card)
        }

        showLoading()
        viewModel?.supportBankList()
//        viewModel?.getPrivacyPolicyList(mutableListOf(CommonConstant.PRIVACY_POLICY_ZHWTKKSQS))
    }

    private fun showReturnBackView(){
        if(viewModel?.finished == true){
            finish()
        } else {
            val authBackTipDialog = AuthBackTipDialog(activity)
            authBackTipDialog.setConfirmLogic {
                finish()
            }
            authBackTipDialog.showDialog()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initLauncher() {
        launcherActivity = registerForActivityResult(ActivityResultContracts.StartActivityForResult()){ result->
            if (result.resultCode == RESULT_OK){
                val intent = result.data
                if(intent != null){
                    val name = intent.getStringExtra(CommonConstant.INTENT_SELECTER_NAME)
                    val selectType = intent.getStringExtra(CommonConstant.INTENT_SELECTER_TYPE)
                    val logo = intent.getStringExtra(CommonConstant.INTENT_SELECTER_LOGO)

                    viewModel?.bankCode?.set(selectType.toString())
                    viewModel?.bankName?.set(name.toString())
                    viewModel?.bankLogo?.set(logo)

                    viewModel?.bankName?.set(name)
                }
            }
        }
    }

    private fun initView() {
        viewModel?.bankNumber?.addOnPropertyChangedCallback(object: Observable.OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                viewModel?.bankNumber?.get()?.length?.let {
                    if(it>1){
                        viewModel?.showClose?.set(true)
                    } else {
                        viewModel?.showClose?.set(false)
                    }
                }
            }
        })

        dataBinding?.otpviewBbn?.setFinishedCallBack {
//            dataBinding?.viewBindBankOtpViewContainer?.setBackgroundResource(R.drawable.shape_main_grante_big_radius)
            dataBinding?.viewBbovContainer?.isClickable = true
        }


        val agreeText = "        我已阅读并同意《账户委托扣款授权书》"
        val agreeColor = ContextCompat.getColor(activity,R.color.color_config_main)

        val spannableStringBuilder = SpannableStringBuilder()
        spannableStringBuilder.append(agreeText)

        val clickableSpan1: ClickableSpan = object : ClickableSpan() {
            override fun onClick(view: View) {
                viewModel?.privacyBeanList?.let {
                    if(it.size>0){
                        startActivity(WebPrivacyActivity.callIntent(activity,0))
                    }
                }
            }
        }

        val start1 = 15

        spannableStringBuilder.setSpan(
            clickableSpan1,
            start1, agreeText.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        val foregroundColorSpan1 = ForegroundColorSpan(agreeColor)

        spannableStringBuilder.setSpan(
            foregroundColorSpan1,
            start1, agreeText.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

    }

    fun clickSendCode(){
        if(viewModel?.isAgree?.get() == false){
            showToast(getString(R.string.agreement_tip))
            return
        }
        if(TextUtils.isEmpty(viewModel?.bankNumber?.get())){
            showToast(getString(R.string.common_input_tip))
            return
        }
        if(TextUtils.isEmpty(viewModel?.bankName?.get())){
            showToast(getString(R.string.common_input_tip))
            return
        }
        if(TextUtils.isEmpty(viewModel?.bankPhoneNumber?.get())){
            showToast(getString(R.string.common_input_tip))
            return
        }

        showLoading()

        val bindCardPostBean = AccidentallyPasswordFriendshipAwareEntity()
        bindCardPostBean.cardNo = viewModel?.bankNumber?.get().toString()
        bindCardPostBean.mobile = viewModel?.bankPhoneNumber?.get().toString()
        bindCardPostBean.orderType = viewModel?.payType
        viewModel?.payBindCardRequest(bindCardPostBean)
    }

    fun clickSubmit(){
        if(viewModel?.isAgree?.get() == false){
            showToast(getString(R.string.agreement_tip))
            return
        }
        if(TextUtils.isEmpty(viewModel?.bankNumber?.get())){
            showToast(getString(R.string.common_input_tip))
            return
        }
        if(TextUtils.isEmpty(viewModel?.bankName?.get())){
            showToast(getString(R.string.common_input_tip))
            return
        }
        if(TextUtils.isEmpty(viewModel?.bankPhoneNumber?.get())){
            showToast(getString(R.string.common_input_tip))
            return
        }
        if(TextUtils.isEmpty(viewModel?.bankVerifyCode?.get())){
            showToast(getString(R.string.common_input_tip))
            return
        }

        showLoading()

        val userAuthBankInfoSavePostBean = InterpretabilityLoopArmNothingEntity()

        userAuthBankInfoSavePostBean.orderType = viewModel?.payType
        userAuthBankInfoSavePostBean.flowId = viewModel?.flowId.toString()
        userAuthBankInfoSavePostBean.smsCode = viewModel?.bankVerifyCode?.get().toString()

        viewModel?.userAuthBankSave(userAuthBankInfoSavePostBean)
    }

    fun clickCloseInput(){
        viewModel?.showClose?.set(false)
        viewModel?.bankNumber?.set("")
    }

    fun clickGetCardBin(){
        if(!TextUtils.isEmpty(viewModel?.bankNumber?.get())){
            showLoading()
            viewModel?.payCardBin(MixStrangeGirlfriendEntity(cardNo = viewModel?.bankNumber?.get().toString()))
        } else {
            if(viewModel?.bankRoList?.isNotEmpty() == true){
                viewModel?.bankRoList?.let {
                    launcherActivity.launch(SelecterActivity.callIntent(activity,Gson().toJson(viewModel?.bankRoList)))
                }
            }
        }
    }

    override fun attachViewModel(): Class<BindBankNewViewModel> {
        return BindBankNewViewModel::class.java
    }

    override fun attachMvvmView(): BindBankNewMvvmView {
        return this
    }

    override fun attachTitleText(): String {
        return getString(R.string.bind_bank_card)
    }

    override fun verifyCodeSendSuccess(){
        dismissLoading()

        dataBinding?.otpviewBbn?.startTimer(OtpView.CODE_DEFAULT_TIME)
//        dataBinding?.viewBindBankOtpViewContainer?.setBackgroundResource(R.drawable.shape_bigr_common_bg)
        dataBinding?.viewBbovContainer?.isClickable = false
    }

    override fun userAuthBankSaveSuccess() {
        when(viewModel?.bindType){
            BIND_BANK_TYPE_FIRST_ORDER_SUBMIT->{
                viewModel?.orderConfirmSubmit()
            }
            BIND_BANK_TYPE_ORDER_SUBMIT->{
                viewModel?.orderConfirmSubmit()
            }
            BIND_BANK_TYPE_BORROW_FAILED_RE_BIND->{
                eventBus.post(MainEvent(event = EventConstants.EVENT_RE_PAY))
                finish()
            }
            BIND_BANK_TYPE_RE_BIND->{
                eventBus.post(MainEvent(event = EventConstants.EVENT_RE_PAY))
                finish()
            }
        }
    }

    override fun selectBank() {
        dismissLoading()
        if(viewModel?.bankRoList?.isNotEmpty() == true){
            viewModel?.bankRoList?.let {
                launcherActivity.launch(SelecterActivity.callIntent(activity,Gson().toJson(viewModel?.bankRoList)))
            }
        }
    }

    override fun orderSubmitConfirmSuccess() {
        dismissLoading()
        finish()
    }

    override fun requestError(message: String, code: Int) {
        dismissLoading()
        showToast(message)
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {
        showLoading()
    }

    override fun onBackPressed() {
        if (viewModel?.finished == false && viewModel?.bindType != BIND_BANK_TYPE_BORROW_FAILED_RE_BIND && viewModel?.bindType != BIND_BANK_TYPE_RE_BIND) {
            showReturnBackView()
            return
        }
        super.onBackPressed()
    }

    override fun onDestroy() {
        super.onDestroy()
        dataBinding?.otpviewBbn?.stopTimer()
    }
}