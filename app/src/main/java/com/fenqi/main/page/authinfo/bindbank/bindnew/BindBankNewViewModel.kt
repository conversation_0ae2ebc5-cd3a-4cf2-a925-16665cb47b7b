package com.fenqi.main.page.authinfo.bindbank.bindnew

import android.text.TextUtils
import androidx.databinding.ObservableField
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.bean.BankSupportListBean
import com.fenqi.main.postentity.AccidentallyPasswordFriendshipAwareEntity
import com.fenqi.main.postentity.VersusCauseKillDesignEntity
import com.fenqi.main.bean.BindCardResendSmsResponseBean
import com.fenqi.main.bean.BindCardResponseBean
import com.fenqi.main.postentity.MixStrangeGirlfriendEntity
import com.fenqi.main.bean.CardBinResponseBean
import com.fenqi.main.postentity.FlushPrayCarryTravelEntity
import com.fenqi.main.bean.PayBindCardConfirmResponseBean
import com.fenqi.main.bean.PrivacyPolicyBean
import com.fenqi.main.bean.UserAuthBankInfoDetailEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.postentity.InterpretabilityLoopArmNothingEntity
import com.fenqi.main.constant.CommonData
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.view.dialog.CommonMessageDialog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class BindBankNewViewModel: BaseViewModel<MainApiService, BindBankNewMvvmView>() {

    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }

    var bankNumber: ObservableField<String> = ObservableField<String>().apply { set("") }
    var bankName: ObservableField<String> = ObservableField<String>().apply { set("") }
    var bankLogo: ObservableField<String> = ObservableField<String>().apply { set("") }

    var bankCode: ObservableField<String> = ObservableField<String>().apply { set("") }
    var bankPhoneNumber: ObservableField<String> = ObservableField<String>().apply { set("") }
    var bankVerifyCode: ObservableField<String> = ObservableField<String>().apply { set("") }

    var bindCardResponseBean:BindCardResponseBean = BindCardResponseBean()

    var userAuthBankInfoDetailEntity:UserAuthBankInfoDetailEntity = UserAuthBankInfoDetailEntity()

    var needHolderName: ObservableField<Boolean> = ObservableField<Boolean>().apply { set(false) }
    var name: ObservableField<String> = ObservableField<String>().apply { set("") }

    var showClose: ObservableField<Boolean> = ObservableField<Boolean>().apply { set(false) }

    var flowId:String = ""

    var bindType:String = BindBankNewActivity.BIND_BANK_TYPE_FIRST_ORDER_SUBMIT
    var finished:Boolean = false

    var bankRoList:MutableList<CardBinResponseBean>? = mutableListOf()

    var firstBankNo:String = ""
    var payType:Int = CommonConstant.BIND_CARD_TYPE_PRINCIPAL_INTEREST

    fun payBindCardRequest(bindCardPostBean: AccidentallyPasswordFriendshipAwareEntity){
        bindCardPostBean.bankName = bankName.get().toString()
        bindCardPostBean.bankCode = bankCode.get().toString()
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val bindCardResponse: BindCardResponseBean = json.decodeFromString(it)
                    if(!TextUtils.isEmpty(bindCardResponse.errorMsg)){
                        activity?.let { context->
                            val commonMessageDialog = CommonMessageDialog(context)
                            commonMessageDialog.setMessage(bindCardResponse.errorMsg.toString())
                            commonMessageDialog.setSingle()
                            commonMessageDialog.showDialog()
                        }
                        mvvmView?.dismissModelLoading()
                    } else {
                        bindCardResponseBean = bindCardResponse
                        flowId = bindCardResponse.flowId.toString()
                        mvvmView?.verifyCodeSendSuccess()
                    }
                }
                firstBankNo = bankNumber.get().toString()
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.payBindCardRequest(RetrofitBuilder.getInstance().getRequestBody(bindCardPostBean))
        }
    }

    fun payBindCardResendSms(bindCardResendSmsPostBean: VersusCauseKillDesignEntity){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val response: BindCardResendSmsResponseBean = json.decodeFromString(it)
                    if(!TextUtils.isEmpty(response.errorMsg)){
                        activity?.let { context->
                            val commonMessageDialog = CommonMessageDialog(context)
                            commonMessageDialog.setMessage(response.errorMsg.toString())
                            commonMessageDialog.setSingle()
                            commonMessageDialog.showDialog()
                        }
                        mvvmView?.dismissModelLoading()
                    } else {
                        flowId = response.flowId.toString()
                        mvvmView?.verifyCodeSendSuccess()
                    }
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.payBindCardResendSms(RetrofitBuilder.getInstance().getRequestBody(bindCardResendSmsPostBean))
        }
    }

    fun payCardBin(payCardBin: MixStrangeGirlfriendEntity){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                if(it != null){
                    val data: CardBinResponseBean = json.decodeFromString(it)
                    if(!TextUtils.isEmpty(data.bankName)){
                        bankName.set(data.bankName)
                    }
                    if(!TextUtils.isEmpty(data.bankCode)){
                        bankCode.set(data.bankCode)
                    } else {
                        mvvmView?.selectBank()
                    }
                } else {
                    mvvmView?.selectBank()
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.payCardBin(RetrofitBuilder.getInstance().getRequestBody(payCardBin))
        }
    }

    fun userAuthBankSave(dataValue: InterpretabilityLoopArmNothingEntity){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                if(it != null){
                    val data: PayBindCardConfirmResponseBean = json.decodeFromString(it)
                    if(TextUtils.isEmpty(data.errorMsg)){
                        mvvmView?.userAuthBankSaveSuccess()
                    } else {
                        mvvmView?.dismissModelLoading()
                        activity?.let { context->
                            val commonMessageDialog = CommonMessageDialog(context)
                            commonMessageDialog.setMessage(data.errorMsg.toString())
                            commonMessageDialog.setSingle()
                            commonMessageDialog.showDialog()
                        }
                    }
                } else {
                    mvvmView?.userAuthBankSaveSuccess()
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.payBindCardConfirm(RetrofitBuilder.getInstance().getRequestBody(dataValue))
        }
    }

    fun supportBankList(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: BankSupportListBean = json.decodeFromString(it)
                    bankRoList = data.bankRoList
                }
                mvvmView?.dismissModelLoading()
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.supportBankList()
        }
    }

    fun orderConfirmSubmit() {
        RetrofitBuilder.getInstance().start({
            onSuccess {
                mvvmView?.orderSubmitConfirmSuccess()
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg, code)
            }
        }) {
            mService?.orderSubmitConfirm(
                RetrofitBuilder.getInstance().getRequestBody(FlushPrayCarryTravelEntity(productCode=""))
            )
        }
    }


    var privacyBeanList: MutableList<PrivacyPolicyBean> = mutableListOf()
    var isAgree: ObservableField<Boolean> = ObservableField<Boolean>().apply { set(true) }
    fun getPrivacyPolicyList(keyList: MutableList<String>){
        privacyBeanList.clear()

        var tag = 0

        for (index in keyList.indices) {
            val key = keyList[index]
            if (!TextUtils.isEmpty(key)) {
                CoroutineScope(Dispatchers.IO).launch {
                    delay(100)
                    RetrofitBuilder.getInstance().start({
                        onSuccess {
                            it?.let {
                                val data: PrivacyPolicyBean = json.decodeFromString(it)
                                tag++
                                data.let { privacyBean ->
                                    privacyBeanList.add(privacyBean)
                                }
                                if(tag>=keyList.size){
                                    CommonData.privacyPolicyList = privacyBeanList
                                }
                            }

                        }
                        onFailure { _, _ ->
                            tag++
                            if(tag>=keyList.size){
                                CommonData.privacyPolicyList = privacyBeanList
                            }
                        }
                    }) {
                        mService?.userProtocolInfos(key)
                    }
                }
            }
        }
    }
}