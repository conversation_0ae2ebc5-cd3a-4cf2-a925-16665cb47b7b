package com.fenqi.main.page.authinfo.bindbank.cardlist

import android.content.Context
import android.content.Intent
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.constant.CommonConstant.AUTH_STEP_CODE
import com.fenqi.main.constant.CommonData
import com.fenqi.main.databinding.ActivityBankCardListBinding
import com.fenqi.main.page.authinfo.bindbank.bindnew.BindBankNewActivity
import com.fenqi.main.page.authinfo.bindbank.bindnew.BindBankNewActivity.Companion.BIND_BANK_TYPE_RE_BIND
import com.fenqi.main.retrofit.api.MainApiService

class BankCardListActivity:
    BaseActivity<MainApiService, BankCardListMvvmView, BankCardListViewModel, ActivityBankCardListBinding>(),BankCardListMvvmView {

    companion object {
        fun callIntent(context: Context,code:String): Intent {
            val intent = Intent(context, BankCardListActivity::class.java)
            intent.putExtra(AUTH_STEP_CODE,code)
            return intent
        }
    }

    private var adapterBankCardList:BankCardListAdapter? = null

    override fun onShowViewById(): Int {
        return R.layout.activity_bank_card_list
    }

    override fun onResume() {
        super.onResume()

        showLoading()
        viewModel?.bindCardListGet()
    }

    override fun kinit() {
        dataBinding?.viewModel = viewModel
        dataBinding?.click = this

        viewModel?.type = intent.getStringExtra(AUTH_STEP_CODE).toString()

        if(viewModel?.type == BindBankNewActivity.BIND_BANK_TYPE_RE_BIND){

        } else {

        }
        initView()
    }

    private fun initView() {
        if(viewModel?.type == BindBankNewActivity.BIND_BANK_TYPE_ORDER_SUBMIT){
            val textView:TextView = findViewById(R.id.text_base_title)
            textView.text = getString(R.string.confirm_bank_card_info)
        }
        if(viewModel?.type == BindBankNewActivity.BIND_BANK_TYPE_RE_BIND){
            val textView:TextView = findViewById(R.id.text_base_title)
            textView.text = getString(R.string.my_bank_card_list)
        }

        adapterBankCardList = BankCardListAdapter(mutableListOf(),activity,object :
            BaseRecyclerViewAdapter.BaseRecyclerViewCallBack{
            override fun onClickListener(position: Int) {

            }
        })

        dataBinding?.recyclerviewBcl?.apply {
            layoutManager = LinearLayoutManager(activity)
            setHasFixedSize(true)
            adapter = adapterBankCardList
        }

        val view:View = findViewById(R.id.view_base_title_end_container)
        val textView:TextView = findViewById(R.id.text_base_title_end_text)
        textView.text = getString(R.string.re_bind)

        view.visibility = View.VISIBLE
        view.setOnClickListener {
            clickChangeCard()
        }
    }

    fun clickSubmit(){
        showLoading()
        viewModel?.orderConfirmSubmit()
    }

    fun clickChangeCard(){
        startActivity(BindBankNewActivity.callIntent(activity,BIND_BANK_TYPE_RE_BIND))
    }

    override fun bindCardListGetSuccess() {
        dismissLoading()
        if(viewModel?.bankRoList?.isNotEmpty() == false){
            startActivity(BindBankNewActivity.callIntent(activity,CommonData.BIND_TYPE))
        } else {
            viewModel?.bankRoList?.let {
                adapterBankCardList?.addNewList(it)
            }
        }
    }

    override fun orderSubmitConfirmSuccess() {
        dismissLoading()
        finish()
    }

    override fun attachViewModel(): Class<BankCardListViewModel> {
        return BankCardListViewModel::class.java
    }

    override fun attachMvvmView(): BankCardListMvvmView {
        return this
    }

    override fun attachTitleText(): String {
        return getString(R.string.bind_bank_card)
    }


    override fun requestError(message: String, code: Int) {
        dismissLoading()
        showToast(message)
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {
        showLoading()
    }
}