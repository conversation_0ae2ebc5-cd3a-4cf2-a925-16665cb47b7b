package com.fenqi.main.page.authinfo.bindbank.cardlist

import android.annotation.SuppressLint
import android.content.Context
import com.bumptech.glide.Glide
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.bean.BankCardListItemBean
import com.fenqi.main.databinding.ItemBankCardListBinding

class BankCardListAdapter(
    datas: MutableList<BankCardListItemBean>,
    context: Context,
    baseRecyclerViewCallBack: BaseRecyclerViewCallBack?
) : BaseRecyclerViewAdapter<BankCardListItemBean, ItemBankCardListBinding>(
    datas,
    context,
    baseRecyclerViewCallBack
) {
    @SuppressLint("SetTextI18n")
    override fun onBaseBindViewHolder(
        holder: BaseRecyclerViewHolder<ItemBankCardListBinding>,
        position: Int
    ) {
        val bankCardInfo = datas[position]
        val dataBinding = holder.itemDataBinding

        dataBinding.tvArbclCardNo.text = bankCardInfo.cardNo
        dataBinding.tvArbclCardName.text = bankCardInfo.bankName

        dataBinding.tvArbclCardType.text = "${bankCardInfo.bankCardType} | ${bankCardInfo.holderName}"

        Glide.with(context).load(bankCardInfo.bankLogo).into(dataBinding.imageAdapterBankLogo)

    }

    override fun attachLayout(): Int {
        return R.layout.item_bank_card_list
    }
}