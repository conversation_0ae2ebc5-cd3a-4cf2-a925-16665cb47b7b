package com.fenqi.main.page.authinfo.bindbank.cardlist

import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.bean.BankCardListItemBean
import com.fenqi.main.bean.BankCardListResponseBean
import com.fenqi.main.postentity.FlushPrayCarryTravelEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.page.authinfo.bindbank.bindnew.BindBankNewActivity
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService

class BankCardListViewModel: BaseViewModel<MainApiService, BankCardListMvvmView>() {

    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }

    var type:String = BindBankNewActivity.BIND_BANK_TYPE_ORDER_SUBMIT

    var bankRoList:MutableList<BankCardListItemBean>? = mutableListOf()

    fun bindCardListGet(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: BankCardListResponseBean = json.decodeFromString(it)
                    data.bankRoList?.let { bankList->
                        bankRoList = bankList
                    }
                }
                mvvmView?.bindCardListGetSuccess()
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.bindCardListGet(CommonConstant.BIND_CARD_TYPE_PRINCIPAL_INTEREST.toString())
        }
    }

    fun orderConfirmSubmit() {
        RetrofitBuilder.getInstance().start({
            onSuccess {
                mvvmView?.orderSubmitConfirmSuccess()
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg, code)
            }
        }) {
            mService?.orderSubmitConfirm(
                RetrofitBuilder.getInstance().getRequestBody(FlushPrayCarryTravelEntity(productCode=""))
            )
        }
    }

}