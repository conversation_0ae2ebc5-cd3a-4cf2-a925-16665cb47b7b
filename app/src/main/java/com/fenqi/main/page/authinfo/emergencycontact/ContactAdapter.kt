package com.fenqi.main.page.authinfo.emergencycontact

import android.content.Context
import androidx.recyclerview.widget.LinearLayoutManager
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.bean.UserInfoVoEntity
import com.fenqi.main.databinding.AdapterContactBinding

class ContactAdapter(
    datas: MutableList<UserInfoVoEntity>,
    context: Context,
    baseRecyclerViewCallBack: BaseRecyclerViewCallBack?
) : BaseRecyclerViewAdapter<UserInfoVoEntity, AdapterContactBinding>(
    datas,
    context,
    baseRecyclerViewCallBack
) {
    override fun onBaseBindViewHolder(
        holder: BaseRecyclerViewHolder<AdapterContactBinding>,
        position: Int
    ) {
        val databinding = holder.itemDataBinding
        val userInfoVoEntity = datas[position]

        val contactSubsetAdapter = ContactSubsetAdapter(mutableListOf(),context,object :BaseRecyclerViewCallBack{
            override fun onClickListener(position: Int) {

            }
        })

        contactSubsetAdapter.setPersonalInfoSubsetCallBack { data, positionSubset ->
            userInfoVoEntity.inputParams?.set(positionSubset, data)
        }

        contactSubsetAdapter.setContactInfoSubsetCallBack {positionContact,type ->
            contactInfoSubsetCallBack.invoke(position,positionContact,type)
        }
        databinding.recyclerviewAdapterPersonalInfo.apply {
            layoutManager = LinearLayoutManager(context)
            setHasFixedSize(true)
            adapter = contactSubsetAdapter
        }
        userInfoVoEntity.inputParams?.let { contactSubsetAdapter.addNewList(it) }
    }

    private lateinit var contactInfoSubsetCallBack:(firstPosition:Int, position:Int,type:Int)->Unit

    fun setContactInfoSubsetCallBack(contactInfoSubsetCallBack:(firstPosition:Int, position:Int,type:Int)->Unit) {
        this.contactInfoSubsetCallBack = contactInfoSubsetCallBack
    }

    override fun attachLayout(): Int {
        return R.layout.adapter_contact
    }
}