package com.fenqi.main.page.authinfo.emergencycontact

import android.annotation.SuppressLint
import android.content.Context
import androidx.core.content.ContextCompat
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.bean.SelectVoEntity
import com.fenqi.main.databinding.AdapterRelationBinding

class ContactRelationAdapter(
    datas: MutableList<SelectVoEntity>,
    context: Context,
    baseRecyclerViewCallBack: BaseRecyclerViewCallBack?
) : BaseRecyclerViewAdapter<SelectVoEntity, AdapterRelationBinding>(
    datas,
    context,
    baseRecyclerViewCallBack
) {
    @SuppressLint("NotifyDataSetChanged")
    override fun onBaseBindViewHolder(
        holder: BaseRecyclerViewHolder<AdapterRelationBinding>,
        position: Int
    ) {
        val databinding = holder.itemDataBinding
        val selectVoEntity = datas[position]

        databinding.tvAdapterRelation.text = selectVoEntity.name
        if(selectVoEntity.check){
            databinding.tvAdapterRelation.setTextColor(ContextCompat.getColor(context,R.color.color_config_text_main))
            databinding.tvAdapterRelation.setBackgroundResource(R.drawable.shape_5r_main_grante)
        } else {
            databinding.tvAdapterRelation.setTextColor(ContextCompat.getColor(context,R.color.color_999999))
            databinding.tvAdapterRelation.setBackgroundResource(R.drawable.shape_5r_grey_common_bg)
        }

        databinding.tvAdapterRelation.setOnClickListener {
            for(item in datas){
                item.check = false
            }
            datas[position].check = true
            relationCallBack.invoke(datas[position].type.toString())
            notifyDataSetChanged()

        }
    }

    private lateinit var relationCallBack:(type:String)->Unit

    fun setRelationCallBack(relationCallBack:(type:String)->Unit) {
        this.relationCallBack = relationCallBack
    }

    override fun attachLayout(): Int {
        return R.layout.adapter_relation
    }
}