package com.fenqi.main.page.authinfo.emergencycontact

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.text.Editable
import android.text.InputType
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.bean.UserInfoItemEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.databinding.AdapterContactSubsetBinding
import com.fenqi.main.util.KeyboardUtil
import java.util.*

class ContactSubsetAdapter(
    datas: MutableList<UserInfoItemEntity>,
    context: Context,
    baseRecyclerViewCallBack: BaseRecyclerViewCallBack?
) : BaseRecyclerViewAdapter<UserInfoItemEntity, AdapterContactSubsetBinding>(
    datas,
    context,
    baseRecyclerViewCallBack
) {

    @SuppressLint("CheckResult")
    override fun onBaseBindViewHolder(
        holder: BaseRecyclerViewHolder<AdapterContactSubsetBinding>,
        position: Int
    ) {

        val databinding = holder.itemDataBinding
        val userInfoItemEntity = datas[position]

        databinding.buttonAdapterPersonalInfoSubsetTitle.text = userInfoItemEntity.paramName

        if(userInfoItemEntity.paramType == CommonConstant.DATA_VIEW_STATUS_CONTACT_RELATIONSHIP){
            databinding.viewNoRelation.visibility = View.GONE
            databinding.viewRelation.visibility = View.VISIBLE
        } else {
            databinding.viewNoRelation.visibility = View.VISIBLE
            databinding.viewRelation.visibility = View.GONE
        }

        when(userInfoItemEntity.paramType){
            CommonConstant.DATA_VIEW_STATUS_TEXT->{
                databinding.buttonAdapterPersonalInfoSubset.visibility = View.GONE
                databinding.imgAdapterPersonalInfoSubsetArrow.visibility = View.GONE

                databinding.inputAdapterPersonalInfoSubset.visibility = View.VISIBLE

                databinding.inputAdapterPersonalInfoSubset.hint = userInfoItemEntity.inputDesc
                databinding.inputAdapterPersonalInfoSubset.inputType = if(userInfoItemEntity.number) InputType.TYPE_CLASS_NUMBER else InputType.TYPE_CLASS_TEXT
                if(!TextUtils.isEmpty(userInfoItemEntity.inputValue)){
                    databinding.inputAdapterPersonalInfoSubset.setText(userInfoItemEntity.inputValue)
                }

                val textWatcher = object :TextWatcher{

                    override fun beforeTextChanged(
                        s: CharSequence?,
                        start: Int,
                        count: Int,
                        after: Int
                    ) {
                    }

                    override fun onTextChanged(
                        s: CharSequence?,
                        start: Int,
                        before: Int,
                        count: Int
                    ) {
                    }

                    override fun afterTextChanged(s: Editable?) {
                        if(!TextUtils.isEmpty(s)){
                            userInfoItemEntity.inputValue = s.toString()
                            personalInfoSubsetCallBack.invoke(userInfoItemEntity,position)
                        }
                    }
                }
                databinding.inputAdapterPersonalInfoSubset.addTextChangedListener(textWatcher)
            }
            CommonConstant.DATA_VIEW_STATUS_PICKER,CommonConstant.DATA_VIEW_STATUS_TIME_PICKER,CommonConstant.DATA_VIEW_STATUS_CONTACT_PICKER,CommonConstant.DATA_VIEW_STATUS_BANK_NAME_SELECTER->{
                databinding.buttonAdapterPersonalInfoSubset.visibility = View.VISIBLE
                databinding.imgAdapterPersonalInfoSubsetArrow.visibility = View.VISIBLE

                databinding.inputAdapterPersonalInfoSubset.visibility = View.GONE

                databinding.buttonAdapterPersonalInfoSubset.hint = userInfoItemEntity.inputDesc
                if(!TextUtils.isEmpty(userInfoItemEntity.inputValue)){
                    databinding.buttonAdapterPersonalInfoSubset.text = userInfoItemEntity.inputValue
                }
            }
            CommonConstant.DATA_VIEW_STATUS_CONTACT_RELATIONSHIP->{

                val contactRelationAdapter =
                    userInfoItemEntity.selectVo?.let {
                        ContactRelationAdapter(it,context,object :
                            BaseRecyclerViewAdapter.BaseRecyclerViewCallBack {
                            override fun onClickListener(position: Int) {

                            }
                        })
                    }
                contactRelationAdapter?.setRelationCallBack {type->
                    userInfoItemEntity.inputValue = type
                }

                databinding.recyclerviewAdapterContactRelation.apply {
                    layoutManager = GridLayoutManager(context,4)
                    setHasFixedSize(true)
                    adapter = contactRelationAdapter
                }
            }
        }

        databinding.buttonAdapterPersonalInfoSubset.setOnClickListener {
            clickPickerView(userInfoItemEntity,databinding,position)
        }
        databinding.imgAdapterPersonalInfoSubsetArrow.setOnClickListener {
            clickPickerView(userInfoItemEntity,databinding,position)
        }
    }

    private fun clickPickerView(userInfoItemEntity:UserInfoItemEntity,databinding:AdapterContactSubsetBinding,position: Int){
        KeyboardUtil.keyboardHideSoftInput(context as Activity)
        if(userInfoItemEntity.paramType == CommonConstant.DATA_VIEW_STATUS_PICKER){
            userInfoItemEntity.selectVo?.let {

             }

        }
        if(userInfoItemEntity.paramType == CommonConstant.DATA_VIEW_STATUS_TIME_PICKER){
            val selectedDateValue = Calendar.getInstance()
            val startDateValue = Calendar.getInstance()
            val endDateValue = Calendar.getInstance()
            val yearValue = selectedDateValue[Calendar.YEAR]
            val monthValue = selectedDateValue[Calendar.MONTH]
            val dayValue = selectedDateValue[Calendar.DAY_OF_MONTH]
            startDateValue.set(1940,0,1)
            endDateValue.set(yearValue, monthValue,dayValue)
            selectedDateValue.set(yearValue, monthValue,dayValue)

        }
        if(userInfoItemEntity.paramType == CommonConstant.DATA_VIEW_STATUS_CONTACT_PICKER){
            contactInfoSubsetCallBack.invoke(position,CommonConstant.DATA_VIEW_STATUS_CONTACT_PICKER)
        }
        if(userInfoItemEntity.paramType == CommonConstant.DATA_VIEW_STATUS_BANK_NAME_SELECTER){
            contactInfoSubsetCallBack.invoke(position,CommonConstant.DATA_VIEW_STATUS_BANK_NAME_SELECTER)
        }
    }

    private lateinit var personalInfoSubsetCallBack:(data: UserInfoItemEntity,position:Int)->Unit

    fun setPersonalInfoSubsetCallBack(personalInfoSubsetCallBack:(data: UserInfoItemEntity,position:Int)->Unit) {
        this.personalInfoSubsetCallBack = personalInfoSubsetCallBack
    }

    private lateinit var contactInfoSubsetCallBack:(position:Int,type:Int)->Unit

    fun setContactInfoSubsetCallBack(contactInfoSubsetCallBack:(position:Int,type:Int)->Unit) {
        this.contactInfoSubsetCallBack = contactInfoSubsetCallBack
    }

    override fun attachLayout(): Int {
        return R.layout.adapter_contact_subset
    }
}