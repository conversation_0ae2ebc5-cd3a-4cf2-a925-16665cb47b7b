package com.fenqi.main.page.authinfo.emergencycontact

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.widget.RelativeLayout
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.bean.SelectVoEntity
import com.fenqi.main.bean.UserInfoItemEntity
import com.fenqi.main.bean.UserInfoVoEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.databinding.ActivityEmergencyContactBinding
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.util.AuthJumpUtil
import com.fenqi.main.util.SystemUtil
import com.fenqi.main.view.dialog.AuthBackTipDialog
import com.fenqi.main.view.dialog.SignOutDialog
import com.fenqi.platformtools.utils.CommonIntent
// import com.tbruyelle.rxpermissions3.RxPermissions

class EmergencyContactActivity :
        BaseActivity<MainApiService, EmergencyContactMvvmView, EmergencyContactViewModel, ActivityEmergencyContactBinding>(), EmergencyContactMvvmView {

    companion object {
        fun callIntent(context: Context): Intent {
            return Intent(context, EmergencyContactActivity::class.java)
        }
    }

    private lateinit var contactAdapter: ContactAdapter
    private lateinit var contactLauncher: ActivityResultLauncher<Intent>
    private var hasReadAgreement = false

    private var firstPosition = 0
    private var subsetPosition = 0

    override fun onShowViewById(): Int {
        return R.layout.activity_emergency_contact
    }

    override fun kinit() {
        dataBinding?.viewModel = viewModel
        dataBinding?.click = this

        contactLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                if (result.data?.data != null) {
                    viewModel?.getPhoneValue(result.data?.data)
                }
            }
        }

        val relateArrow: RelativeLayout = findViewById(R.id.view_base_title_return_back)
        relateArrow.setOnClickListener {
            showReturnBackView()
        }

        initView()

        viewModel?.userAuthContactInfo()
        viewModel?.orderSubmitConfigGet()
    }

    private fun showReturnBackView(){
        if(viewModel?.finished == true){
            finish()
        } else {
            val authBackTipDialog = AuthBackTipDialog(activity)
            authBackTipDialog.setConfirmLogic {
                finish()
            }
            authBackTipDialog.showDialog()
        }
    }

    @SuppressLint("CheckResult")
    private fun initView() {
        contactAdapter = ContactAdapter(mutableListOf(), activity, object :
                BaseRecyclerViewAdapter.BaseRecyclerViewCallBack {
            override fun onClickListener(position: Int) {

            }
        })
        dataBinding?.recyclerviewEc?.apply {
            layoutManager = LinearLayoutManager(activity)
            setHasFixedSize(true)
            adapter = contactAdapter
        }
        contactAdapter.setContactInfoSubsetCallBack { firstPosition, subsetPosition, _ ->
            this.firstPosition = firstPosition
            this.subsetPosition = subsetPosition

            // TODO: Replace with proper permission handling
            viewModel?.jumpContact(contactLauncher)
        }
    }

    private fun showOpenSettingDialog(){
        val signOutDialog = SignOutDialog(activity)
        signOutDialog.setClickSubmit {
            CommonIntent.intentToSetting(activity)
        }
        signOutDialog.showSignOutDialog(getString(R.string.permission_contact_tip))
    }

    fun clickSubmit() {
        showLoading()
        viewModel?.userContactSave(contactAdapter.datas)
    }

    override fun attachViewModel(): Class<EmergencyContactViewModel> {
        return EmergencyContactViewModel::class.java
    }

    override fun attachMvvmView(): EmergencyContactMvvmView {
        return this
    }

    override fun attachTitleText(): String {
        return getString(R.string.contact_information)
    }

    override fun userAuthContactInfoSuccess(userInputInfoVOList: MutableList<UserInfoVoEntity>) {
        for (userInfoVoEntity: UserInfoVoEntity in userInputInfoVOList) {
            val inputParams = userInfoVoEntity.inputParams
            inputParams?.let {
                for (userInfoItemEntity: UserInfoItemEntity in inputParams) {
                    if (userInfoItemEntity.paramType == CommonConstant.DATA_VIEW_STATUS_PICKER && !TextUtils.isEmpty(userInfoItemEntity.inputValue)) {
                        userInfoItemEntity.selectVo?.let {
                            for (selectVoItem: SelectVoEntity in it) {
                                if (selectVoItem.type.toString() == userInfoItemEntity.inputValue) {
                                    userInfoItemEntity.inputValue = selectVoItem.name
                                    break
                                }
                            }
                        }

                    }
                }
            }
        }
        contactAdapter.addNewList(userInputInfoVOList)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun selectContactSuccess(name: String?, phoneNumber: String?) {
        val userInputInfoVOList: MutableList<UserInfoVoEntity> = contactAdapter.datas
        var canNext = true
        for (index in userInputInfoVOList.indices) {
            if (firstPosition != index) {
                val inputParams: MutableList<UserInfoItemEntity>? = userInputInfoVOList[index].inputParams
                inputParams?.let {
                    for (item in inputParams) {
                        if (item.param == CommonConstant.EMERGENCY_PARAM_NAME && name == item.inputValue) {
                            showToast(getString(R.string.select_contact_phone_tip))
                            canNext = false
                            break
                        }
                        if (item.param == CommonConstant.EMERGENCY_PARAM_MOBILE && phoneNumber == item.inputValue) {
                            showToast(getString(R.string.select_contact_phone_tip))
                            canNext = false
                            break
                        }
                    }
                }
            }
        }
        if (canNext) {
            val brand = SystemUtil.getInstance().getPhoneBrand()
            val inputParams = contactAdapter.datas[firstPosition].inputParams
            inputParams?.let {
                for (item in inputParams) {
                    if (item.param == CommonConstant.EMERGENCY_PARAM_NAME) {
                        item.inputValue = name.toString()
                    }
                    if (item.param == CommonConstant.EMERGENCY_PARAM_MOBILE) {
                        item.inputValue = phoneNumber.toString()
                    }

                    if(brand.lowercase().contains("huawei")){
                        if(TextUtils.isEmpty(name) || TextUtils.isEmpty(phoneNumber)){
                            if(item.param == "friendName" || item.param == "friendMobile"){
                                item.paramType = CommonConstant.DATA_VIEW_STATUS_TEXT
                                showToast(getString(R.string.contact_input_tip))
                            }
                        }
                    }
                }
            }

            contactAdapter.notifyDataSetChanged()
        }
    }

    override fun userAuthContactSaveSuccess() {
        showToast(getString(R.string.person_info_success_tip))

        AuthJumpUtil.getInstance().setFinishedCallBack {
            dismissLoading()
            finish()
        }.setOnFailed {
            dismissLoading()
        }.judgeJump(activity)
    }

    override fun requestError(message: String, code: Int) {
        dismissLoading()
        showToast(message)
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {
        showLoading()
    }

    override fun onBackPressed() {
        if (viewModel?.finished == false) {
            showReturnBackView()
            return
        }
        super.onBackPressed()
    }
}