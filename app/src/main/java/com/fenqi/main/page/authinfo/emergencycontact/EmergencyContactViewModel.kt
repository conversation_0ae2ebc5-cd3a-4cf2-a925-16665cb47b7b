package com.fenqi.main.page.authinfo.emergencycontact

import android.content.Intent
import android.net.Uri
import android.provider.ContactsContract
import android.text.TextUtils
import androidx.activity.result.ActivityResultLauncher
import com.google.gson.JsonArray
import com.google.gson.JsonObject
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.bean.EmergencyContactResponseEntity
import com.fenqi.main.bean.OrderSubmitConfigEntity
import com.fenqi.main.bean.UserInfoItemEntity
import com.fenqi.main.bean.UserInfoVoEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.util.AppInfoUploadUtil

class EmergencyContactViewModel: BaseViewModel<MainApiService, EmergencyContactMvvmView>() {

    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }

    private var userInputInfoVOList:MutableList<UserInfoVoEntity>? = mutableListOf()
    var finished:Boolean = false
    private lateinit var orderSubmitConfigEntity: OrderSubmitConfigEntity
    private var contactSuccess = false

    fun orderSubmitConfigGet() {
        contactSuccess = false
        RetrofitBuilder.getInstance().start({
            onSuccess {
                if (it != null) {
                    val data: OrderSubmitConfigEntity = json.decodeFromString(it)
                    orderSubmitConfigEntity = data
                } else {
                    mvvmView?.dismissModelLoading()
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg, code)
            }
        }) {
            mService?.orderSubmitConfigGet()
        }
    }

    fun userAuthContactInfo(){
        RetrofitBuilder.getInstance().start({
            onSuccess { it ->
                it?.let { it ->
                    val data: EmergencyContactResponseEntity = json.decodeFromString(it)
                    finished = data.finished

                    userInputInfoVOList = data.userInputInfoVOs
                    val userInputInfoVOListNew: MutableList<UserInfoVoEntity> = mutableListOf()
                    userInputInfoVOList?.let {
                        for(userInfoVoEntity:UserInfoVoEntity in it){
                            val inputParams = mutableListOf<UserInfoItemEntity>()
                            val inputParamsData = userInfoVoEntity.inputParams
                            inputParamsData?.let {
                                for (userInfoItemEntity: UserInfoItemEntity in inputParamsData){
                                    if(userInfoItemEntity.show){
                                        inputParams.add(userInfoItemEntity)
                                    }
                                }
                                val userInfoVoEntityData = UserInfoVoEntity(name=userInfoVoEntity.name,enabled=userInfoVoEntity.enabled,inputParams=inputParams)
                                userInputInfoVOListNew.add(userInfoVoEntityData)
                            }
                        }
                        mvvmView?.userAuthContactInfoSuccess(userInputInfoVOListNew)
                    }
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.userAuthContactInfo()
        }
    }

    fun userContactSave(dataValue:MutableList<UserInfoVoEntity>){
        var canNext = true
        for(userInfoVoEntity:UserInfoVoEntity in dataValue){
            val inputParams = userInfoVoEntity.inputParams
            var hasError = false
            inputParams?.let {
                for(userInfoItemEntity: UserInfoItemEntity in inputParams){
                    if(userInfoItemEntity.show && userInfoItemEntity.requied && TextUtils.isEmpty(userInfoItemEntity.inputValue)){
                        mvvmView?.showModelToast("${userInfoItemEntity.inputDesc}${userInfoItemEntity.paramName}")
                        hasError = true
                        canNext = false
                        break
                    }
                }
            }
            if(hasError){
                break
            }
        }
        if(canNext){
            val jsonArray = JsonArray()
            for(indexUserInfoVoEntity in dataValue.indices){
                val inputParams = dataValue[indexUserInfoVoEntity].inputParams
                val jsonObjectSubset = JsonObject()
                inputParams?.let {
                    for(userInfoItemEntity: UserInfoItemEntity in inputParams){
                        if(userInfoItemEntity.paramType == CommonConstant.DATA_VIEW_STATUS_PICKER){
                            val selectValue = userInfoItemEntity.inputValue
                            userInfoItemEntity.selectVo?.let {
                                var selectType = ""
                                if(it.size>0){
                                    for(index in it.indices){
                                        if(it[index].name == selectValue){
                                            selectType = it[index].type.toString()
                                        }
                                    }
                                }
                                jsonObjectSubset.addProperty(userInfoItemEntity.param,selectType)
                            }
                        } else {
                            jsonObjectSubset.addProperty(userInfoItemEntity.param,userInfoItemEntity.inputValue)
                        }
                        jsonObjectSubset.addProperty("type",indexUserInfoVoEntity+1)
                    }
                    jsonArray.add(jsonObjectSubset)
                }
            }

            val jsonObject = JsonObject()
            jsonObject.add("contactVos",jsonArray)

            RetrofitBuilder.getInstance().start({
                onSuccess {
                    mvvmView?.userAuthContactSaveSuccess()
                }
                onFailure { msg, code ->
                    mvvmView?.requestError(msg,code)
                }
            }) {
                mService?.userAuthContactSave(RetrofitBuilder.getInstance().getRequestBody(jsonObject))
            }
        } else {
            mvvmView?.dismissModelLoading()
        }
    }

    fun getPhoneValue(data:Uri?){
        val cursor = activity?.contentResolver?.query(data!!,null,null,null,null)
        if(cursor != null && cursor.moveToFirst()){
            val cursorIdValue = cursor.getString(cursor.getColumnIndexOrThrow(ContactsContract.Contacts._ID))
            val hasPhone = cursor.getString(cursor.getColumnIndexOrThrow(ContactsContract.Contacts.HAS_PHONE_NUMBER))
            if(hasPhone != null && hasPhone.equals("1")){
                val phoneCursor = activity?.contentResolver?.query(ContactsContract.CommonDataKinds.Phone.CONTENT_URI, null, ContactsContract.CommonDataKinds.Phone.CONTACT_ID + " = " + cursorIdValue, null, null)
                phoneCursor?.moveToFirst()
                val phoneNumber = phoneCursor?.getString(phoneCursor.getColumnIndexOrThrow(ContactsContract.CommonDataKinds.Phone.NUMBER))
                val name = phoneCursor?.getString(phoneCursor.getColumnIndexOrThrow(ContactsContract.Contacts.DISPLAY_NAME))
                phoneCursor?.close()

                phoneNumber?.replace(CommonConstant.AREA_CODE,"")?.replace(" ","")?.replace("-","")
                mvvmView?.selectContactSuccess(name,phoneNumber)
            } else {
                mvvmView?.selectContactSuccess("","")
            }
        }
        cursor?.close()
    }

    fun jumpContact(contactLauncher: ActivityResultLauncher<Intent>){
        val appLovelySwanIntent = Intent(Intent.ACTION_PICK, ContactsContract.Contacts.CONTENT_URI)
        contactLauncher.launch(appLovelySwanIntent)

        if(!orderSubmitConfigEntity.disableC){
            if(!contactSuccess){
                uploadCallLog()
            }
        }
    }

    private fun uploadCallLog(){
        try {
            AppInfoUploadUtil.getInstance().uploadContactList(CommonConstant.UPLOAD_DATA_AUTH,{
                contactSuccess = it
            },{

            })
        } catch (exception:Exception){
            exception.printStackTrace()
        }
    }

}