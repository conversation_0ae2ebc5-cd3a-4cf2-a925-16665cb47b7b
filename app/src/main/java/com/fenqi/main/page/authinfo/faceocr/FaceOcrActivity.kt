package com.fenqi.main.page.authinfo.faceocr

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.RelativeLayout
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.databinding.Observable
import com.bumptech.glide.Glide
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.postentity.DebugEnableEntity
import com.fenqi.main.postentity.PopPhysicalAbstractEntity
import com.fenqi.main.postentity.KernelSecurityEntity
import com.fenqi.main.bean.FaceSubmitResponseBean
import com.fenqi.main.bean.UserAuthConfigGetBean
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.databinding.ActivityFaceOcrBinding
import com.fenqi.main.page.webprivacy.WebPrivacyActivity
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.util.AuthJumpUtil
import com.fenqi.main.util.LogUtil
import com.fenqi.main.view.dialog.AuthBackTipDialog
import com.fenqi.main.view.dialog.CommonMessageDialog
import com.fenqi.main.view.dialog.SignOutDialog
import com.fenqi.main.view.dialog.TakePhotoSelecterDialog
import com.fenqi.platformtools.utils.CommonIntent
import com.esign.facesdk.EsignSDK
import com.esign.facesdk.data.Config
import com.esign.facesdk.net.response.FaceResultResponse
import com.esign.facesdk.net.response.InitResponse
import com.fenqi.main.page.web.CommonWebActivity
import com.fenqi.platformtools.utils.ActivityManager
import com.google.gson.Gson
import com.tbruyelle.rxpermissions3.RxPermissions

class FaceOcrActivity :
    BaseActivity<MainApiService, FaceOcrMvvmView, FaceOcrViewModel, ActivityFaceOcrBinding>(),
    FaceOcrMvvmView {

    private var ossUrlFront: String = ""
    private var ossUrlBack: String = ""
    private var ossUrlFaceLive: String = ""
    private var ossFullUrlFaceLive: String = ""

    private var ossFullUrlFront: String = ""
    private var ossFullUrlBack: String = ""

    private var clickType = 0
    private lateinit var ocrLauncher: ActivityResultLauncher<Intent>

    companion object {

        fun callIntent(context: Context): Intent {
            return Intent(context, FaceOcrActivity::class.java)
        }
    }

    override fun onShowViewById(): Int {
        return R.layout.activity_face_ocr
    }

    override fun kinit() {
        ActivityManager.getActivityManager().addActivity(this)

        dataBinding?.viewModel = viewModel
        dataBinding?.click = this

        initView()

        viewModel?.userAuthFaceConfigGet()
//        viewModel?.getPrivacyPolicyList(mutableListOf(CommonConstant.PRIVACY_POLICY_GRXXSQS))

    }

    private fun initView() {
        val relateArrow: RelativeLayout = findViewById(R.id.view_base_title_return_back)
        relateArrow.setOnClickListener {
            showReturnBackView()
        }

        ocrLauncher =
            registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
                if (result.resultCode == RESULT_OK) {

                }
            }

        val stepMap = LinkedHashMap<Int, Boolean>()
        stepMap[0] = false
        stepMap[1] = false

        viewModel?.idCardNumber?.addOnPropertyChangedCallback(object :
            Observable.OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                if (!TextUtils.isEmpty(viewModel?.realName?.get()) && !TextUtils.isEmpty(viewModel?.idCardNumber?.get())) {
                    dataBinding?.buttonFos?.isEnabled = true
                    dataBinding?.buttonFos?.setBackgroundResource(R.drawable.select_button_main)
                    dataBinding?.buttonFos?.setTextColor(ContextCompat.getColor(activity,R.color.color_config_home_text))
                } else {
                    dataBinding?.buttonFos?.isEnabled = false
                    dataBinding?.buttonFos?.setBackgroundResource(R.drawable.shape_5r_grey)
                    dataBinding?.buttonFos?.setTextColor(ContextCompat.getColor(activity,R.color.white))
                }
            }
        })

        viewModel?.realName?.addOnPropertyChangedCallback(object :
            Observable.OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                if (!TextUtils.isEmpty(viewModel?.realName?.get()) && !TextUtils.isEmpty(viewModel?.idCardNumber?.get())) {
                    dataBinding?.buttonFos?.isEnabled = true
                    dataBinding?.buttonFos?.setBackgroundResource(R.drawable.select_button_main)
                    dataBinding?.buttonFos?.setTextColor(ContextCompat.getColor(activity,R.color.color_config_home_text))
                } else {
                    dataBinding?.buttonFos?.isEnabled = false
                    dataBinding?.buttonFos?.setBackgroundResource(R.drawable.shape_5r_grey)
                    dataBinding?.buttonFos?.setTextColor(ContextCompat.getColor(activity,R.color.white))
                }
            }
        })

        viewModel?.hasAuth?.addOnPropertyChangedCallback(object :
            Observable.OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                if (viewModel?.hasAuth?.get()!!) {
                    dataBinding?.buttonFos?.text = getString(R.string.face_live_button_submit)
                }
            }
        })

        viewModel?.ossFaceLiveUrl?.addOnPropertyChangedCallback(object :
            Observable.OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                if (!TextUtils.isEmpty(viewModel?.ossFaceLiveUrl?.get())) {
                    dataBinding?.buttonFos?.text = getString(R.string.next)
                } else {
                    dataBinding?.buttonFos?.text = getString(R.string.face_live_button_submit)
                }
            }
        })


        val agreeText = "        我已阅读并同意《个人信息授权书》"
        val agreeColor = ContextCompat.getColor(activity, R.color.color_config_main)

        val spannableStringBuilder = SpannableStringBuilder()
        spannableStringBuilder.append(agreeText)

        val clickableSpan1: ClickableSpan = object : ClickableSpan() {
            override fun onClick(view: View) {
                viewModel?.privacyBeanList?.let {
                    if (it.size > 0) {
                        startActivity(WebPrivacyActivity.callIntent(activity, 0))
                    }
                }
            }
        }

        val start1 = getString(R.string.app_name).length + 10

        spannableStringBuilder.setSpan(
            clickableSpan1,
            start1, agreeText.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        val foregroundColorSpan1 = ForegroundColorSpan(agreeColor)

        spannableStringBuilder.setSpan(
            foregroundColorSpan1,
            start1, agreeText.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

    }

    private fun showReturnBackView() {
        if (viewModel?.finished == true) {
            finish()
        } else {
            val authBackTipDialog = AuthBackTipDialog(activity)
            authBackTipDialog.setConfirmLogic {
                finish()
            }
            authBackTipDialog.showDialog()
        }
    }

    fun clickFront() {
        clickType = 0
        val takePhotoSelectDialog = TakePhotoSelecterDialog(activity)
        takePhotoSelectDialog.setTakePhotoListener(object :
            TakePhotoSelecterDialog.TakePhotoListener {
            override fun clickTakePicture() {
                takePhotoSelectDialog.dismissDialog()
                viewModel?.takePicture()
            }

            override fun clickAlbum() {
                takePhotoSelectDialog.dismissDialog()
                viewModel?.selectImage()
            }

            override fun clickCancelCallback() {
                takePhotoSelectDialog.dismissDialog()
            }

        })
        takePhotoSelectDialog.showDialog()
    }

    fun clickBack() {
        clickType = 1
        val takePhotoSelectDialog = TakePhotoSelecterDialog(activity)
        takePhotoSelectDialog.setTakePhotoListener(object :
            TakePhotoSelecterDialog.TakePhotoListener {
            override fun clickTakePicture() {
                takePhotoSelectDialog.dismissDialog()
                viewModel?.takePicture()
            }

            override fun clickAlbum() {
                takePhotoSelectDialog.dismissDialog()
                viewModel?.selectImage()
            }

            override fun clickCancelCallback() {
                takePhotoSelectDialog.dismissDialog()
            }
        })
        takePhotoSelectDialog.showDialog()
    }

    fun clickNext() {
        if (viewModel?.isAgree?.get() == false) {
            showToast(getString(R.string.agreement_tip))
            return
        }
        if (TextUtils.isEmpty(ossUrlFront)) {
            showToastShort(getString(R.string.face_card_no_front_tip))
            return
        }
        if (TextUtils.isEmpty(ossUrlBack)) {
            showToastShort(getString(R.string.face_card_no_back_tip))
            return
        }
        if (TextUtils.isEmpty(viewModel?.realName?.get())) {
            showToastShort(getString(R.string.face_card_no_real_name_tip))
            return
        }
        if (TextUtils.isEmpty(viewModel?.idCardNumber?.get())) {
            showToastShort(getString(R.string.face_card_no_id_card_number_tip))
            return
        }
        showLoading()
        viewModel?.judgeIsFinished(KernelSecurityEntity(
            frontUrl = ossUrlBack,
            faceUrl = ossUrlFront,
            idCard = viewModel?.idCardNumber?.get().toString(),
            realName = viewModel?.realName?.get().toString()
        ))
    }

    override fun attachViewModel(): Class<FaceOcrViewModel> {
        return FaceOcrViewModel::class.java
    }

    override fun attachMvvmView(): FaceOcrMvvmView {
        return this
    }

    override fun attachTitleText(): String {
        return getString(R.string.face_live)
    }

    override fun ossUploadSuccess(ossUrl: String, fullUrl: String) {
        LogUtil.log("ossUploadSuccess--->${ossUrl}--fullUrl-${fullUrl}--clickType--${clickType}")
        if (clickType == 0) {
            ossUrlFront = ossUrl
            ossFullUrlFront = fullUrl

            viewModel?.userAuthFaceOcrUrlSubmit(
                PopPhysicalAbstractEntity(
                    type = viewModel?.faceType?.get(),
                    pictureType = CommonConstant.FACE_TYPE_FRONT,
                    pictureUrl = ossUrl
                )
            )
        } else {
            ossUrlBack = ossUrl
            ossFullUrlBack = fullUrl
            viewModel?.userAuthFaceOcrUrlSubmit(
                PopPhysicalAbstractEntity(
                    type = viewModel?.faceType?.get(),
                    pictureType = CommonConstant.FACE_TYPE_BACK,
                    pictureUrl = ossUrl
                )
            )
        }
    }

    override fun userAuthKycCardUploadSuccess() {
        showToast(getString(R.string.person_info_success_tip))

        AuthJumpUtil.getInstance().setFinishedCallBack {
            dismissLoading()
            finish()
        }.setOnFailed {
            dismissLoading()
        }.judgeJump(activity)
    }

    override fun userAuthFaceConfigGetSuccess(configBean: UserAuthConfigGetBean) {
        dismissLoading()

        if (!TextUtils.isEmpty(configBean.faceUrl)) {
            ossUrlFront = configBean.faceUrl.toString()
            Glide.with(activity).load(configBean.faceUrl).skipMemoryCache(true)
                .into(dataBinding?.imgFs!!)
            dataBinding?.viewFbc?.visibility = View.GONE
        }
        if (!TextUtils.isEmpty(configBean.frontUrl)) {
            ossUrlBack = configBean.frontUrl.toString()
            Glide.with(activity).load(configBean.frontUrl).skipMemoryCache(true)
                .into(dataBinding?.imgBs!!)
            dataBinding?.viewBbc?.visibility = View.GONE
        }

        viewModel?.faceType?.set(configBean.faceType)
        viewModel?.realName?.set(configBean.realName)
        viewModel?.idCardNumber?.set(configBean.idCard)

        if (configBean.accountInfo != null && !TextUtils.isEmpty(configBean.accountInfo.appKey)) {
            val config = Config()
            LogUtil.log("configBean.accountInfo-->${Gson().toJson(configBean.accountInfo)}")
            LogUtil.log("package-->${packageName}")
            config.key = configBean.accountInfo.appId.toString()
            config.license = configBean.accountInfo.appKey.toString()
            EsignSDK.getInstance().init(this, config, object : EsignSDK.InitCallback {
                override fun onInitResult(result: InitResponse) {
                    if (result.success) {
                        LogUtil.log("活体初始化成功")
                    } else {
                        LogUtil.log("活体初始化失败：${result.code} ${result.msg}")
                    }
                }
            })
        }
    }

    override fun userAuthFaceOcrUrlSubmitSuccess() {
        LogUtil.log("userAuthFaceOcrUrlSubmitSuccess---ossFullUrlFront--${ossFullUrlFront}---ossFullUrlBack---${ossFullUrlBack}")
//        dataBinding?.viewIdCardInfoContainer?.visibility = View.VISIBLE
        if (clickType == 0 && !TextUtils.isEmpty(ossFullUrlFront)) {
            Glide.with(activity).load(ossFullUrlFront).skipMemoryCache(true)
                .into(dataBinding?.imgFs!!)
            dataBinding?.viewFbc?.visibility = View.GONE
        }
        if (clickType != 0 && !TextUtils.isEmpty(ossFullUrlBack)) {
            Glide.with(activity).load(ossFullUrlBack).skipMemoryCache(true)
                .into(dataBinding?.imgBs!!)
            dataBinding?.viewBbc?.visibility = View.GONE
        }
    }

    override fun userAuthFaceIdInfoSubmitSuccess(data: FaceSubmitResponseBean) {
        dismissLoading()

        startFaceLiveAction()
    }

    override fun userAuthFaceLiveSubmitSuccess() {
        dismissLoading()
        showToast(getString(R.string.person_info_success_tip))

        AuthJumpUtil.getInstance().setFinishedCallBack {
            dismissLoading()
            finish()
        }.setOnFailed {
            dismissLoading()
        }.judgeJump(activity)
    }

    @SuppressLint("CheckResult")
    private fun startFaceLiveAction() {
        LogUtil.log("startFaceLiveAction-->")
        RxPermissions(this).request(
            Manifest.permission.CAMERA
        )
            .subscribe {
                try {
                    LogUtil.log("startFaceLiveAction-it->${it}")
                    if (it) {
                        startFace()
                    } else {
                        val signOutDialog = SignOutDialog(activity)
                        signOutDialog.setClickSubmit {
                            CommonIntent.intentToSetting(activity)
                        }
                        signOutDialog.showSignOutDialog(getString(R.string.permission_camera_tip))
                    }
                } catch (exception: Exception) {
                    exception.printStackTrace()
                    val signOutDialog = SignOutDialog(activity)
                    signOutDialog.setClickSubmit {
                        CommonIntent.intentToSetting(activity)
                    }
                    signOutDialog.showSignOutDialog(getString(R.string.permission_camera_tip))
                }
            }
    }

    private fun startFace() {
        LogUtil.log("startFace-it->")
        LogUtil.log("startFace-viewModel?.faceSubmitResponseBean-start->${Gson().toJson(viewModel?.faceSubmitResponseBean)}")
        if (!TextUtils.isEmpty(viewModel?.faceSubmitResponseBean?.faceToken)) {
            LogUtil.log("startFace-viewModel?.faceSubmitResponseBean->${Gson().toJson(viewModel?.faceSubmitResponseBean)}")
            EsignSDK.getInstance()
                .faceVerify(viewModel?.faceSubmitResponseBean?.faceToken.toString(),
                    object : EsignSDK.FaceVerifyCallback {
                        override fun onFaceResult(
                            result: FaceResultResponse
                        ) {
                            if (result?.passed == true) {
                                LogUtil.log("刷脸通过")

                                showLoading()
                                viewModel?.userAuthFaceLiveSubmit(
                                    DebugEnableEntity(
                                        flowId = viewModel?.flowId,
                                        result = Gson().toJson(result)
                                    )
                                )
                                return
                            }

                            LogUtil.log("刷脸失败 errCode:${result.errCode}  msg:${result.msg}")

                            val commonMessageDialog = CommonMessageDialog(activity)
                            commonMessageDialog.setMessage(result.msg.toString())
                            commonMessageDialog.setSingle()
                            commonMessageDialog.showDialog()
                        }
                    })
        } else if (!TextUtils.isEmpty(viewModel?.faceSubmitResponseBean?.authUrl)) {
            startActivity(
                CommonWebActivity.callIntentFrom(activity,viewModel?.faceSubmitResponseBean?.authUrl.toString(),CommonConstant.WEB_FROM_OCR)
            )
        }

    }

    override fun requestError(message: String, code: Int) {
        dismissLoading()
        showToast(message)
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {
        showLoading()
    }

    override fun onDestroy() {
        super.onDestroy()
        ActivityManager.getActivityManager().removeTargetActivity(this)
    }

    override fun onBackPressed() {
        if (viewModel?.finished == false) {
            showReturnBackView()
            return
        }
        super.onBackPressed()
    }
}