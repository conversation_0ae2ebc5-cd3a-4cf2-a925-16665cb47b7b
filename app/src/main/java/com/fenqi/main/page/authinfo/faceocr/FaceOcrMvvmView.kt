package com.fenqi.main.page.authinfo.faceocr

import com.fenqi.main.bean.FaceSubmitResponseBean
import com.fenqi.main.bean.UserAuthConfigGetBean
import com.fenqi.main.presenter.base.MvvmView

interface FaceOcrMvvmView:MvvmView {
    fun ossUploadSuccess(ossUrl:String,fullUrl:String)
    fun userAuthKycCardUploadSuccess()
    fun userAuthFaceConfigGetSuccess(configBean:UserAuthConfigGetBean)
    fun userAuthFaceOcrUrlSubmitSuccess()
    fun userAuthFaceIdInfoSubmitSuccess(data: FaceSubmitResponseBean)
    fun userAuthFaceLiveSubmitSuccess()
}