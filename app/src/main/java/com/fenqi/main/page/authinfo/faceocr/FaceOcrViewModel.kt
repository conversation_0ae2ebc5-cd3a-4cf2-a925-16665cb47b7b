package com.fenqi.main.page.authinfo.faceocr

import android.Manifest
import android.content.Context
import android.text.TextUtils
import androidx.databinding.ObservableField
import com.fenqi.main.R
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.bean.*
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.constant.CommonData
import com.fenqi.main.postentity.DebugEnableEntity
import com.fenqi.main.postentity.PopPhysicalAbstractEntity
import com.fenqi.main.postentity.KernelSecurityEntity
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.util.AuthJumpUtil
import com.fenqi.main.util.LogUtil
import com.fenqi.main.util.OSSUploadUtil
import com.fenqi.main.util.TakePhotoUtil
import com.fenqi.main.view.dialog.SignOutDialog
import com.fenqi.platformtools.utils.CommonIntent
import com.google.gson.Gson
import com.fenqi.main.util.SimplePermissionHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class FaceOcrViewModel : BaseViewModel<MainApiService, FaceOcrMvvmView>() {
    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }

    var currentPosition = 0
    var pictureType = 0

    var realName: ObservableField<String> = ObservableField<String>().apply { set("") }
    var idCardNumber: ObservableField<String> = ObservableField<String>().apply { set("") }
    var faceType: ObservableField<Int> = ObservableField<Int>().apply { set(CommonConstant.FACE_TYPE_ACCUAUTH) }
    var hasAuth: ObservableField<Boolean> = ObservableField<Boolean>().apply { set(false) }
    var ossFaceLiveUrl: ObservableField<String> = ObservableField<String>().apply { set("") }

    var finished:Boolean = false

    var flowId:String = ""

    var faceSubmitResponseBean:FaceSubmitResponseBean = FaceSubmitResponseBean()

    private fun takePhotoSuccess(imagePath: String) {
        mvvmView?.showModelLoading()
        LogUtil.log("takePhotoSuccess-->")
        OSSUploadUtil().setOnFailed {msg->
            mvvmView?.dismissModelLoading()
            if(!TextUtils.isEmpty(msg)){
                mvvmView?.showModelToast(msg)
            }
        }
                .setOnSuccess { url, fullUrl ->
                    LogUtil.log("url-->${url}---fullUrl--${fullUrl}")
                    mvvmView?.ossUploadSuccess(url, fullUrl)
                }
                .startUploadFile(imagePath)
    }

    fun userAuthFaceConfigGet() {
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: UserAuthConfigGetBean = json.decodeFromString(it)
                    mvvmView?.userAuthFaceConfigGetSuccess(data)
                    finished = data.finished
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg, code)
            }
        }) {
            mService?.userAuthFaceConfigGet()
        }
    }

    fun judgeIsFinished(postBean: KernelSecurityEntity) {
        RetrofitBuilder.getInstance().start({
            onSuccess { it ->
                it?.let { it ->
                    val data: UserAuthConfigGetBean = json.decodeFromString(it)
                    if(data.finished){
                        activity?.let {
                            AuthJumpUtil.getInstance().setFinishedCallBack {
                                mvvmView?.dismissModelLoading()
                                it.finish()
                            }.setOnFailed {
                                mvvmView?.dismissModelLoading()
                            }.judgeJump(it)
                        }
                    } else {
                        userAuthFaceIdInfoSubmit(postBean)
                    }
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg, code)
            }
        }) {
            mService?.userAuthFaceConfigGet()
        }
    }


    fun userAuthFaceIdInfoSubmit(postBean: KernelSecurityEntity) {
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: FaceSubmitResponseBean = json.decodeFromString(it)
                    faceSubmitResponseBean = data
                    flowId = data.flowId.toString()
                    mvvmView?.userAuthFaceIdInfoSubmitSuccess(data)
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg, code)
            }
        }) {
            mService?.userAuthFaceIdInfoSubmit(RetrofitBuilder.getInstance().getRequestBody(postBean))
        }
    }

    fun userAuthFaceOcrUrlSubmit(postBean: PopPhysicalAbstractEntity) {
        LogUtil.log("userAuthFaceOcrUrlSubmit-->${Gson().toJson(postBean)}")
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: FaceOcrUrlResponseBean = json.decodeFromString(it)
                    LogUtil.log("userAuthFaceOcrUrlSubmit--onSuccess")
                    mvvmView?.dismissModelLoading()
                    data.idCardInfo.let {cardInfo->
                        LogUtil.log("userAuthFaceOcrUrlSubmit--onSuccess--cardInfo--${Gson().toJson(cardInfo)}}")
                        if (!TextUtils.isEmpty(cardInfo?.fullName)) {
                            realName.set(cardInfo?.fullName)
                        }
                        if (!TextUtils.isEmpty(cardInfo?.accountNumber)) {
                            idCardNumber.set(cardInfo?.accountNumber)
                        }
                    }
                    mvvmView?.userAuthFaceOcrUrlSubmitSuccess()
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg, code)
                LogUtil.log("userAuthFaceOcrUrlSubmit--onFailure-msg->${msg}")
            }
        }) {
            mService?.userAuthFaceOcrUrlSubmit(RetrofitBuilder.getInstance().getRequestBody(postBean))
        }
    }

    fun userAuthFaceLiveSubmit(postBean: DebugEnableEntity) {
        RetrofitBuilder.getInstance().start({
            onSuccess {
                mvvmView?.userAuthFaceLiveSubmitSuccess()
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg, code)
            }
        }) {
            mService?.userAuthFaceLiveSubmit(RetrofitBuilder.getInstance().getRequestBody(postBean))
        }
    }

    fun takePicture() {
        activity?.let { activity ->
            if (SimplePermissionHelper.hasPermissions(activity, Manifest.permission.CAMERA, Manifest.permission.READ_EXTERNAL_STORAGE)) {
                TakePhotoUtil.getInstance().takeCamera(activity, successListener = { path ->
                    takePhotoSuccess(path)
                }, cancelListener = {

                })
            } else {
                SimplePermissionHelper.requestPermissions(activity, 1003, Manifest.permission.CAMERA, Manifest.permission.READ_EXTERNAL_STORAGE)
            }
        }
    }

    fun selectImage() {
        activity?.let { activity ->
            if (SimplePermissionHelper.hasPermission(activity, Manifest.permission.READ_EXTERNAL_STORAGE)) {
                TakePhotoUtil.getInstance().selectImage(activity, successListener = { path ->
                    takePhotoSuccess(path)
                }, cancelListener = {

                })
            } else {
                SimplePermissionHelper.requestPermissions(activity, 1004, Manifest.permission.READ_EXTERNAL_STORAGE)
            }
        }
    }


    var privacyBeanList: MutableList<PrivacyPolicyBean> = mutableListOf()
    var isAgree: ObservableField<Boolean> = ObservableField<Boolean>().apply { set(true) }
    fun getPrivacyPolicyList(keyList: MutableList<String>){
        privacyBeanList.clear()

        var tag = 0

        for (index in keyList.indices) {
            val key = keyList[index]
            if (!TextUtils.isEmpty(key)) {
                CoroutineScope(Dispatchers.IO).launch {
                    delay(100)
                    RetrofitBuilder.getInstance().start({
                        onSuccess {
                            it?.let {
                                val data: PrivacyPolicyBean = json.decodeFromString(it)
                                tag++
                                data.let { privacyBean ->
                                    privacyBeanList.add(privacyBean)
                                }
                                if(tag>=keyList.size){
                                    CommonData.privacyPolicyList = privacyBeanList
                                }
                            }
                        }
                        onFailure { _, _ ->
                            tag++
                            if(tag>=keyList.size){
                                CommonData.privacyPolicyList = privacyBeanList
                            }
                        }
                    }) {
                        mService?.userProtocolInfos(key)
                    }
                }
            }
        }
    }

}