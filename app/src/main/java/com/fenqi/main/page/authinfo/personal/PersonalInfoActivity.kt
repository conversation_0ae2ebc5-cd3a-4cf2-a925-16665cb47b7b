package com.fenqi.main.page.authinfo.personal

import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.widget.RelativeLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.bean.SelectVoEntity
import com.fenqi.main.bean.UserInfoItemEntity
import com.fenqi.main.bean.UserInfoVoEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.databinding.ActivityPersonalInformationBinding
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.util.AuthJumpUtil
import com.fenqi.main.view.dialog.AuthBackTipDialog

class PersonalInfoActivity:
    BaseActivity<MainApiService, PersonalInfoMvvmView, PersonalInfoViewModel, ActivityPersonalInformationBinding>(),PersonalInfoMvvmView {

    companion object {
        fun callIntent(context: Context): Intent {
            return Intent(context, PersonalInfoActivity::class.java)
        }
    }

    private lateinit var personalInfoAdapter: PersonalInfoAdapter

    override fun onShowViewById(): Int {
        return R.layout.activity_personal_information
    }

    override fun kinit() {
        dataBinding?.viewModel = viewModel
        dataBinding?.click = this

        val relateArrow: RelativeLayout = findViewById(R.id.view_base_title_return_back)
        relateArrow.setOnClickListener {
            showReturnBackView()
        }

        initView()

        viewModel?.userAuthInfo()
    }

    private fun showReturnBackView(){
        if(viewModel?.finished == true){
            finish()
        } else {
            val authBackTipDialog = AuthBackTipDialog(activity)
            authBackTipDialog.setConfirmLogic {
                finish()
            }
            authBackTipDialog.showDialog()
        }
    }

    private fun initView() {
        personalInfoAdapter = PersonalInfoAdapter(mutableListOf(),activity,object :BaseRecyclerViewAdapter.BaseRecyclerViewCallBack{
            override fun onClickListener(position: Int) {

            }
        })
        dataBinding?.recyclerviewPersonal?.apply {
            layoutManager = LinearLayoutManager(activity)
            setHasFixedSize(true)
            adapter = personalInfoAdapter
        }
    }

    fun clickSubmit(){
        showLoading()
        viewModel?.userAuthSave(personalInfoAdapter.datas)
    }

    override fun attachViewModel(): Class<PersonalInfoViewModel> {
        return PersonalInfoViewModel::class.java
    }

    override fun attachMvvmView(): PersonalInfoMvvmView {
        return this
    }

    override fun attachTitleText(): String {
        return getString(R.string.personal_information)
    }

    override fun userAuthInfoSuccess(userInputInfoVOList: MutableList<UserInfoVoEntity>) {
        for(userInfoVoEntity:UserInfoVoEntity in userInputInfoVOList){
            val inputParams = userInfoVoEntity.inputParams
            inputParams?.let {
                for(userInfoItemEntity: UserInfoItemEntity in inputParams){
                    if(userInfoItemEntity.paramType == CommonConstant.DATA_VIEW_STATUS_PICKER && !TextUtils.isEmpty(userInfoItemEntity.inputValue)){
                        userInfoItemEntity.selectVo?.let {
                            for(selectVoItem:SelectVoEntity in it){
                                if(selectVoItem.type.toString() == userInfoItemEntity.inputValue){
                                    userInfoItemEntity.inputValue = selectVoItem.name
                                    break
                                }
                            }
                        }
                    }
                }
            }

        }
        personalInfoAdapter.addNewList(userInputInfoVOList)
    }

    override fun userAuthSaveSuccess() {
        showToast(getString(R.string.person_info_success_tip))

        AuthJumpUtil.getInstance().setFinishedCallBack {
            dismissLoading()
            finish()
        }.setOnFailed {
            dismissLoading()
        }.judgeJump(activity)
    }

    override fun requestError(message: String, code: Int) {
        dismissLoading()
        showToast(message)
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {
        showLoading()
    }

    override fun onBackPressed() {
        if (viewModel?.finished == false) {
            showReturnBackView()
            return
        }
        super.onBackPressed()
    }
}