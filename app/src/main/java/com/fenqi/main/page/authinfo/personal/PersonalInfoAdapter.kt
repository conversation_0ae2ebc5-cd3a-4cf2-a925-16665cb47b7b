package com.fenqi.main.page.authinfo.personal

import android.content.Context
import androidx.recyclerview.widget.LinearLayoutManager
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.bean.UserInfoVoEntity
import com.fenqi.main.databinding.AdapterPersonalInfoBinding

class PersonalInfoAdapter(
    datas: MutableList<UserInfoVoEntity>,
    context: Context,
    baseRecyclerViewCallBack: BaseRecyclerViewCallBack?
) : BaseRecyclerViewAdapter<UserInfoVoEntity, AdapterPersonalInfoBinding>(
    datas,
    context,
    baseRecyclerViewCallBack
) {
    override fun onBaseBindViewHolder(
        holder: BaseRecyclerViewHolder<AdapterPersonalInfoBinding>,
        position: Int
    ) {
        val databinding = holder.itemDataBinding
        val userInfoVoEntity = datas[position]

        val personalInfoSubsetAdapter = PersonalInfoSubsetAdapter(mutableListOf(),context,object :BaseRecyclerViewCallBack{
            override fun onClickListener(position: Int) {

            }
        })
        personalInfoSubsetAdapter.setPersonalInfoSubsetCallBack { data, positionSubset ->
            userInfoVoEntity.inputParams?.set(positionSubset, data)
        }
        personalInfoSubsetAdapter.setContactInfoSubsetCallBack {positionContact,type ->
            contactInfoSubsetCallBack.invoke(position,positionContact,type)
        }
        databinding.recyclerviewAdapterPersonalInfo.apply {
            layoutManager = LinearLayoutManager(context)
            setHasFixedSize(true)
            adapter = personalInfoSubsetAdapter
        }
        userInfoVoEntity.inputParams?.let { personalInfoSubsetAdapter.addNewList(it) }
    }

    private lateinit var contactInfoSubsetCallBack:(firstPosition:Int, position:Int,type:Int)->Unit

    fun setContactInfoSubsetCallBack(contactInfoSubsetCallBack:(firstPosition:Int, position:Int,type:Int)->Unit) {
        this.contactInfoSubsetCallBack = contactInfoSubsetCallBack
    }

    override fun attachLayout(): Int {
        return R.layout.adapter_personal_info
    }
}