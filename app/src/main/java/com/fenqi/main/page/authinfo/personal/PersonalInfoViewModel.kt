package com.fenqi.main.page.authinfo.personal

import android.text.TextUtils
import com.google.gson.JsonObject
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.bean.UserInfoItemEntity
import com.fenqi.main.bean.UserInfoResponseEntity
import com.fenqi.main.bean.UserInfoVoEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService

class PersonalInfoViewModel: BaseViewModel<MainApiService, PersonalInfoMvvmView>() {

    private var userInputInfoVOList:MutableList<UserInfoVoEntity>? = mutableListOf()
    var finished:Boolean = false

    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }

    fun userAuthInfo(){
        RetrofitBuilder.getInstance().start({
            onSuccess { it ->
                it?.let { it ->
                    val data: UserInfoResponseEntity = json.decodeFromString(it)
                    finished = data.finished
                    userInputInfoVOList = data.userInputInfoVOList
                    val userInputInfoVOListNew: MutableList<UserInfoVoEntity> = mutableListOf()
                    userInputInfoVOList?.let {
                        for(userInfoVoEntity:UserInfoVoEntity in it){
                            val inputParams = mutableListOf<UserInfoItemEntity>()
                            val inputParamsData = userInfoVoEntity.inputParams
                            inputParamsData?.let {
                                for (userInfoItemEntity: UserInfoItemEntity in inputParamsData){
                                    if(userInfoItemEntity.show){
                                        inputParams.add(userInfoItemEntity)
                                    }
                                }
                                val userInfoVoEntityData = UserInfoVoEntity(name=userInfoVoEntity.name,enabled=userInfoVoEntity.enabled,inputParams=inputParams)
                                userInputInfoVOListNew.add(userInfoVoEntityData)
                            }
                        }
                        mvvmView?.userAuthInfoSuccess(userInputInfoVOListNew)
                    }

                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.userAuthInfo()
        }
    }

    fun userAuthSave(dataValue:MutableList<UserInfoVoEntity>){
        var canNext = true
        for(userInfoVoEntity:UserInfoVoEntity in dataValue){
            val inputParams = userInfoVoEntity.inputParams
            var hasError = false
            inputParams?.let {
                for(userInfoItemEntity: UserInfoItemEntity in inputParams){
                    if(userInfoItemEntity.show && userInfoItemEntity.requied && TextUtils.isEmpty(userInfoItemEntity.inputValue)){
                        mvvmView?.showModelToast("${userInfoItemEntity.inputDesc}${userInfoItemEntity.paramName}")
                        hasError = true
                        canNext = false
                        break
                    }
                }
            }
            if(hasError){
                break
            }
        }
        if(canNext){
            val jsonObject = JsonObject()
            for(userInfoVoEntity:UserInfoVoEntity in dataValue){
                val inputParams = userInfoVoEntity.inputParams
                inputParams?.let {
                    for(userInfoItemEntity: UserInfoItemEntity in inputParams){
                        if(userInfoItemEntity.paramType == CommonConstant.DATA_VIEW_STATUS_PICKER){
                            val selectValue = userInfoItemEntity.inputValue
                            userInfoItemEntity.selectVo?.let {
                                var selectType = ""
                                if(it.size>0){
                                    for(index in it.indices){
                                        if(it[index].name == selectValue){
                                            selectType = it[index].type.toString()
                                        }
                                    }
                                }
                                jsonObject.addProperty(userInfoItemEntity.param,selectType)
                            }
                        } else {
                            jsonObject.addProperty(userInfoItemEntity.param,userInfoItemEntity.inputValue)
                        }
                    }
                }

            }

            RetrofitBuilder.getInstance().start({
                onSuccess {
                    mvvmView?.userAuthSaveSuccess()
                }
                onFailure { msg, code ->
                    mvvmView?.requestError(msg,code)
                }
            }) {
                mService?.userAuthBaseSave(RetrofitBuilder.getInstance().getRequestBody(jsonObject))
            }
        } else {
            mvvmView?.dismissModelLoading()
        }
    }
}