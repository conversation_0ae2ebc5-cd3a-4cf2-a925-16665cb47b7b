package com.fenqi.main.page.login

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.core.content.ContextCompat
import androidx.databinding.Observable
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.databinding.ActivityLoginBinding
import com.fenqi.main.page.main.MainActivity
import com.fenqi.main.page.verifycode.VerifyCodeActivity
import com.fenqi.main.page.webprivacy.WebPrivacyActivity
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.platformtools.utils.TitleBar
import java.util.regex.Pattern

public class LoginActivity :
    BaseActivity<MainApiService, LoginMvvmView, LoginViewModel, ActivityLoginBinding>(),
    LoginMvvmView {

    companion object {
        fun callIntent(context: Context): Intent {
            return Intent(context, LoginActivity::class.java)
        }
    }

    override fun onShowViewById(): Int {
        return R.layout.activity_login
    }

    @SuppressLint("SetTextI18n")
    override fun kinit() {
        TitleBar.changeTransparencyBar(activity)
        dataBinding?.loginClick = this
        dataBinding?.loginModel = viewModel

        dataBinding?.buttonLoginTitle?.text = getString(R.string.login_title_tip)+getString(R.string.app_name)

        initView()
        loadData()
    }

    override fun onResume() {
        super.onResume()
    }

    private fun initView() {
        viewModel?.phoneNumber?.addOnPropertyChangedCallback(object: Observable.OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                viewModel?.phoneNumber?.get()?.length?.let {
                    if(it>10){
                        dataBinding?.buttonSubmit?.setBackgroundResource(R.drawable.select_button_main)
                        dataBinding?.buttonSubmit?.setTextColor(ContextCompat.getColor(activity,R.color.color_config_text_main))
                        dataBinding?.buttonSubmit?.isEnabled = true
                    } else {
                        dataBinding?.buttonSubmit?.setBackgroundResource(R.drawable.shape_5r_grey)
                        dataBinding?.buttonSubmit?.setTextColor(ContextCompat.getColor(activity,R.color.color_white))
                        dataBinding?.buttonSubmit?.isEnabled = false
                    }
                }
            }
        })

        val agreeText = "        温馨提示:未注册${getString(R.string.app_name)}的手机号,登录时将自动注册，且代表您已同意《用户注册协议》《隐私保护政策》"
        val agreeColor = ContextCompat.getColor(activity,R.color.color_config_main)

        val spannableStringBuilder = SpannableStringBuilder()
        spannableStringBuilder.append(agreeText)

        val clickableSpan1: ClickableSpan = object : ClickableSpan() {
            override fun onClick(view: View) {
                viewModel?.privacyBeanList?.let {
                    if(it.size>0){
                        startActivity(WebPrivacyActivity.callIntent(activity,0))
                    }
                }
            }
        }
        val clickableSpan2: ClickableSpan = object : ClickableSpan() {
            override fun onClick(view: View) {
                viewModel?.privacyBeanList?.let {
                    if(it.size>1){
                        startActivity(WebPrivacyActivity.callIntent(activity,1))
                    }
                }
            }
        }

        val start1 = getString(R.string.app_name).length+37
        val end1 = start1+8

        spannableStringBuilder.setSpan(
            clickableSpan1,
            start1, end1,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannableStringBuilder.setSpan(
            clickableSpan2,
            end1, agreeText.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        val foregroundColorSpan1 = ForegroundColorSpan(agreeColor)
        val foregroundColorSpan2 = ForegroundColorSpan(agreeColor)

        spannableStringBuilder.setSpan(
            foregroundColorSpan1,
            start1, end1,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannableStringBuilder.setSpan(
            foregroundColorSpan2,
            end1, agreeText.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        dataBinding?.buttonAgreementLogin?.movementMethod = LinkMovementMethod.getInstance()
        dataBinding?.buttonAgreementLogin?.text = spannableStringBuilder

        dataBinding?.viewAgreementCc?.setOnClickListener {
            if(viewModel?.isAgree?.get() == true){
                viewModel?.isAgree?.set(false)
                dataBinding?.imgAgreementCheck?.setBackgroundResource(R.drawable.check_not)
            } else {
                viewModel?.isAgree?.set(true)
                dataBinding?.imgAgreementCheck?.setBackgroundResource(R.drawable.check_yes)
            }
        }
    }

    private fun loadData() {
        viewModel?.getPrivacyPolicyList(mutableListOf(CommonConstant.PRIVACY_POLICY_ZCXY,CommonConstant.PRIVACY_POLICY_YSZC))
    }

    fun clickGoBack(){
        finish()
    }

    fun login() {
        if(viewModel?.isAgree?.get() == false){
            showToast(getString(R.string.agreement_tip))
            return
        }
        if (TextUtils.isEmpty(viewModel?.phoneNumber?.get())) {
            showToast(getString(R.string.verification_login_hint_mobile_length))
            return
        }
        if(!TextUtils.isEmpty(viewModel?.mobileRegExp?.get()) && !TextUtils.isEmpty(viewModel?.phoneNumber?.get()) && !Pattern.matches(viewModel?.mobileRegExp?.get().toString(),viewModel?.phoneNumber?.get().toString())){
            showToast(getString(R.string.verification_login_hint_mobile_length))
            return
        }
        startActivity(VerifyCodeActivity.callIntent(activity,viewModel?.phoneNumber?.get()))
    }

    override fun loginSuccess() {
        dismissLoading()

        startActivity(MainActivity.callIntent(activity))
        finish()
    }

    override fun verifySendSuccess(seconds: Int?) {
        dismissLoading()
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {
        showLoading()
    }

    override fun attachViewModel(): Class<LoginViewModel> {
        return LoginViewModel::class.java
    }

    override fun attachTitleText(): String? {
        return null
    }

    override fun requestError(message: String, code: Int) {
        dismissLoading()
        showToast(message)
    }

    override fun attachMvvmView(): LoginMvvmView {
        return this
    }

    override fun onDestroy() {
        super.onDestroy()
    }

}