package com.fenqi.main.page.login

import android.text.TextUtils
import androidx.databinding.ObservableField
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.bean.*
import com.fenqi.main.constant.CommonData
import com.fenqi.main.postentity.EquivalentRegardingOrganizeEntity
import com.fenqi.main.postentity.FailCreatedThroatMatchEntity
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.sharedata.SharePreferenceData
import com.fenqi.main.util.AppInfoUploadUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

public class LoginViewModel : BaseViewModel<MainApiService, LoginMvvmView>() {

    var phoneNumber: ObservableField<String> = ObservableField<String>().apply { set("") }
    var otp: ObservableField<String> = ObservableField<String>()
    var mobileRegExp: ObservableField<String> = ObservableField<String>().apply { set("") }

    fun verifyCodeSend(verifyCodeSendPostEntity: FailCreatedThroatMatchEntity){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val verifyCodeSendResponseEntity: VerifyCodeSendResponseEntity = json.decodeFromString(it)
                    if(verifyCodeSendResponseEntity!=null){
                        val verifyCodeSendVo = verifyCodeSendResponseEntity.sendVerifyCodeSuccessVo
                        mvvmView?.verifySendSuccess(verifyCodeSendVo?.effectiveSeconds)
                    }
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.verifyCodeSend(RetrofitBuilder.getInstance().getRequestBody(verifyCodeSendPostEntity))
        }
    }

    fun login(loginPostEntity: EquivalentRegardingOrganizeEntity){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: LoginResponseEntity = json.decodeFromString(it)
                    if(!TextUtils.isEmpty(data.token)){
                        SharePreferenceData.setToken(data.token.toString())
                        SharePreferenceData.setPhoneNumber(phoneNumber.get().toString())

                        AppInfoUploadUtil.getInstance().deviceInfoUpload()
                        mvvmView?.loginSuccess()
                    }
                    if (data.status==2){
                        SharePreferenceData.setIsRc(true)
                    }
                }

            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.userLogin(RetrofitBuilder.getInstance().getRequestBody(loginPostEntity))
        }
    }

    var privacyBeanList: MutableList<PrivacyPolicyBean> = mutableListOf()
    var isAgree: ObservableField<Boolean> = ObservableField<Boolean>().apply { set(true) }
    fun getPrivacyPolicyList(keyList: MutableList<String>){
        privacyBeanList.clear()

        var tag = 0

        for (index in keyList.indices) {
            val key = keyList[index]
            if (!TextUtils.isEmpty(key)) {
                CoroutineScope(Dispatchers.IO).launch {
                    delay(100)
                    RetrofitBuilder.getInstance().start({
                        onSuccess {
                            it?.let {
                                val data: PrivacyPolicyBean = json.decodeFromString(it)
                                tag++
                                data.let { privacyBean ->
                                    privacyBeanList.add(privacyBean)
                                }
                                if(tag>=keyList.size){
                                    CommonData.privacyPolicyList = privacyBeanList
                                }
                            }
                        }
                        onFailure { _, _ ->
                            tag++
                            if(tag>=keyList.size){
                                CommonData.privacyPolicyList = privacyBeanList
                            }
                        }
                    }) {
                        mService?.userProtocolInfos(key)
                    }
                }
            }
        }
    }

    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }
}