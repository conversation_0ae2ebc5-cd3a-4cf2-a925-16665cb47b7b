package com.fenqi.main.page.main

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import com.fenqi.platformtools.utils.TitleBar
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.databinding.ActivityMainBinding
import com.fenqi.main.event.MainEvent
import com.fenqi.main.page.main.fragment.home.HomeFragment
import com.fenqi.main.page.mine.MineFragment
import com.fenqi.main.page.repayment.RepaymentFragment
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.util.event.EventConstants
import com.fenqi.main.view.MainTabView
import com.fenqi.platformtools.utils.ActivityManager
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe

class MainActivity :
    BaseActivity<MainApiService, MainMvvmView, MainViewModel, ActivityMainBinding>(), MainMvvmView {

    var homeFragment: HomeFragment? = null
    var mineFragment: MineFragment? = null
    var repaymentFragment: RepaymentFragment?=null

    private var currentFragment: Fragment? = null

    companion object {

        const val FRAGMENT_HOME = 1
        const val FRAGMENT_REPAYMENT = 2
        const val FRAGMENT_MINE = 3

        fun callIntent(context: Context): Intent {
            return Intent(context, MainActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun onShowViewById(): Int {
        return R.layout.activity_main
    }

    @SuppressLint("SimpleDateFormat", "CheckResult")
    override fun kinit() {
        TitleBar.changeTransparencyBar(activity)
        dataBinding?.mainModel = viewModel
        dataBinding?.click = this

        initFragment(FRAGMENT_HOME)

        dataBinding?.tabViewMain?.setMainTabClickCallBack(object : MainTabView.MainTabClickCallBack {
            override fun onMainTabClick(position: Int) {
                initFragment(position)

            }
        })
    }

    private fun initFragment(position:Int){
        try {
            val fragmentTransaction = supportFragmentManager.beginTransaction()
            when (position) {
                FRAGMENT_HOME -> {
                    if (homeFragment == null) {
                        homeFragment = HomeFragment()
                        fragmentTransaction.add(
                            R.id.view_main_fragment_container,
                            homeFragment!!,
                            HomeFragment::class.java.simpleName
                        )
                    } else {
                        fragmentTransaction.show(homeFragment!!)
                    }
                    mineFragment?.let {
                        fragmentTransaction.hide(it)
                    }
                    repaymentFragment?.let {
                        fragmentTransaction.hide(it)
                    }
                    fragmentTransaction.commit()
                    currentFragment = homeFragment
                }
                FRAGMENT_REPAYMENT -> {
                    if (repaymentFragment == null) {
                        repaymentFragment = RepaymentFragment()
                        fragmentTransaction.add(
                            R.id.view_main_fragment_container,
                            repaymentFragment!!,
                            MineFragment::class.java.simpleName
                        )
                    } else {
                        fragmentTransaction.show(repaymentFragment!!)
                    }
                    homeFragment?.let {
                        fragmentTransaction.hide(it)
                    }
                    mineFragment?.let {
                        fragmentTransaction.hide(it)
                    }
                    fragmentTransaction.commit()
                    currentFragment = repaymentFragment
                }
                FRAGMENT_MINE -> {
                    if (mineFragment == null) {
                        mineFragment = MineFragment()
                        fragmentTransaction.add(
                            R.id.view_main_fragment_container,
                            mineFragment!!,
                            MineFragment::class.java.simpleName
                        )
                    } else {
                        fragmentTransaction.show(mineFragment!!)
                    }
                    homeFragment?.let {
                        fragmentTransaction.hide(it)
                    }
                    repaymentFragment?.let {
                        fragmentTransaction.hide(it)
                    }
                    fragmentTransaction.commit()
                    currentFragment = mineFragment
                }
            }
        } catch (ex: InstantiationException) {
            ex.printStackTrace()
        }
    }

    @SuppressLint("HardwareIds")
    override fun onResume() {
        super.onResume()
        viewModel?.configAppCodeGet()
        viewModel?.userCustomerServiceGet()
        homeFragment?.getServiceData()
        mineFragment?.getServiceData()
        repaymentFragment?.getServiceData()
    }

    @SuppressLint("MissingSuperCall")
    override fun onSaveInstanceState(outState: Bundle) {
//        super.onSaveInstanceState(outState)
    }

    override fun attachViewModel(): Class<MainViewModel> {
        return MainViewModel::class.java
    }

    override fun attachTitleText(): String? {
        return null
    }

    override fun attachMvvmView(): MainMvvmView {
        return this
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {

    }

    override fun refreshApi() {
        if(homeFragment!=null){
            homeFragment?.rebindApiService()
        }
        if(mineFragment!=null){
            mineFragment?.rebindApiService()
        }
        if(repaymentFragment!=null){
            repaymentFragment?.rebindApiService()
        }
    }

    override fun requestError(message: String, code: Int) {
        showToast(message)
        dismissLoading()
    }

    private var lastClickBackKeyTime:Long = 0
    override fun onBackPressed() {
        if (System.currentTimeMillis() - lastClickBackKeyTime > 900) {
            showToastShort(getString(R.string.please_again_to_exit));
            lastClickBackKeyTime = System.currentTimeMillis();
            return;
        }
        ActivityManager.getActivityManager().destroyAllActivity()
        super.onBackPressed()
    }

    @Subscribe
    public fun onEventPost(event: MainEvent){
        if(event.event == EventConstants.EVENT_GO_REPAYMENT){
            initFragment(FRAGMENT_REPAYMENT)
            dataBinding?.tabViewMain?.setTargetIndex(FRAGMENT_REPAYMENT)
        }
        if(event.event == EventConstants.EVENT_GO_HOME){
            initFragment(FRAGMENT_HOME)
            dataBinding?.tabViewMain?.setTargetIndex(FRAGMENT_HOME)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }
}