package com.fenqi.main.page.main

import android.app.Activity
import android.content.Context
import com.fenqi.main.bean.MainHomeCenterVoEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.constant.CommonData
import com.fenqi.main.page.apply.ApplyNowActivity
import com.fenqi.main.page.authinfo.bindbank.BindBankActivity
import com.fenqi.main.page.authinfo.bindbank.bindnew.BindBankNewActivity
import com.fenqi.main.page.authinfo.bindbank.cardlist.BankCardListActivity
import com.fenqi.main.page.authinfo.emergencycontact.EmergencyContactActivity
import com.fenqi.main.page.authinfo.faceocr.FaceOcrActivity
import com.fenqi.main.page.authinfo.personal.PersonalInfoActivity
import com.fenqi.platformtools.utils.ActivityManager


class MainJumpUtil {

    companion object {
        fun getInstance(): MainJumpUtil {
            return MainJumpUtilBuilderHelper.instance
        }
    }

    private object MainJumpUtilBuilderHelper {
        val instance = MainJumpUtil()
    }

    fun jump(activity: Activity, url: String,centerVoEntity: MainHomeCenterVoEntity) {
        when (url) {
            CommonConstant.STATUS_HOME_PRODUCT_CARD_LIST->{
                activity.startActivity(BankCardListActivity.callIntent(activity,BindBankNewActivity.BIND_BANK_TYPE_ORDER_SUBMIT))
            }
            CommonConstant.STATUS_HOME_PRODUCT_CONFIRM_TRADE->{
                centerVoEntity.tradeNo?.let {
                    activity.startActivity(ApplyNowActivity.callIntent(activity,
                        it
                    ))
                }
            }
            else -> {
            }
        }
    }

    fun authListJump(context: Context, url: String) {
        when (url) {
            CommonConstant.AUTH_USER_BANK_CARD->{
                context.startActivity(BindBankActivity.callIntent(context,""))
            }
            CommonConstant.AUTH_USER_BANK_CARD_NEW->{
                context.startActivity(BindBankNewActivity.callIntent(context,CommonData.BIND_TYPE))
            }
            CommonConstant.AUTH_USER_PERSONAL_INFO -> {
                context.startActivity(PersonalInfoActivity.callIntent(context))
            }
            CommonConstant.AUTH_USER_CONTACT_INFO -> {
                context.startActivity(EmergencyContactActivity.callIntent(context))
            }
            CommonConstant.AUTH_USER_FACE_OCR -> {
                ActivityManager.getActivityManager().destroyTargetActivity(FaceOcrActivity::class.java)
                context.startActivity(FaceOcrActivity.callIntent(context))
            }
            else -> {
            }
        }
    }
}