package com.fenqi.main.page.main

import androidx.databinding.ObservableField
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.bean.AppCodeConfigEntity
import com.fenqi.main.bean.CustomerServiceGetEntity
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.sharedata.SharePreferenceData

class MainViewModel : BaseViewModel<MainApiService, MainMvvmView>() {

    override fun bindService(): MainApiService {
        if(mvvmView!=null){
            mvvmView?.refreshApi()
        }
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }

    var serviceUrl: ObservableField<String> = ObservableField<String>().apply { set("") }

    fun userCustomerServiceGet(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: CustomerServiceGetEntity = json.decodeFromString(it)
                    serviceUrl.set(data.customerServiceUrl)
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg, code)
            }
        }) {
            mService?.userCustomerServiceGet()
        }
    }

    fun configAppCodeGet(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: AppCodeConfigEntity = json.decodeFromString(it)
                    SharePreferenceData.setFileUploadType(data.fileUploadType)
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.configAppCodeGet()
        }
    }


}