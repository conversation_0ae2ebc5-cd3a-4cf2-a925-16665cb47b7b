package com.fenqi.main.page.main.fragment.home

import android.content.Context
import android.text.TextUtils
import androidx.databinding.ObservableField
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.bean.*
import com.fenqi.main.constant.CommonData
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.util.AuthJumpUtil
import com.fenqi.main.view.dialog.CommonMessageDialog

class HomeFragmentViewModel: BaseViewModel<MainApiService, HomeFragmentMvvmView>() {

    var nextJumpUrl:String = ""

    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }

    var showHomeBottomBg: ObservableField<Boolean> = ObservableField<Boolean>().apply { set(false) }

    var centerVo:MainHomeCenterVoEntity? = null

    var serviceUrl: ObservableField<String> = ObservableField<String>().apply { set("") }

    fun userCustomerServiceGet(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: CustomerServiceGetEntity = json.decodeFromString(it)
                    serviceUrl.set(data.customerServiceUrl)
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg, code)
            }
        }) {
            mService?.userCustomerServiceGet()
        }
    }

    fun userHome(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: MainHomeCenterVoEntity = json.decodeFromString(it)
                    centerVo = data
                    CommonData.centerVo = data
                    mvvmView?.userHomeSuccess(data)
                }

            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.userHome()
        }
    }

    fun orderBorrowConfirm(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: BorrowAgainBean = json.decodeFromString(it)
                    if(!TextUtils.isEmpty(data.jumpUrl)){
                        AuthJumpUtil.getInstance().setFinishedCallBack {
                            mvvmView?.dismissModelLoading()
                        }.setOnFailed {
                            mvvmView?.dismissModelLoading()
                        }.judgeJump(activity as Context)
                    } else {
                        mvvmView?.applyNow()
                    }
                }
            }
            onFailure { msg, code ->
                activity?.let { context->
                    val commonMessageDialog = CommonMessageDialog(context)
                    commonMessageDialog.setMessage(msg)
                    commonMessageDialog.setSingle()
                    commonMessageDialog.showDialog()
                }
                mvvmView?.dismissModelLoading()
            }
        }) {
            mService?.orderBorrowConfirm()
        }
    }
}