package com.fenqi.main.page.main.fragment.home

import android.annotation.SuppressLint
import android.text.TextUtils
import android.view.View
import android.widget.LinearLayout
import com.fenqi.main.R
import com.fenqi.main.base.BaseFragment
import com.fenqi.main.bean.MainHomeResponseEntity
import com.fenqi.main.bean.SlideshowVOBean
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.constant.CommonData
import com.fenqi.main.databinding.FragmentHomeBinding
import com.fenqi.main.page.main.view.*
import com.fenqi.main.page.web.CommonWebActivity
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.util.SystemUtil
import com.fenqi.main.view.banner.ImageAdapter
import com.bumptech.glide.Glide
import com.fenqi.main.bean.MainHomeCenterVoEntity
import com.fenqi.main.page.login.LoginActivity
import com.fenqi.main.sharedata.SharePreferenceData
import com.youth.banner.indicator.CircleIndicator

class HomeFragment:
    BaseFragment<MainApiService, HomeFragmentMvvmView, HomeFragmentViewModel, FragmentHomeBinding>(),HomeFragmentMvvmView {

    private var adapterBanner:ImageAdapter?=null
    private var dialogAuthTip:AuthTipDialog?=null

    override fun onShowViewById(): Int {
        return R.layout.fragment_home
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if(!hidden){
            getServiceData()
        }
    }

    override fun kfragmentInit() {
        dataBinding?.viewModel = viewModel
        dataBinding?.click = this

        initStatusBarHeight()
        initRefreshView()
        initView()

        getServiceData()
    }

    @SuppressLint("SetTextI18n")
    private fun initView() {
        if(adapterBanner == null){
            adapterBanner = ImageAdapter(mutableListOf(),requireActivity())
            dataBinding?.bannerHome?.setAdapter(adapterBanner)
                //添加生命周期观察者
                ?.addBannerLifecycleObserver(this) //设置指示器
                ?.setIndicator(CircleIndicator(requireActivity()))
                ?.setOnBannerListener { data: Any, position: Int ->
                    val slideshowVOBean = data as SlideshowVOBean
                    if(!TextUtils.isEmpty(slideshowVOBean.jumpUrl)){
                        startActivity(CommonWebActivity.callIntent(requireActivity(),
                            slideshowVOBean.title.toString(), slideshowVOBean.jumpUrl.toString()
                        ))
                    }
                }
        }
    }

    fun getServiceData() {
        viewModel?.userHome()
        viewModel?.userCustomerServiceGet()
    }

    fun rebindApiService(){
        viewModel?.bindService()
    }

    private fun initStatusBarHeight() {
        val layoutParams = LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,LinearLayout.LayoutParams.WRAP_CONTENT)
        layoutParams.height = SystemUtil.getInstance().getStatusBarHeight()
    }

    private fun initRefreshView() {
        dataBinding?.refreshviewMainHome?.apply {
            setOnRefreshListener {
                getServiceData()
            }
        }
    }

    override fun attachViewModel(): Class<HomeFragmentViewModel> {
        return HomeFragmentViewModel::class.java
    }

    override fun attachMvvmView(): HomeFragmentMvvmView {
        return this
    }

    override fun userHomeSuccess(mainHomeResponseEntity: MainHomeCenterVoEntity?) {
        if(activity?.isFinishing!! || activity?.isDestroyed!!){
            return
        }

        dismissLoading()

        dataBinding?.refreshviewMainHome?.finishRefresh()
        dataBinding?.linearMainHomeContainer?.removeAllViews()

        mainHomeResponseEntity?.let { it ->
            if(it.buttonJumpUrl == CommonConstant.STATUS_HOME_ROUTE_USER_DATA){
                if(CommonData.IS_FIRST_OPEN){
                    if(dialogAuthTip == null){
                        dialogAuthTip = AuthTipDialog(requireActivity())
                        dialogAuthTip?.setConfirmLogic{
                            if(TextUtils.isEmpty(SharePreferenceData.getToken())){
                                startActivity(LoginActivity.callIntent(requireActivity()))
                            } else {
                                showLoading()
                                viewModel?.orderBorrowConfirm()
                            }
                        }
                    }
                    dialogAuthTip?.setData(it.maxAmount.toString(), it.buttonTxt.toString())
                    dialogAuthTip?.showDialog()
                }
            }

            if(it.slideshowVOList == null){
                dataBinding?.viewHomeBannerContainer?.visibility = View.GONE
            } else {
                if(it.slideshowVOList!!.size>0){
                    dataBinding?.viewHomeBannerContainer?.visibility = View.VISIBLE
                    adapterBanner?.updateData(it.slideshowVOList)
                } else {
                    dataBinding?.viewHomeBannerContainer?.visibility = View.GONE
                }
            }

            if(mainHomeResponseEntity.templateCode == CommonConstant.STATUS_HOME){
                viewModel?.showHomeBottomBg?.set(true)
            } else {
                viewModel?.showHomeBottomBg?.set(false)
            }

            CommonData.HOME_STATUS = mainHomeResponseEntity.buttonJumpUrl.toString()

            if(mainHomeResponseEntity.templateCode == CommonConstant.STATUS_HOME_LOAN_REFUSE){
                if(mainHomeResponseEntity.refuseSlideshow == null){
                    dataBinding?.viewRefuseBorrowOther?.visibility = View.GONE
                }
                mainHomeResponseEntity.refuseSlideshow?.let {
                    if(TextUtils.isEmpty(it.imgUrl)){
                        dataBinding?.viewRefuseBorrowOther?.visibility = View.GONE
                    } else {
                        dataBinding?.viewRefuseBorrowOther?.visibility = View.VISIBLE
                        dataBinding?.imageRefuseBorrowOther?.let { it1 ->
                            Glide.with(requireActivity()).load(it.imgUrl).into(
                                it1
                            )
                        }
                        dataBinding?.imageRefuseBorrowOther?.setOnClickListener {
                            startActivity(CommonWebActivity.callIntent(requireActivity(),mainHomeResponseEntity.refuseSlideshow?.title.toString(),mainHomeResponseEntity.refuseSlideshow?.jumpUrl.toString()))
                        }
                    }
                }
            } else {
                dataBinding?.viewRefuseBorrowOther?.visibility = View.GONE
            }

            when(mainHomeResponseEntity.templateCode){
                CommonConstant.STATUS_HOME->{
                    val mainHomeView = MainHomeView(activity,mainHomeResponseEntity)
                    mainHomeView.setAuthJump {
                        showLoading()
                        viewModel?.orderBorrowConfirm()
                    }
                    dataBinding?.linearMainHomeContainer?.addView(mainHomeView)
                }

                CommonConstant.STATUS_HOME_WAIT_REVIEW->{
                    val mainAuthView = MainHomeAuthView(activity,mainHomeResponseEntity)
                    mainAuthView.setCountDownFinished {
                        viewModel?.userHome()
                    }
                    dataBinding?.linearMainHomeContainer?.addView(mainAuthView)
                }

                CommonConstant.STATUS_HOME_WAIT_FOR_PAY->{
                    val mainHomeWaitForPayView = MainHomeWaitForPayView(activity,mainHomeResponseEntity)
                    dataBinding?.linearMainHomeContainer?.addView(mainHomeWaitForPayView)
                }

                CommonConstant.STATUS_HOME_WAIT_REPAYMENT,CommonConstant.STATUS_HOME_REPAYMENT_OVERDUE->{

                }

                CommonConstant.STATUS_HOME_LOAN_REFUSE->{
                    val mainHomeLoanRefuse = MainHomeLoanRefuse(activity,mainHomeResponseEntity)
                    dataBinding?.linearMainHomeContainer?.addView(mainHomeLoanRefuse)
                }
                CommonConstant.STATUS_HOME_CONFIRM_TRADE,CommonConstant.STATUS_HOME_WAIT_SIGN->{
                    val mainHomeConfirmContractView = MainHomeConfirmContractView(activity,mainHomeResponseEntity)
                    dataBinding?.linearMainHomeContainer?.addView(mainHomeConfirmContractView)
                }
                else -> {}
            }

            if(mainHomeResponseEntity.homeMessages?.isNotEmpty() == true){
                dataBinding?.marqueeviewHome?.startWithList(mainHomeResponseEntity.homeMessages)
                dataBinding?.viewHomeMarqueeviewContainer?.visibility = View.VISIBLE
            } else {
                dataBinding?.viewHomeMarqueeviewContainer?.visibility = View.GONE
            }
        }
    }

    override fun applyNow() {
        viewModel?.userHome()
    }

    fun clickCustomerService(){
        if(!TextUtils.isEmpty(viewModel?.serviceUrl?.get())){
            startActivity(CommonWebActivity.callIntent(requireActivity(),getString(R.string.customer_service),viewModel?.serviceUrl?.get().toString()))
        }
    }

    override fun requestError(message: String, code: Int) {
        showToast(message)
        dismissLoading()
        dataBinding?.refreshviewMainHome?.finishRefresh()
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {

    }

}