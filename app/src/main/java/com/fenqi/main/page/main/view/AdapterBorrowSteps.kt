package com.fenqi.main.page.main.view

import android.annotation.SuppressLint
import android.content.Context
import android.view.View
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.bean.BorrowStepVoItemBean
import com.fenqi.main.databinding.AdapterBorrowStepsBinding

class AdapterBorrowSteps(
    datas: MutableList<BorrowStepVoItemBean>,
    context: Context,
    baseRecyclerViewCallBack: BaseRecyclerViewCallBack?
) : BaseRecyclerViewAdapter<BorrowStepVoItemBean, AdapterBorrowStepsBinding>(
    datas,
    context,
    baseRecyclerViewCallBack
) {

    @SuppressLint("SetTextI18n", "NotifyDataSetChanged")
    override fun onBaseBindViewHolder(
        holder: BaseRecyclerViewHolder<AdapterBorrowStepsBinding>,
        position: Int
    ) {
        val borrowSteps = datas[position]
        val dataBinding = holder.itemDataBinding

        if(borrowSteps.finished){
            dataBinding.imgAdapterBorrowStepsStatus.setBackgroundResource(R.drawable.check_yes)
        } else {
            dataBinding.imgAdapterBorrowStepsStatus.setBackgroundResource(R.drawable.check_not_d2)
        }

        if(position == datas.size-1){
            dataBinding.lineBorrowSteps.visibility = View.GONE
        } else {
            dataBinding.lineBorrowSteps.visibility = View.VISIBLE
        }

        dataBinding.buttonAdapterBorrowStepsTitle.text = "${position+1}.${borrowSteps.content}"

    }

    override fun attachLayout(): Int {
        return R.layout.adapter_borrow_steps
    }
}