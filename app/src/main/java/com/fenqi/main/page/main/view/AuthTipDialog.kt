package com.fenqi.main.page.main.view

import android.annotation.SuppressLint
import android.app.Activity
import android.text.Html
import android.text.Html.TO_HTML_PARAGRAPH_LINES_CONSECUTIVE
import com.fenqi.main.R
import com.fenqi.main.constant.CommonData
import com.fenqi.main.databinding.DialogAuthBackTipBinding
import com.fenqi.main.databinding.DialogHomeAuthDataTipBinding
import com.fenqi.platformtools.customerui.dialog.BaseBottomDialog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

public class AuthTipDialog(activity: Activity) :
    BaseBottomDialog<DialogHomeAuthDataTipBinding>(activity) {

    override fun attachDialogLayout(): Int {
        return R.layout.dialog_home_auth_data_tip
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        databinding?.click = this

        setCancelAble(false)

        databinding?.tvDialogHomeAuthTipTitle?.text = "欢迎来到${activity.getString(R.string.app_name)}!\n现在即可申请额度"

        databinding?.tvHomeAuthTipSubmitButton?.setOnClickListener {
            confirmLogic.invoke()
            CommonData.IS_FIRST_OPEN = false
            dismissDialog()
            CoroutineScope(Dispatchers.Main).launch {
                delay(200)
                dismissDialog()
            }
        }
        databinding?.tvHomeAuthTipCantBorrowNow?.setOnClickListener {
            CommonData.IS_FIRST_OPEN = false
            dismissDialog()
            CoroutineScope(Dispatchers.Main).launch {
                delay(200)
                dismissDialog()
            }
        }
    }

    fun setData(maxAmount:String,buttonText:String){
        databinding?.tvMainHomeAuthTipMaxAmount?.text = maxAmount
        databinding?.tvHomeAuthTipSubmitButton?.text = buttonText
    }

    private lateinit var confirmLogic:()->Unit
    fun setConfirmLogic(confirmLogic:()->Unit){
        this.confirmLogic = confirmLogic
    }
}