package com.fenqi.main.page.main.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.fenqi.main.R
import com.fenqi.main.bean.BorrowStepVoItemBean
import com.fenqi.main.databinding.LayoutBorrowStepsBinding
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter

class BorrowStepsView: LinearLayout {

    private var adapterBorrowSteps:AdapterBorrowSteps?=null

    constructor(context: Context?) : super(context) {}
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {

        val inflater = context?.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val dataBinding:LayoutBorrowStepsBinding = DataBindingUtil.inflate(inflater,R.layout.layout_borrow_steps,this,true)

        adapterBorrowSteps = AdapterBorrowSteps(mutableListOf(),context,object:BaseRecyclerViewAdapter.BaseRecyclerViewCallBack{
            override fun onClickListener(position: Int) {

            }
        })
        dataBinding.recyclerviewBorrowSteps.apply {
            layoutManager = LinearLayoutManager(context)
            setHasFixedSize(true)
            adapter = adapterBorrowSteps
        }

    }

    fun setData(borrowStepVOList:MutableList<BorrowStepVoItemBean>){
        adapterBorrowSteps?.addNewList(borrowStepVOList)
    }
}