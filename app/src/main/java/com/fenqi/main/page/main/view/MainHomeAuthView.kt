package com.fenqi.main.page.main.view

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.view.LayoutInflater
import android.widget.RelativeLayout
import androidx.databinding.DataBindingUtil
import com.fenqi.main.R
import com.fenqi.main.bean.MainHomeCenterVoEntity
import com.fenqi.main.databinding.LayoutMainHomeAuthBinding
import com.fenqi.main.page.main.MainJumpUtil

@SuppressLint("ViewConstructor", "SetTextI18n")
class MainHomeAuthView(context: Context?, private val centerVoEntity: MainHomeCenterVoEntity) : RelativeLayout(context) {

    init {
        val inflater = context?.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val dataBinding:LayoutMainHomeAuthBinding = DataBindingUtil.inflate(inflater,R.layout.layout_main_home_auth,this,true)

        dataBinding.tvLayoutMainAuthTitleTip.text = centerVoEntity.bigTips
        dataBinding.tvLayoutMainAuthSubTip.text=centerVoEntity.tips

        dataBinding.tvMainHomeMaxAmount.text = centerVoEntity.maxAmount

        if(centerVoEntity.borrowStepVOList != null){
            centerVoEntity.borrowStepVOList?.let {
                dataBinding.borrowStepsView.setData(it)
            }
        }
    }

    fun clickSubmit(){
        MainJumpUtil.getInstance().jump(context as Activity,
            centerVoEntity.buttonJumpUrl.toString(),centerVoEntity)
    }

    private lateinit var countDownFinished:()->Unit
    fun setCountDownFinished(countDownFinished:()->Unit){
        this.countDownFinished = countDownFinished
    }
}