package com.fenqi.main.page.main.view

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.widget.RelativeLayout
import androidx.databinding.DataBindingUtil
import com.fenqi.main.R
import com.fenqi.main.bean.MainHomeCenterVoEntity
import com.fenqi.main.databinding.LayoutMainHomeLoanRefuseBinding

@SuppressLint("ViewConstructor", "SetTextI18n")
class MainHomeLoanRefuse(context: Context?, private val centerVoEntity: MainHomeCenterVoEntity) : RelativeLayout(context) {

    init {
        val inflater = context?.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val dataBinding:LayoutMainHomeLoanRefuseBinding = DataBindingUtil.inflate(inflater,R.layout.layout_main_home_loan_refuse,this,true)

        dataBinding.tvLayoutMainAuthTitleTip.text = centerVoEntity.bigTips
        dataBinding.tvLayoutMainAuthSubTip.text = centerVoEntity.tips
        dataBinding.tvMainHomeBottomTip.text = centerVoEntity.miniTips
    }
}