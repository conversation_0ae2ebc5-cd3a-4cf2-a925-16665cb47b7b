package com.fenqi.main.page.main.view

import android.annotation.SuppressLint
import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.widget.RelativeLayout
import androidx.databinding.DataBindingUtil
import com.fenqi.main.R
import com.fenqi.main.bean.MainHomeCenterVoEntity
import com.fenqi.main.databinding.LayoutHomeMainHomeBinding
import com.fenqi.main.page.login.LoginActivity
import com.fenqi.main.sharedata.SharePreferenceData

@SuppressLint("ViewConstructor", "SetTextI18n")
class MainHomeView(context: Context?, private val centerVoEntity: MainHomeCenterVoEntity) :
    RelativeLayout(context) {

    init {
        val inflater = context?.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater

        val dataBinding: LayoutHomeMainHomeBinding =
            DataBindingUtil.inflate(inflater, R.layout.layout_home_main_home, this, true)

        dataBinding.click = this

        dataBinding.tvMainHomeMaxAmount.text = centerVoEntity.maxAmount

        dataBinding.buttonMainHomeApplyNow.visibility =
            if (TextUtils.isEmpty(centerVoEntity.buttonJumpUrl)) GONE else VISIBLE

        dataBinding.buttonMainHomeApplyNow.text = centerVoEntity.buttonTxt

        dataBinding.tvMainHomeBottomTip.text = centerVoEntity.miniTips
    }

    fun clickSubmit() {
        if (TextUtils.isEmpty(SharePreferenceData.getToken())) {
            context?.startActivity(LoginActivity.callIntent(context))
        } else {
            authJump.invoke()
        }
    }

    private lateinit var authJump:()->Unit
    fun setAuthJump(authJump:()->Unit){
        this.authJump = authJump
    }
}