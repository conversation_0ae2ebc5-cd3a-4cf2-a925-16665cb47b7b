package com.fenqi.main.page.main.view

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.widget.RelativeLayout
import androidx.databinding.DataBindingUtil
import com.fenqi.main.R
import com.fenqi.main.bean.MainHomeCenterVoEntity
import com.fenqi.main.databinding.LayoutMainHomeWaitForPayBinding
import com.fenqi.main.page.authinfo.bindbank.bindnew.BindBankNewActivity

@SuppressLint("ViewConstructor", "SetTextI18n")
class MainHomeWaitForPayView(context: Context?, private val centerVoEntity: MainHomeCenterVoEntity) : RelativeLayout(context) {

    init {
        val inflater = context?.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val dataBinding:LayoutMainHomeWaitForPayBinding = DataBindingUtil.inflate(inflater,R.layout.layout_main_home_wait_for_pay,this,true)
        dataBinding.click = this

        dataBinding.tvLayoutMainAuthTitleTip.text = centerVoEntity.bigTips
        dataBinding.tvLayoutMainAuthSubTip.text= centerVoEntity.tips
        dataBinding.tvMainHomeBottomTip.text = centerVoEntity.miniTips
    }

    fun clickSubmit(){
        context.startActivity(BindBankNewActivity.callIntent(context,BindBankNewActivity.BIND_BANK_TYPE_BORROW_FAILED_RE_BIND))
    }
}