package com.fenqi.main.page.mine

import android.content.Context
import android.text.TextUtils
import androidx.databinding.ObservableField
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.bean.BankSupportListBean
import com.fenqi.main.bean.BorrowAgainBean
import com.fenqi.main.bean.CustomerServiceGetEntity
import com.fenqi.main.bean.UserPersonalResponseEntity
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.util.AuthJumpUtil
import com.fenqi.main.view.dialog.CommonMessageDialog

class MineActivityViewModel : BaseViewModel<MainApiService, MineActivityMvvmView>() {

    var userPersonalResponseEntity: ObservableField<UserPersonalResponseEntity> =
        ObservableField<UserPersonalResponseEntity>()
    var serviceUrl: ObservableField<String> = ObservableField<String>().apply { set("") }

    var maxAmount: ObservableField<String> = ObservableField<String>().apply { set("") }
    var buttonText: ObservableField<String> = ObservableField<String>().apply { set("") }


    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }

    fun userCustomerServiceGet(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: CustomerServiceGetEntity = json.decodeFromString(it)
                    serviceUrl.set(data.customerServiceUrl)
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg, code)
            }
        }) {
            mService?.userCustomerServiceGet()
        }
    }

    fun configAppCodeGet() {

    }

    fun userPersonalData() {
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: UserPersonalResponseEntity = json.decodeFromString(it)
                    userPersonalResponseEntity.set(data)
                    maxAmount.set(data.maxAmount)
                    buttonText.set(data.buttonText)

                    mvvmView?.userPersonalDataSuccess(data)
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg, code)
            }
        }) {
            mService?.userPersonalCenter()
        }
    }

    fun userSignOut() {
        RetrofitBuilder.getInstance().start({
            onSuccess {
                mvvmView?.userSignOutSuccess()
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg, code)
            }
        }) {
            mService?.userSignOut()
        }
    }

    fun orderBorrowConfirm(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: BorrowAgainBean = json.decodeFromString(it)
                    if(!TextUtils.isEmpty(data.jumpUrl)){
                        AuthJumpUtil.getInstance().setFinishedCallBack {
                            mvvmView?.dismissModelLoading()
                        }.setOnFailed {
                            mvvmView?.dismissModelLoading()
                        }.judgeJump(activity as Context)
                    } else {
                        mvvmView?.dismissModelLoading()
                        mvvmView?.applyNow()
                    }
                }
            }
            onFailure { msg, code ->
                activity?.let { context->
                    val commonMessageDialog = CommonMessageDialog(context)
                    commonMessageDialog.setMessage(msg)
                    commonMessageDialog.setSingle()
                    commonMessageDialog.showDialog()
                }
                mvvmView?.dismissModelLoading()
            }
        }) {
            mService?.orderBorrowConfirm()
        }
    }

}