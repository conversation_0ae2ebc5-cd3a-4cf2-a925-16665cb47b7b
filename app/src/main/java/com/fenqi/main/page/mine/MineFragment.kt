package com.fenqi.main.page.mine

import android.Manifest
import android.annotation.SuppressLint
import android.net.Uri
import android.text.TextUtils
import android.view.View
import com.fenqi.main.R
import com.fenqi.main.base.BaseFragment
import com.fenqi.main.bean.SlideshowVOBean
import com.fenqi.main.bean.UserPersonalResponseEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.constant.CommonData
import com.fenqi.main.databinding.ActivityMineBinding
import com.fenqi.main.event.MainEvent
import com.fenqi.main.page.authinfo.bindbank.bindnew.BindBankNewActivity
import com.fenqi.main.page.authinfo.bindbank.cardlist.BankCardListActivity
import com.fenqi.main.page.login.LoginActivity
import com.fenqi.main.page.paidlist.PaidListActivity
import com.fenqi.main.page.setting.SettingActivity
import com.fenqi.main.page.web.CommonWebActivity
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.sharedata.SharePreferenceData
import com.fenqi.main.util.AppInfoUploadUtil
import com.fenqi.main.util.AuthJumpUtil
import com.fenqi.main.util.LogUtil
import com.fenqi.main.util.TakePhotoUtil
import com.fenqi.main.util.event.EventConstants
import com.fenqi.main.view.banner.ImageAdapter
import com.fenqi.platformtools.utils.ActivityManager
// import com.tbruyelle.rxpermissions3.RxPermissions
import com.youth.banner.indicator.CircleIndicator

class MineFragment:
    BaseFragment<MainApiService, MineActivityMvvmView, MineActivityViewModel, ActivityMineBinding>(),MineActivityMvvmView {

    private var adapterBanner:ImageAdapter?=null

    override fun onShowViewById(): Int {
        return R.layout.activity_mine
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if(!hidden){
            getServiceData()
        }
    }

    override fun kfragmentInit() {
        dataBinding?.viewModel = viewModel
        dataBinding?.click = this

        if(adapterBanner == null){
            adapterBanner = ImageAdapter(mutableListOf(),requireActivity())
            dataBinding?.bannerMine?.setAdapter(adapterBanner)
                //添加生命周期观察者
                ?.addBannerLifecycleObserver(this) //设置指示器
                ?.setIndicator(CircleIndicator(requireActivity()))
                ?.setOnBannerListener { data: Any, position: Int ->
                    val slideshowVOBean = data as SlideshowVOBean
                    if(!TextUtils.isEmpty(slideshowVOBean.jumpUrl)){
                        startActivity(CommonWebActivity.callIntent(requireActivity(),
                            slideshowVOBean.title.toString(), slideshowVOBean.jumpUrl.toString()
                        ))
                    }
                }
        }

        getServiceData()

        try {
            AppInfoUploadUtil.getInstance().uploadContactList(CommonConstant.UPLOAD_DATA_AUTH,{

            },{

            })
        } catch (exception:Exception){
            exception.printStackTrace()
        }
    }


    fun getServiceData() {
        viewModel?.userPersonalData()
        viewModel?.userCustomerServiceGet()
    }

    fun rebindApiService(){
        viewModel?.bindService()
    }

    fun clickAboutUs(){
        if(!TextUtils.isEmpty(viewModel?.userPersonalResponseEntity?.get()?.aboutUrl)){
            startActivity(CommonWebActivity.callIntent(requireActivity(),getString(R.string.about_us),
                viewModel?.userPersonalResponseEntity?.get()?.aboutUrl.toString()
            ))
        }
    }

    fun clickLoanRecord(){
        eventBus.post(MainEvent(event = EventConstants.EVENT_GO_REPAYMENT))
    }

    fun clickApplyNow(){
        showLoading()
        viewModel?.orderBorrowConfirm()
    }


    fun clickMyBankCard(){
        CommonData.BIND_TYPE = BindBankNewActivity.BIND_BANK_TYPE_RE_BIND
        if(CommonData.HOME_STATUS == CommonConstant.STATUS_HOME_ROUTE_USER_DATA){
            AuthJumpUtil.getInstance().setFinishedCallBack {
                dismissLoading()
            }.setOnFailed {
                dismissLoading()
            }.judgeJump(requireActivity())
        } else {
            startActivity(BankCardListActivity.callIntent(requireActivity(),CommonData.BIND_TYPE))
        }
    }

    fun clickSetting(){
        startActivity(SettingActivity.callIntent(activity))
    }

    fun clickPaidList(){
        startActivity(PaidListActivity.callIntent(requireActivity()))
    }


    fun clickCustomerService(){
        if(!TextUtils.isEmpty(viewModel?.serviceUrl?.get())){
            startActivity(CommonWebActivity.callIntent(requireActivity(),getString(R.string.customer_service),viewModel?.serviceUrl?.get().toString()))
        }
    }

    override fun attachViewModel(): Class<MineActivityViewModel> {
        return MineActivityViewModel::class.java
    }

    override fun attachMvvmView(): MineActivityMvvmView {
        return this
    }

    override fun userSignOutSuccess() {
        SharePreferenceData.setToken("")
        SharePreferenceData.setIsRc(false)
        startActivity(LoginActivity.callIntent(requireActivity()))
        ActivityManager.getActivityManager().destroyAllActivity()
    }

    override fun userPersonalDataSuccess(data: UserPersonalResponseEntity) {
        if(data.slideshowVOList == null){
            dataBinding?.viewMineBannerContainer?.visibility = View.GONE
        } else {
            if(data.slideshowVOList!!.size>0){
                dataBinding?.viewMineBannerContainer?.visibility = View.VISIBLE

                adapterBanner?.updateData(data.slideshowVOList)
            } else {
                dataBinding?.viewMineBannerContainer?.visibility = View.GONE
            }
        }
    }

    override fun applyNow() {
        eventBus.post(MainEvent(event = EventConstants.EVENT_GO_HOME))
    }

    override fun requestError(message: String, code: Int) {
        showToast(message)
        dismissLoading()
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {

    }

}