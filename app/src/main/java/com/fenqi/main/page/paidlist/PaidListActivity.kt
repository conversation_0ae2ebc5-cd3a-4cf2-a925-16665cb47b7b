package com.fenqi.main.page.paidlist

import android.app.Activity
import android.content.Intent
import androidx.recyclerview.widget.LinearLayoutManager
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.databinding.ActivityPaidListBinding
import com.fenqi.main.page.repayment.RepaymentListAdapter
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter

class PaidListActivity:
    BaseActivity<MainApiService, PaidListMvvmView, PaidListViewModel, ActivityPaidListBinding>(),
    PaidListMvvmView {

    companion object {
        fun callIntent(activity: Activity?): Intent {
            return Intent(activity, PaidListActivity::class.java)
        }
    }

    private lateinit var repaymentListAdapter: RepaymentListAdapter

    override fun onShowViewById(): Int {
        return R.layout.activity_paid_list
    }

    override fun kinit() {
        dataBinding?.viewModel = viewModel
        dataBinding?.click = this

        dataBinding?.refreshviewRepayment?.apply {
            setOnRefreshListener {
                viewModel?.orderBillPaidList()
            }
        }

        repaymentListAdapter = RepaymentListAdapter(mutableListOf(),activity, object :
            BaseRecyclerViewAdapter.BaseRecyclerViewCallBack {
            override fun onClickListener(position: Int) {

            }
        })

        dataBinding?.recyclerviewRepayment?.apply {
            layoutManager = LinearLayoutManager(activity)
            setHasFixedSize(true)
            adapter = repaymentListAdapter
        }

        viewModel?.orderBillPaidList()
    }

    override fun attachViewModel(): Class<PaidListViewModel> {
        return PaidListViewModel::class.java
    }

    override fun attachMvvmView(): PaidListMvvmView {
        return this
    }

    override fun attachTitleText(): String? {
        return getString(R.string.title_paid_list)
    }

    override fun orderBillPaidListSuccess() {
        dismissLoading()
        setNoData()

        viewModel?.paidBillList?.let {
            if(it.size>0){
                repaymentListAdapter.addNewList(it)
            }
        }

        dataBinding?.refreshviewRepayment?.finishRefresh()
    }

    private fun setNoData() {
        viewModel?.showNoDataPaid?.set(true)
        viewModel?.paidBillList?.let {
            if(it.size>0){
                viewModel?.showNoDataPaid?.set(false)
            }
        }
    }

    override fun requestError(message: String, code: Int) {
        showToast(message)
        dismissLoading()
        dataBinding?.refreshviewRepayment?.finishRefresh()
    }

    override fun showModelToast(message: String) {
       showToast(message)
    }

    override fun dismissModelLoading() {
       dismissLoading()
    }

    override fun showModelLoading() {

    }
}