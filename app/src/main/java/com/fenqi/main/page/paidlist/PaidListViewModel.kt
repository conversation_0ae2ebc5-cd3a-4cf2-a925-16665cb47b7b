package com.fenqi.main.page.paidlist

import androidx.databinding.ObservableField
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.bean.OrderBillListItemBean
import com.fenqi.main.bean.OrderBillPaidListResponseBean
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService

class PaidListViewModel: BaseViewModel<MainApiService, PaidListMvvmView>() {

    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }

    var paidBillList:MutableList<OrderBillListItemBean>? = mutableListOf()

    var showNoDataPaid: ObservableField<Boolean> = ObservableField<Boolean>().apply { set(false) }

    fun orderBillPaidList(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: OrderBillPaidListResponseBean = json.decodeFromString(it)
                    data.paidBillList.let {dataList->
                        paidBillList = dataList
                    }
                    mvvmView?.orderBillPaidListSuccess()
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.orderBillPaidList()
        }
    }

}