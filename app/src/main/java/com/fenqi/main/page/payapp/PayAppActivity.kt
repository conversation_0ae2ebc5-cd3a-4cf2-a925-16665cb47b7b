package com.fenqi.main.page.payapp

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.core.content.ContextCompat
import androidx.databinding.Observable
import androidx.databinding.Observable.OnPropertyChangedCallback
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.bean.BankCardListItemBean
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.databinding.ActivityPayAppBinding
import com.fenqi.main.page.main.MainActivity
import com.fenqi.main.page.verifycode.VerifyCodeActivity
import com.fenqi.main.page.webprivacy.WebPrivacyActivity
import com.fenqi.main.postentity.PayAppDoPayPostBean
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.util.SystemUtil
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.platformtools.utils.TitleBar
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.regex.Pattern

public class PayAppActivity :
    BaseActivity<MainApiService, PayAppMvvmView, PayAppViewModel, ActivityPayAppBinding>(),
    PayAppMvvmView {

    companion object {
        private const val INTENT_TYPE_PAYKEY = "payKey"
        private const val INTENT_TYPE_PAY_TYPE = "payType"
        fun callIntent(context: Context,payKey:String,payType:Int): Intent {
            val intent = Intent(context, PayAppActivity::class.java)
            intent.putExtra(INTENT_TYPE_PAYKEY,payKey)
            intent.putExtra(INTENT_TYPE_PAY_TYPE,payType)
            return intent
        }
    }

    private var adapterPayApp:PayAppBankListAdapter? = null

    override fun onShowViewById(): Int {
        return R.layout.activity_pay_app
    }

    @SuppressLint("SetTextI18n")
    override fun kinit() {
        TitleBar.changeTransparencyBar(activity)
        dataBinding?.click = this
        dataBinding?.viewModel = viewModel

        val payKey = intent.getStringExtra(INTENT_TYPE_PAYKEY)
        val payType = intent.getIntExtra(INTENT_TYPE_PAY_TYPE,CommonConstant.BIND_CARD_TYPE_PRINCIPAL_INTEREST)
        if(!TextUtils.isEmpty(payKey)){
            viewModel?.payKey?.set(payKey)
        }
        viewModel?.payType = payType

        viewModel?.paySteps?.addOnPropertyChangedCallback(object : OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                val type = viewModel?.paySteps?.get()
                when(type){
                    CommonConstant.PAY_STEPS_TYPE_INIT->{
                        dataBinding?.viewPayContainer1?.visibility = View.GONE
                        dataBinding?.viewPayContainer2?.visibility = View.GONE
                        dataBinding?.viewPayContainer3?.visibility = View.GONE
                    }
                    CommonConstant.PAY_STEPS_TYPE_START->{
                        dataBinding?.viewPayContainer1?.visibility = View.VISIBLE
                        dataBinding?.viewPayContainer2?.visibility = View.GONE
                        dataBinding?.viewPayContainer3?.visibility = View.GONE
                    }
                    CommonConstant.PAY_STEPS_TYPE_ING->{
                        dataBinding?.viewPayContainer1?.visibility = View.GONE
                        dataBinding?.viewPayContainer2?.visibility = View.VISIBLE
                        dataBinding?.viewPayContainer3?.visibility = View.GONE
                    }
                    CommonConstant.PAY_STEPS_TYPE_FINISHED->{
                        dataBinding?.viewPayContainer1?.visibility = View.GONE
                        dataBinding?.viewPayContainer2?.visibility = View.GONE
                        dataBinding?.viewPayContainer3?.visibility = View.VISIBLE
                    }
                    else -> {}
                }
            }
        })

        viewModel?.payStatus?.addOnPropertyChangedCallback(object : OnPropertyChangedCallback(){
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                if(viewModel?.payStatus?.get() == CommonConstant.PAY_FINISHED_SUCCESS){
                    dataBinding?.imgPayFinishedStatus?.setBackgroundResource(R.drawable.success)
                    dataBinding?.textPayFinishedStatus?.setTextColor(ContextCompat.getColor(activity,R.color.color_success))
                }
                if(viewModel?.payStatus?.get() == CommonConstant.PAY_FINISHED_FAILED){
                    dataBinding?.imgPayFinishedStatus?.setBackgroundResource(R.drawable.failed)
                    dataBinding?.textPayFinishedStatus?.setTextColor(ContextCompat.getColor(activity,R.color.color_error))
                }
            }

        })

        initView()

    }

    override fun onResume() {
        super.onResume()
        showLoading()
        viewModel?.cashierInfo()
    }

    private fun initView() {
        dataBinding?.viewPayAppBar?.layoutParams?.height = SystemUtil.getInstance().getStatusBarHeight()

        adapterPayApp = PayAppBankListAdapter(mutableListOf(),activity,object :BaseRecyclerViewAdapter.BaseRecyclerViewCallBack{
            override fun onClickListener(position: Int) {
                viewModel?.bankList?.let {
                    viewModel?.bindId = it[position].bindId.toString()
                }
            }
        })

        dataBinding?.recyclerviewPayApp?.apply {
            layoutManager = LinearLayoutManager(activity)
            setHasFixedSize(true)
            adapter = adapterPayApp
        }

        dataBinding?.imgPayIng?.let {
            Glide.with(activity).load(R.drawable.pay_loading).into(it)
        }
    }

    fun clickGoBack(){
        finish()
    }

    fun pay() {
        showLoading()
        viewModel?.cashierDoPay(PayAppDoPayPostBean(bindId = viewModel?.bindId))
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {
        showLoading()
    }

    override fun attachViewModel(): Class<PayAppViewModel> {
        return PayAppViewModel::class.java
    }

    override fun attachTitleText(): String? {
        return null
    }

    override fun cashierInfoSuccess(bankList: MutableList<BankCardListItemBean>?) {
        bankList?.let {
            if(it.size>0){
                it[0].check = true
                viewModel?.bindId = it[0].bindId.toString()
                adapterPayApp?.addNewList(it)
            }
        }
    }

    override fun cashierDoPaySuccess() {
        viewModel?.pollTaskUsingCoroutines()
    }

    override fun requestError(message: String, code: Int) {
        dismissLoading()
        showToast(message)
    }

    override fun attachMvvmView(): PayAppMvvmView {
        return this
    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel?.stopCondition = false
    }

}