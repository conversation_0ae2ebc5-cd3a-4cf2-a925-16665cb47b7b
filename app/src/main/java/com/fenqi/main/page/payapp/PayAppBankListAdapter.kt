package com.fenqi.main.page.payapp

import android.annotation.SuppressLint
import android.content.Context
import com.bumptech.glide.Glide
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.bean.BankCardListItemBean
import com.fenqi.main.databinding.AdapterPayBankListBinding

class PayAppBankListAdapter(
    datas: MutableList<BankCardListItemBean>,
    context: Context,
    baseRecyclerViewCallBack: BaseRecyclerViewCallBack?
) : BaseRecyclerViewAdapter<BankCardListItemBean, AdapterPayBankListBinding>(
    datas,
    context,
    baseRecyclerViewCallBack
) {
    @SuppressLint("NotifyDataSetChanged")
    override fun onBaseBindViewHolder(
        holder: BaseRecyclerViewHolder<AdapterPayBankListBinding>,
        position: Int
    ) {
        val bankVo = datas[position]
        val databinding = holder.itemDataBinding

        Glide.with(context).load(bankVo.bankLogo).into(databinding.imageAdapterBankLogo)
        databinding.txtAdapterBankName.text = bankVo.bankName

        if(bankVo.check){
            databinding.imageAdapterCheck.setBackgroundResource(R.drawable.check_yes2)
        } else {
            databinding.imageAdapterCheck.setBackgroundResource(R.drawable.check_not_grey2)
        }

        databinding.txtAdapterBankNo.text = bankVo.cardNo

        databinding.viewAdapterPayAppBankList.setOnClickListener {
            for(item in datas){
                item.check = false
            }
            datas[position].check = true
            notifyDataSetChanged()
            baseRecyclerViewCallBack?.onClickListener(position)
        }
    }

    override fun attachLayout(): Int {
        return R.layout.adapter_pay_bank_list
    }
}