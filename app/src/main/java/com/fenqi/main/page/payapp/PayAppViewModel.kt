package com.fenqi.main.page.payapp

import android.annotation.SuppressLint
import android.text.TextUtils
import androidx.databinding.ObservableField
import com.fenqi.main.R
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.bean.BankCardListItemBean
import com.fenqi.main.bean.CashierPayInfoResponseBean
import com.fenqi.main.bean.DoPayResponseBean
import com.fenqi.main.bean.PayStatusResponseBean
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.page.authinfo.bindbank.bindnew.BindBankNewActivity
import com.fenqi.main.page.authinfo.bindbank.bindnew.BindBankNewActivity.Companion.BIND_BANK_TYPE_RE_BIND
import com.fenqi.main.postentity.PayAppDoPayPostBean
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.view.dialog.CommonMessageDialog
import com.fenqi.request.RequestCode
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.math.BigDecimal

class PayAppViewModel : BaseViewModel<MainApiService, PayAppMvvmView>() {

    var payKey: ObservableField<String> = ObservableField<String>().apply { set("") }
    var amount: ObservableField<String> = ObservableField<String>().apply { set("") }

    var paySteps: ObservableField<Int> = ObservableField<Int>().apply { set(0) }

    var payType: Int = CommonConstant.BIND_CARD_TYPE_PRINCIPAL_INTEREST

    var stopCondition: Boolean = false
    private var cashierInfoData: CashierPayInfoResponseBean = CashierPayInfoResponseBean()
    var bindId = ""

    var bankList: MutableList<BankCardListItemBean>? = mutableListOf()

    var payStatus: ObservableField<Int> = ObservableField<Int>().apply { set(0) }
    var payFinishedContent: ObservableField<String> = ObservableField<String>().apply { set("") }

    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }

    @SuppressLint("DefaultLocale")
    fun cashierInfo() {
        RetrofitBuilder.getInstance().start({
            onSuccess { it ->
                it?.let { it ->
                    val data: CashierPayInfoResponseBean = json.decodeFromString(it)
                    if (data.errorCode == RequestCode.RE_BIND_CARD) {
                        activity?.let {it.startActivity(BindBankNewActivity.callIntent(it, BIND_BANK_TYPE_RE_BIND))}
                        mvvmView?.dismissModelLoading()
                    } else {
                        bankList = data.bankRoList
                        cashierInfoData = data
                        amount.set(BigDecimal.valueOf(data.amount).divide(BigDecimal.valueOf(100)).setScale(2).toString())
                        if (data.bankRoList == null) {
                            activity?.let {
                                it.startActivity(
                                    BindBankNewActivity.callIntent(
                                        it,
                                        BIND_BANK_TYPE_RE_BIND,
                                        payType
                                    )
                                )
                                it.finish()
                            }
                        } else {
                            data.bankRoList?.let { it ->
                                if (it.size == 0) {
                                    activity?.let {
                                        it.startActivity(
                                            BindBankNewActivity.callIntent(
                                                it,
                                                BIND_BANK_TYPE_RE_BIND,
                                                payType
                                            )
                                        )
                                        it.finish()
                                    }
                                } else {
                                    mvvmView?.cashierInfoSuccess(data.bankRoList)
                                    paySteps.set(CommonConstant.PAY_STEPS_TYPE_START)
                                }
                            }
                        }
                    }
                }
                mvvmView?.dismissModelLoading()
            }
            onFailure { msg, code ->

                    mvvmView?.requestError(msg, code)

            }
        }) {
            mService?.cashierInfo(payKey.get().toString())
        }
    }

    fun cashierDoPay(payAppDoPayPostBean: PayAppDoPayPostBean) {
        payAppDoPayPostBean.orderType = cashierInfoData.orderType
        RetrofitBuilder.getInstance().start({
            onSuccess { it ->
                it?.let { it ->
                    val data: DoPayResponseBean = json.decodeFromString(it)
                    if (data.errorCode == RequestCode.RE_BIND_CARD) {
                        activity?.let {it.startActivity(BindBankNewActivity.callIntent(it, BIND_BANK_TYPE_RE_BIND))}
                        mvvmView?.dismissModelLoading()
                    } else {
                        if(!TextUtils.isEmpty(data.errorMsg)){
                            activity?.let { context->
                                val commonMessageDialog = CommonMessageDialog(context)
                                commonMessageDialog.setMessage(data.errorMsg.toString())
                                commonMessageDialog.setSingle()
                                commonMessageDialog.showDialog()
                            }
                        } else {
                            mvvmView?.cashierDoPaySuccess()
                        }
                    }
                }
                mvvmView?.dismissModelLoading()
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg, code)
            }
        }) {
            mService?.cashierDoPay(
                payKey.get().toString(),
                RetrofitBuilder.getInstance().getRequestBody(payAppDoPayPostBean)
            )
        }
    }

    private fun cashierStatus() {
        stopCondition = false
        RetrofitBuilder.getInstance().start({
            onSuccess { it ->
                it?.let { it ->
                    val data: PayStatusResponseBean = json.decodeFromString(it)
                    when (val status = data.payStatus) {
                        CommonConstant.PAY_FINISHED_INIT -> {
                            stopCondition = true
                        }

                        CommonConstant.PAY_FINISHED_ING -> {
                            stopCondition = true
                        }

                        CommonConstant.PAY_FINISHED_SUCCESS -> {
                            paySteps.set(CommonConstant.PAY_STEPS_TYPE_FINISHED)
                            stopCondition = false
                            activity?.let {
                                payFinishedContent.set(it.getString(R.string.pay_success))
                            }
                            payStatus.set(status)

                            startBack()
                        }

                        CommonConstant.PAY_FINISHED_FAILED -> {
                            paySteps.set(CommonConstant.PAY_STEPS_TYPE_FINISHED)
                            stopCondition = false
                            activity?.let {
                                payFinishedContent.set(data.errorMsg.toString())
                            }
                            payStatus.set(status)
                        }

                        else -> {
                            stopCondition=true
                        }
                    }
                }
                if(it == null){
                    stopCondition = true
                }
                mvvmView?.dismissModelLoading()
            }
            onFailure { msg, code ->
                stopCondition = true
                mvvmView?.requestError(msg, code)
            }
        }) {
            mService?.cashierStatus(payKey.get().toString())
        }
    }

    fun startBack(){
        CoroutineScope(Dispatchers.Main).launch {
            delay(3000)
           activity?.let {
               it.finish()
           }
        }
    }

    fun pollTaskUsingCoroutines() {
        stopCondition = true
        paySteps.set(CommonConstant.PAY_STEPS_TYPE_ING)
        CoroutineScope(Dispatchers.IO).launch {
            while (stopCondition) {
                cashierStatus()
                delay(2000)
            }
        }
    }
}