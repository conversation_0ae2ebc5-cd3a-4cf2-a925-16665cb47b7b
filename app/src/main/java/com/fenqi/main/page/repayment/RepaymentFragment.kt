package com.fenqi.main.page.repayment

import android.app.Activity
import androidx.recyclerview.widget.LinearLayoutManager
import com.fenqi.main.R
import com.fenqi.main.base.BaseFragment
import com.fenqi.main.bean.MainHomeCenterVoEntity
import com.fenqi.main.postentity.AppearSexualProsecutorEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.constant.CommonData
import com.fenqi.main.databinding.ActivityRepaymentBinding
import com.fenqi.main.event.MainEvent
import com.fenqi.main.page.authinfo.bindbank.bindnew.BindBankNewActivity
import com.fenqi.main.page.main.MainActivity.Companion.FRAGMENT_HOME
import com.fenqi.main.page.main.MainActivity.Companion.FRAGMENT_REPAYMENT
import com.fenqi.main.page.main.MainJumpUtil
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.util.AuthJumpUtil
import com.fenqi.main.util.event.EventConstants
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.platformtools.utils.TitleBar
import org.greenrobot.eventbus.Subscribe


class RepaymentFragment :
    BaseFragment<MainApiService, RepaymentMvvmView, RepaymentViewModel, ActivityRepaymentBinding>(),
    RepaymentMvvmView {

    private lateinit var repaymentListAdapter: RepaymentListAdapter

    private var currentPayData:AppearSexualProsecutorEntity = AppearSexualProsecutorEntity()
    private var currentPayType:Int = CommonConstant.BIND_CARD_TYPE_PRINCIPAL_INTEREST

    override fun onShowViewById(): Int {
        return R.layout.activity_repayment
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (!hidden) {
            getServiceData()
        }
    }

    override fun kfragmentInit() {
        dataBinding?.viewModel = viewModel
        dataBinding?.click = this

        initView()

        getServiceData()
    }

    private fun initView() {
        val statusBarHeight = TitleBar.getStatusBarHeight(requireActivity())
        dataBinding?.viewStatusBarHeight?.layoutParams?.height = statusBarHeight

        repaymentListAdapter = RepaymentListAdapter(mutableListOf(), requireActivity(), object :
            BaseRecyclerViewAdapter.BaseRecyclerViewCallBack {
            override fun onClickListener(position: Int) {

            }
        })
        repaymentListAdapter.setRepayAction { postion->
            viewModel?.repaymentBillList?.let {
                showLoading()
                val payData = AppearSexualProsecutorEntity(billNo = it[postion].billNo, payType = CommonConstant.PAY_TYPE_PRINCIPAL_INTEREST, requestType = CommonConstant.PAY_ACTION_TYPE_START)
                currentPayData = payData
                currentPayType = CommonConstant.BIND_CARD_TYPE_PRINCIPAL_INTEREST
                viewModel?.payClientCharges(payData,CommonConstant.BIND_CARD_TYPE_PRINCIPAL_INTEREST)
            }
        }
        repaymentListAdapter.setPledgeAction { postion->
            viewModel?.repaymentBillList?.let {
                showLoading()
                val payData = AppearSexualProsecutorEntity(billNo = it[postion].billNo, payType = CommonConstant.PAY_TYPE_GUARANTEE,requestType = CommonConstant.PAY_ACTION_TYPE_START)
                currentPayData = payData
                currentPayType = CommonConstant.BIND_CARD_TYPE_GUARANTEE
                viewModel?.payClientCharges(payData,CommonConstant.BIND_CARD_TYPE_GUARANTEE)
            }
        }

        dataBinding?.recyclerviewRepayment?.apply {
            layoutManager = LinearLayoutManager(activity)
            setHasFixedSize(true)
            adapter = repaymentListAdapter
        }

        dataBinding?.refreshviewRepayment?.apply {
            setOnRefreshListener {
                viewModel?.orderBillRepaymentList()
            }
        }
    }

    fun getServiceData() {
        viewModel?.orderBillRepaymentList()
    }

    fun rebindApiService() {
        viewModel?.bindService()
    }

    private fun setNoData() {
        if (viewModel?.repaymentBillList?.isNotEmpty() == true) {
            viewModel?.showNoData?.set(false)
        } else {
            viewModel?.showNoData?.set(true)
        }
    }

    override fun attachViewModel(): Class<RepaymentViewModel> {
        return RepaymentViewModel::class.java
    }

    override fun attachMvvmView(): RepaymentMvvmView {
        return this
    }

    override fun orderBorrowConfirmSuccess() {
        val centerVoData:MainHomeCenterVoEntity? = CommonData.centerVo
        centerVoData?.let { centerVo->
            if(centerVo.buttonJumpUrl == CommonConstant.STATUS_HOME_ROUTE_USER_DATA){
                CommonData.BIND_TYPE = BindBankNewActivity.BIND_BANK_TYPE_FIRST_ORDER_SUBMIT
                AuthJumpUtil.getInstance().setFinishedCallBack {
                    dismissLoading()
                }.setOnFailed {
                    dismissLoading()
                }.judgeJump(requireActivity())
            } else {
                MainJumpUtil.getInstance().jump(context as Activity,
                    centerVo.buttonJumpUrl.toString(),centerVo)
            }
        }

    }

    override fun orderBillRepaymentListSuccess() {
        dismissLoading()
        setNoData()
        if (viewModel?.repaymentBillList?.isNotEmpty() == true) {
            viewModel?.repaymentBillList?.let {
                repaymentListAdapter.addNewList(it)
            }
        }
        dataBinding?.refreshviewRepayment?.finishRefresh()
    }

    override fun requestError(message: String, code: Int) {
        showToast(message)
        dismissLoading()
        dataBinding?.refreshviewRepayment?.finishRefresh()
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {

    }

    @Subscribe
    public fun onEventPost(event: MainEvent){
        if(event.event == EventConstants.EVENT_RE_PAY){
            val payList = viewModel?.repaymentBillList
            payList?.let {
                if(it.size>0){
                    showLoading()
                    viewModel?.payClientCharges(currentPayData,currentPayType)
                }
            }
        }

    }


}