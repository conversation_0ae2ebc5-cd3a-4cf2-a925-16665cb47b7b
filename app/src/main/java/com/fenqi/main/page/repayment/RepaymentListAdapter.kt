package com.fenqi.main.page.repayment

import android.annotation.SuppressLint
import android.content.Context
import androidx.core.content.ContextCompat
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.bean.OrderBillListItemBean
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.databinding.LayoutItemRepaymentBinding

class RepaymentListAdapter(
    datas: MutableList<OrderBillListItemBean>,
    context: Context,
    baseRecyclerViewCallBack: BaseRecyclerViewCallBack?
) : BaseRecyclerViewAdapter<OrderBillListItemBean, LayoutItemRepaymentBinding>(
    datas,
    context,
    baseRecyclerViewCallBack
) {
    @SuppressLint("SetTextI18n")
    override fun onBaseBindViewHolder(
        holder: BaseRecyclerViewHolder<LayoutItemRepaymentBinding>,
        position: Int
    ) {
        val orderListBean = datas[position]
        val dataBinding = holder.itemDataBinding

        dataBinding.tvArWaitForPayAmountRepayment.text = "${context.getString(R.string.bill_wait_for_pay_tip)}  ${orderListBean.needPayCapital}${context.getString(R.string.yuan)}"
        dataBinding.tvArStartTime.text = orderListBean.repaymentDate
        dataBinding.tvArBillCycle.text = orderListBean.cycleDate
        dataBinding.tvArBillRepaymentAmount.text = orderListBean.repaymentCapital
        dataBinding.tvArOrderIssue.text = orderListBean.currentPeriodDesc

        dataBinding.tvArWaitForPayAmountRepaymentPledge.text = "${context.getString(R.string.bill_wait_for_pay_tip_pledge)}  ${orderListBean.pledgeNeedPayCapital}${context.getString(R.string.yuan)}"
        dataBinding.tvArStartTimePledge.text = orderListBean.pledgeRepaymentDate
        dataBinding.tvArBillCyclePledge.text = orderListBean.cycleDate
        dataBinding.tvArBillRepaymentAmountPledge.text = orderListBean.pledgeRepaymentCapital
        dataBinding.tvArOrderIssuePledge.text = orderListBean.currentPeriodDesc

        if(orderListBean.paidStatus == CommonConstant.ORDER_STATUS_PAY_PAID){
            dataBinding.tvArOrderStatus.setTextColor(ContextCompat.getColor(context, R.color.white))
            dataBinding.tvArOrderStatus.setBackgroundResource(R.drawable.shape_repayment_status_grey)
        } else {
            dataBinding.tvArOrderStatus.setTextColor(ContextCompat.getColor(context,R.color.white))

            if(orderListBean.overdueDays>0){
                dataBinding.tvArOrderStatus.setBackgroundResource(R.drawable.shape_repayment_status_light_red)
            } else {
                dataBinding.tvArOrderStatus.setBackgroundResource(R.drawable.shape_repayment_status_light_blue)
            }
        }
        dataBinding.tvArOrderStatus.text = orderListBean.paidOrderStatusDesc

        if(orderListBean.pledgePaidStatus == CommonConstant.ORDER_STATUS_PAY_PAID){
            dataBinding.tvArOrderStatusPledge.setTextColor(ContextCompat.getColor(context,R.color.white))
            dataBinding.tvArOrderStatusPledge.setBackgroundResource(R.drawable.shape_repayment_status_grey)
        } else {
            dataBinding.tvArOrderStatusPledge.setTextColor(ContextCompat.getColor(context,R.color.white))
            if(orderListBean.pledgeOverdueDays>0){
                dataBinding.tvArOrderStatusPledge.setBackgroundResource(R.drawable.shape_repayment_status_light_red)
            } else {
                dataBinding.tvArOrderStatusPledge.setBackgroundResource(R.drawable.shape_repayment_status_light_blue)
            }
        }
        dataBinding.tvArOrderStatusPledge.text = orderListBean.pledgeOrderStatusDesc

        dataBinding.tvArButtonRepaymentStatus.text = orderListBean.paidStatusDesc

        dataBinding.tvArButtonRepaymentStatus.text = orderListBean.paidStatusDesc
        dataBinding.tvArButtonRepaymentStatusPledge.text = orderListBean.pledgePaidStatusDesc

        if(orderListBean.paidStatus == CommonConstant.ORDER_STATUS_PAY_PAID){
            dataBinding.tvArButtonRepaymentStatus.setBackgroundResource(R.drawable.shape_grey_big_radius)
            dataBinding.tvArButtonRepaymentStatus.setTextColor(ContextCompat.getColor(context,R.color.white))
            dataBinding.tvArButtonRepaymentStatus.isEnabled = false
        } else {
            dataBinding.tvArButtonRepaymentStatus.setTextColor(ContextCompat.getColor(context,R.color.white))
            dataBinding.tvArButtonRepaymentStatus.isEnabled = true
            if(orderListBean.overdueDays>0){
                dataBinding.tvArButtonRepaymentStatus.setBackgroundResource(R.drawable.select_repayment_submit)
            } else {
                dataBinding.tvArButtonRepaymentStatus.setBackgroundResource(R.drawable.select_repayment_submit_blue)
            }
        }

        if(orderListBean.pledgePaidStatus == CommonConstant.ORDER_STATUS_PAY_PAID){
            dataBinding.tvArButtonRepaymentStatusPledge.setBackgroundResource(R.drawable.shape_grey_big_radius)
            dataBinding.tvArButtonRepaymentStatusPledge.setTextColor(ContextCompat.getColor(context,R.color.white))
            dataBinding.tvArButtonRepaymentStatusPledge.isEnabled = false
        } else {
            dataBinding.tvArButtonRepaymentStatusPledge.setTextColor(ContextCompat.getColor(context,R.color.white))
            dataBinding.tvArButtonRepaymentStatusPledge.isEnabled = true

            if(orderListBean.pledgeOverdueDays>0){
                dataBinding.tvArButtonRepaymentStatusPledge.setBackgroundResource(R.drawable.select_repayment_submit)
            } else {
                dataBinding.tvArButtonRepaymentStatusPledge.setBackgroundResource(R.drawable.select_repayment_submit_blue)
            }
        }

        if(orderListBean.overdueDays>0){
            dataBinding.tvAdapterRepaymentExpirationTitle.text = context.getString(R.string.overdue)
            dataBinding.tvAdapterRepaymentExpirationValue.text = "${orderListBean.overdueDays} 天"
            dataBinding.tvAdapterRepaymentExpirationValue.setTextColor(ContextCompat.getColor(context,R.color.color_warnning_new))
        } else {
            dataBinding.tvAdapterRepaymentExpirationTitle.text = context.getString(R.string.date_until_expiration)
            dataBinding.tvAdapterRepaymentExpirationValue.text = "${orderListBean.repaymentDays} 天"
            dataBinding.tvAdapterRepaymentExpirationValue.setTextColor(ContextCompat.getColor(context,R.color.color_999999))
        }

        if(orderListBean.pledgeOverdueDays>0){
            dataBinding.tvAdapterPledgeExpirationTitle.text = context.getString(R.string.overdue)
            dataBinding.tvAdapterPledgeExpirationValue.text = "${orderListBean.pledgeOverdueDays} 天"
            dataBinding.tvAdapterPledgeExpirationValue.setTextColor(ContextCompat.getColor(context,R.color.color_warnning_new))
        } else {
            dataBinding.tvAdapterPledgeExpirationTitle.text = context.getString(R.string.date_until_expiration)
            dataBinding.tvAdapterPledgeExpirationValue.text = "${orderListBean.pledgeRepaymentDays} 天"
            dataBinding.tvAdapterPledgeExpirationValue.setTextColor(ContextCompat.getColor(context,R.color.color_999999))
        }

        dataBinding.tvArButtonRepaymentStatus.setOnClickListener {
            repayAction.invoke(position)
        }
        dataBinding.tvArButtonRepaymentStatusPledge.setOnClickListener {
            pledgeAction.invoke(position)
        }

//        dataBinding.viewItemRepaymentContainer.setOnClickListener {
//            baseRecyclerViewCallBack?.onClickListener(position)
//        }
    }

    override fun attachLayout(): Int {
        return R.layout.layout_item_repayment
    }

    private lateinit var repayAction:(position:Int)->Unit
    fun setRepayAction(repayAction:(position:Int)->Unit){
        this.repayAction = repayAction
    }

    private lateinit var pledgeAction:(position:Int)->Unit
    fun setPledgeAction(pledgeAction:(position:Int)->Unit){
        this.pledgeAction = pledgeAction
    }

}