package com.fenqi.main.page.repayment

import android.text.TextUtils
import androidx.databinding.ObservableField
import com.fenqi.main.R
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.bean.OrderBillListItemBean
import com.fenqi.main.bean.OrderBillRepaymentListResponseBean
import com.fenqi.main.bean.PayClientChargesEntity
import com.fenqi.main.postentity.AppearSexualProsecutorEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.page.payapp.PayAppActivity
import com.fenqi.main.page.web.CommonWebActivity
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.view.dialog.CommonMessageDialog
import com.fenqi.request.RequestCode
import org.json.JSONObject

class RepaymentViewModel : BaseViewModel<MainApiService, RepaymentMvvmView>() {

    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }

    var repaymentBillList:MutableList<OrderBillListItemBean>? = mutableListOf()
    var showNoData: ObservableField<Boolean> = ObservableField<Boolean>().apply { set(false) }

    fun orderBillRepaymentList(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: OrderBillRepaymentListResponseBean = json.decodeFromString(it)
                    data.repaymentBillList.let {dataList->
                        repaymentBillList = dataList
                    }
                    mvvmView?.orderBillRepaymentListSuccess()
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.orderBillRepaymentList()
        }
    }

    fun orderBorrowConfirm(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                mvvmView?.orderBorrowConfirmSuccess()
            }
            onFailure { msg, code ->
                activity?.let { context->
                    val commonMessageDialog = CommonMessageDialog(context)
                    commonMessageDialog.setMessage(msg)
                    commonMessageDialog.setSingle()
                    commonMessageDialog.showDialog()
                }
                mvvmView?.dismissModelLoading()
            }
        }) {
            mService?.orderBorrowConfirm()
        }
    }


    fun payClientCharges(payClientChargesPostEntity: AppearSexualProsecutorEntity,payType:Int){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: PayClientChargesEntity = json.decodeFromString(it)
                    payClientSuccess(data,payType)
                }
            }
            onFailure { msg, code ->
                if(code == RequestCode.PAY_CONFIRM){
                    mvvmView?.dismissModelLoading()
                    activity?.let { context->
                        val commonMessageDialog = CommonMessageDialog(context)
                        commonMessageDialog.setMessage(msg.toString())
                        commonMessageDialog.setCommonMessageDialogCallBack(object :CommonMessageDialog.CommonMessageDialogCallBack{
                            override fun confirmClick() {
                                mvvmView?.showModelLoading()
                                payClientChargesPostEntity.requestType =
                                    CommonConstant.PAY_ACTION_TYPE_CONFIRM
                                payClientCharges(payClientChargesPostEntity,payType)
                            }
                        })
                        commonMessageDialog.showDialog()
                    }
                } else {
                    mvvmView?.requestError(msg,code)
                }
            }
        }) {
            mService?.payClientCharges(RetrofitBuilder.getInstance().getRequestBody(payClientChargesPostEntity))
        }
    }

    private fun payClientSuccess(payClientChargesEntity: PayClientChargesEntity?,payType:Int){
        if(payClientChargesEntity!=null){
            when(payClientChargesEntity.payClientType){
                CommonConstant.PAY_TYPE_URL->{
                    val jsonObject = JSONObject(payClientChargesEntity.repaymentJson.toString())
                    val payUrl = jsonObject.getString("payUrl")
                    if(!TextUtils.isEmpty(payUrl)){
                        activity?.startActivity(
                            CommonWebActivity.callIntent(
                                activity!!,
                                activity!!.getString(
                                    R.string.repayment),payUrl))
                    }
                }
                CommonConstant.PAY_TYPE_APP->{
                    val jsonObject = JSONObject(payClientChargesEntity.repaymentJson.toString())
                    val payKey = jsonObject.getString("payKey")
                    if(!TextUtils.isEmpty(payKey)){
                        activity?.let {
                            it.startActivity(PayAppActivity.callIntent( it,payKey,payType))
                        }
                    }
                }
            }
            mvvmView?.dismissModelLoading()
        } else {
            mvvmView?.dismissModelLoading()
        }
    }

}