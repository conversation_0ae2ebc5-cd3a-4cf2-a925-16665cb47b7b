package com.fenqi.main.page.selecter

import android.content.Context
import android.content.Intent
import android.text.TextUtils
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.bean.CardBinResponseBean
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.databinding.ActivitySelecterBinding
import com.fenqi.main.retrofit.api.MainApiService

class SelecterActivity:
    BaseActivity<MainApiService, SelecterMvvmView, SelecterViewModel, ActivitySelecterBinding>(),SelecterMvvmView {

    companion object {
        fun callIntent(context: Context,data:String): Intent {
            val intent = Intent(context, SelecterActivity::class.java)
            intent.putExtra(CommonConstant.INTENT_SELECTER_DATA,data)
            return intent
        }
    }

    private lateinit var selecterData:String
    private var selecterDataList = mutableListOf<CardBinResponseBean>()
    private lateinit var selecterAdapter: SelecterAdapter
    private var currentSelecterDataList = mutableListOf<CardBinResponseBean>()

    override fun onShowViewById(): Int {
        return R.layout.activity_selecter
    }

    override fun kinit() {
        dataBinding?.viewModel = viewModel
        dataBinding?.click = this

        selecterData = intent.getStringExtra(CommonConstant.INTENT_SELECTER_DATA).toString()

        if(!TextUtils.isEmpty(selecterData)){
            selecterDataList = Gson().fromJson(selecterData, object : TypeToken<List<CardBinResponseBean>>() {}.type)
            currentSelecterDataList = Gson().fromJson(selecterData, object : TypeToken<List<CardBinResponseBean>>() {}.type)

            if(selecterDataList.size>0){
                selecterAdapter = SelecterAdapter(mutableListOf(),activity,object :BaseRecyclerViewAdapter.BaseRecyclerViewCallBack{
                    override fun onClickListener(position: Int) {

                        val name = currentSelecterDataList[position].bankName
                        val type = currentSelecterDataList[position].bankCode
                        val logo = currentSelecterDataList[position].bankLogo

                        val intent = Intent()
                        intent.putExtra(CommonConstant.INTENT_SELECTER_NAME,name)
                        intent.putExtra(CommonConstant.INTENT_SELECTER_TYPE,type)
                        intent.putExtra(CommonConstant.INTENT_SELECTER_LOGO,logo)
                        setResult(RESULT_OK,intent)
                        finish()
                    }
                })
                dataBinding?.recyclerviewSelecter?.apply {
                    layoutManager = LinearLayoutManager(activity)
                    setHasFixedSize(true)
                    adapter = selecterAdapter
                }
                selecterAdapter.addNewList(selecterDataList)
            }
        }

        initView()
    }

    private fun initView() {

    }

    override fun attachViewModel(): Class<SelecterViewModel> {
        return SelecterViewModel::class.java
    }

    override fun attachMvvmView(): SelecterMvvmView {
        return this
    }

    override fun attachTitleText(): String {
        return getString(R.string.title_select_bank_name)
    }

    override fun requestError(message: String, code: Int) {
        showToast(message)
        dismissLoading()
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {
        showLoading()
    }
}