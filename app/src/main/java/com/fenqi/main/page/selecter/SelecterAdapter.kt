package com.fenqi.main.page.selecter

import android.content.Context
import com.bumptech.glide.Glide
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.bean.CardBinResponseBean
import com.fenqi.main.databinding.AdapterSelecterBinding

class SelecterAdapter(
    datas: MutableList<CardBinResponseBean>,
    context: Context,
    baseRecyclerViewCallBack: BaseRecyclerViewCallBack?
) : BaseRecyclerViewAdapter<CardBinResponseBean, AdapterSelecterBinding>(
    datas,
    context,
    baseRecyclerViewCallBack
) {
    override fun onBaseBindViewHolder(
        holder: BaseRecyclerViewHolder<AdapterSelecterBinding>,
        position: Int
    ) {
        val selectVoEntity = datas[position]
        val databinding = holder.itemDataBinding

        databinding.tvAdapterSelecter.text = selectVoEntity.bankName

        Glide.with(context).load(selectVoEntity.bankLogo).into(databinding.imageAdapterCardListLogo)


        databinding.viewAdapterCardListContainer.setOnClickListener {
            baseRecyclerViewCallBack?.onClickListener(position)
        }
    }

    override fun attachLayout(): Int {
        return R.layout.adapter_selecter
    }
}