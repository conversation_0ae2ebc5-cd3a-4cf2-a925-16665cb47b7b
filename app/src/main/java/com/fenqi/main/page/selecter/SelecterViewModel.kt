package com.fenqi.main.page.selecter

import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.bean.CardBinResponseBean
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService
import java.util.regex.Pattern

class SelecterViewModel: BaseViewModel<MainApiService, SelecterMvvmView>() {
    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }

    fun searchList(searchValue:String,selectVoEntityList: MutableList<CardBinResponseBean>):MutableList<CardBinResponseBean>{
        val resultList = mutableListOf<CardBinResponseBean>()
        val pattern = Pattern.compile(searchValue,Pattern.CASE_INSENSITIVE)
        for(item in selectVoEntityList){
            val matcher = pattern.matcher(item.bankName)
            if(matcher.find()){
                resultList.add(item)
            }
        }
        return resultList
    }
}