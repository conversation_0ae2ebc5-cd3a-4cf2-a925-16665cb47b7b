package com.fenqi.main.page.setting

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.text.TextUtils
import com.fenqi.main.BuildConfig
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.constant.CommonData
import com.fenqi.main.databinding.ActivitySettingBinding
import com.fenqi.main.page.login.LoginActivity
import com.fenqi.main.page.web.CommonWebActivity
import com.fenqi.main.page.webprivacy.WebPrivacyActivity
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.sharedata.SharePreferenceData
import com.fenqi.main.view.dialog.CommonMessageDialog
import com.fenqi.platformtools.utils.ActivityManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File

class SettingActivity :
    BaseActivity<MainApiService, SettingMvvmView, SettingViewModel, ActivitySettingBinding>(),
    SettingMvvmView {

    companion object {
        fun callIntent(activity: Activity?): Intent {
            return Intent(activity, SettingActivity::class.java)
        }
    }

    override fun onShowViewById(): Int {
        return R.layout.activity_setting
    }

    @SuppressLint("SetTextI18n")
    override fun kinit() {
        dataBinding?.click = this
        dataBinding?.viewModel = viewModel

        dataBinding?.buttonSettingAppVersion?.text = "App版本号 ${BuildConfig.VERSION_NAME}"
        dataBinding?.buttonAboutUsTitle?.text = "关于${getString(R.string.app_name)}"

        viewModel?.getPrivacyPolicy()
        viewModel?.getRegisterPrivacy()
        viewModel?.userPersonalData()

        getCacheSize()
    }

    private fun getCacheSize(){
        var cacheSize = 0L
        // 获取内部缓存目录
        val internalCacheDir = activity.cacheDir
        cacheSize += getDirSize(internalCacheDir)

        viewModel?.appCache?.set("${cacheSize/1000}kb")
    }

    private fun getDirSize(dir: File): Long {
        var size: Long = 0
        val files = dir.listFiles()

        if (files != null) {
            for (file in files) {
                size += if (file.isDirectory) {
                    getDirSize(file)
                } else {
                    file.length()
                }
            }
        }
        return size
    }

    fun clickClearCache(){
        // 清除内部缓存目录
        val internalCacheDir = activity.cacheDir
        deleteDir(internalCacheDir)

        showLoading()
        CoroutineScope(Dispatchers.Main).launch {
            delay(1000)
            getCacheSize()
            dismissLoading()
            showToast(getString(R.string.clear_cache_success))
        }
    }

    private fun deleteDir(dir: File): Boolean {
        if (dir.isDirectory) {
            val children = dir.listFiles()
            for (child in children ?: emptyArray()) {
                deleteDir(child)
            }
        }
        return dir.delete()
    }

    fun clickAboutUs(){
        if(!TextUtils.isEmpty(viewModel?.aboutUrl?.get())){
            startActivity(CommonWebActivity.callIntent(activity,getString(R.string.about_us),viewModel?.aboutUrl?.get().toString()))
        }
    }

    fun clickSignOut() {
        val messageDialog = CommonMessageDialog(activity)
        messageDialog.setMessage(getString(R.string.tip_sign_out))
        messageDialog.setCommonMessageDialogCallBack(object :
            CommonMessageDialog.CommonMessageDialogCallBack {
            override fun confirmClick() {
                SharePreferenceData.setToken("")
                SharePreferenceData.setIsRc(false)
                ActivityManager.getActivityManager().destroyAllActivity()
                startActivity(LoginActivity.callIntent(activity))
            }
        })
        messageDialog.showDialog()
    }

    fun clickRegisterPrivacy(){
        if(TextUtils.isEmpty(viewModel?.registerPolicy?.text)){
            showToast(getString(R.string.re_get_privacy_tip))
            viewModel?.getRegisterPrivacy()
            return
        }
        viewModel?.registerPolicy?.let {
            CommonData.privacyPolicyList = mutableListOf(it)
            startActivity(WebPrivacyActivity.callIntent(activity,0))
        }
    }

    fun clickPrivacyPolicy(){
        if(TextUtils.isEmpty(viewModel?.privacyPolicy?.text)){
            showToast(getString(R.string.re_get_privacy_tip))
            viewModel?.getPrivacyPolicy()
            return
        }
        viewModel?.privacyPolicy?.let {
            CommonData.privacyPolicyList = mutableListOf(it)
            startActivity(WebPrivacyActivity.callIntent(activity,0))
        }
    }

    override fun attachViewModel(): Class<SettingViewModel> {
        return SettingViewModel::class.java
    }

    override fun attachMvvmView(): SettingMvvmView {
        return this
    }

    override fun attachTitleText(): String {
        return getString(R.string.title_setting)
    }

    override fun requestError(message: String, code: Int) {
        showToast(message)
        dismissLoading()
    }

    override fun showModelToast(message: String) {

    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {
        showLoading()
    }
}