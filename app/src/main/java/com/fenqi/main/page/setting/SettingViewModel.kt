package com.fenqi.main.page.setting

import androidx.databinding.ObservableField
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.bean.PrivacyPolicyBean
import com.fenqi.main.bean.UserPersonalResponseEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService

class SettingViewModel: BaseViewModel<MainApiService, SettingMvvmView>() {

    var appCache:ObservableField<String> = ObservableField<String>().apply { set("0kb") }

    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }

    var privacyPolicy:PrivacyPolicyBean = PrivacyPolicyBean()
    var registerPolicy:PrivacyPolicyBean = PrivacyPolicyBean()

    fun getRegisterPrivacy(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: PrivacyPolicyBean = json.decodeFromString(it)
                    registerPolicy = data
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.userProtocolInfos(CommonConstant.PRIVACY_POLICY_ZCXY)
        }
    }

    fun getPrivacyPolicy(){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: PrivacyPolicyBean = json.decodeFromString(it)
                    privacyPolicy = data
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.userProtocolInfos(CommonConstant.PRIVACY_POLICY_YSZC)
        }
    }


    var aboutUrl: ObservableField<String> = ObservableField<String>().apply { set("") }
    fun userPersonalData() {
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: UserPersonalResponseEntity = json.decodeFromString(it)
                    aboutUrl.set(data.aboutUrl)
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg, code)
            }
        }) {
            mService?.userPersonalCenter()
        }
    }
}