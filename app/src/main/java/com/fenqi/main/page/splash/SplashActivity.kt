package com.fenqi.main.page.splash

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.constant.CommonData
import com.fenqi.main.databinding.ActivitySplashBinding
import com.fenqi.main.page.login.LoginActivity
import com.fenqi.main.page.main.MainActivity
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.sharedata.SharePreferenceData
import com.fenqi.main.util.*
import com.fenqi.platformtools.utils.TitleBar

@SuppressLint("CustomSplashScreen")
class SplashActivity :
        BaseActivity<MainApiService, SplashMvvmView, SplashViewModel, ActivitySplashBinding>(),
        SplashMvvmView {

    companion object {
        fun callIntent(context: Context): Intent {
            return Intent(context, LoginActivity::class.java)
        }
    }

    override fun onShowViewById(): Int {
        return R.layout.activity_splash
    }

    @SuppressLint("SetTextI18n")
    override fun kinit() {
        TitleBar.changeTransparencyBar(activity)

        CommonData.IS_FIRST_OPEN = true

        openApp()
    }

    override fun onResume() {
        super.onResume()
    }

    private fun openApp() {
        startActivity(MainActivity.callIntent(activity))
        finish()
    }

    override fun attachViewModel(): Class<SplashViewModel> {
        return SplashViewModel::class.java
    }

    override fun attachMvvmView(): SplashMvvmView {
        return this
    }

    override fun attachTitleText(): String? {
        return null
    }

    override fun configAppCodeGet() {
        if (SharePreferenceData.getIsFistOpen() == true) {
            SharePreferenceData.setIsFistOpen(false)
        }
    }

    override fun requestError(message: String, code: Int) {
        showToast(message)
        dismissLoading()
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {
        showLoading()
    }
}