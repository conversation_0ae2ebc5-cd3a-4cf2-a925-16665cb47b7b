package com.fenqi.main.page.verifycode

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.view.View
import androidx.core.content.ContextCompat
import androidx.databinding.Observable.OnPropertyChangedCallback
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.postentity.EquivalentRegardingOrganizeEntity
import com.fenqi.main.postentity.FailCreatedThroatMatchEntity
import com.fenqi.main.databinding.ActivityVerifyCodeBinding
import com.fenqi.main.page.main.MainActivity
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.platformtools.utils.TitleBar

class VerifyCodeActivity :
    BaseActivity<MainApiService, VerifyCodeMvvmView, VerifyCodeViewModel, ActivityVerifyCodeBinding>(),
    VerifyCodeMvvmView {

    companion object {
        private const val VERIFY_CODE_PHONE = "VERIFY_CODE_PHONE"
        fun callIntent(context: Context, phone:String?): Intent {
            val intent = Intent(context, VerifyCodeActivity::class.java)
            intent.putExtra(VERIFY_CODE_PHONE,phone)
            return intent
        }
    }

    override fun onShowViewById(): Int {
        return R.layout.activity_verify_code
    }

    @SuppressLint("SetTextI18n")
    override fun kinit() {
        TitleBar.changeTransparencyBar(activity)

        dataBinding?.click = this
        dataBinding?.viewModel = viewModel

        val phoneNumber = intent.getStringExtra(VERIFY_CODE_PHONE)
        if(!TextUtils.isEmpty(phoneNumber)){
            viewModel?.phoneNumber?.set(phoneNumber)
            dataBinding?.buttonVerifyCodePhoneNumber?.text = "${getString(R.string.verify_code_send_to)}${phoneNumber}"
            showLoading()
            viewModel?.verifyCodeSend(FailCreatedThroatMatchEntity(mobile = phoneNumber.toString()))
        }

        dataBinding?.otpviewVerifyCode?.setOnClickListener {
            showLoading()
            viewModel?.verifyCodeSend(FailCreatedThroatMatchEntity(mobile = phoneNumber.toString()))
        }

        viewModel?.otp4?.addOnPropertyChangedCallback(object : OnPropertyChangedCallback() {
            override fun onPropertyChanged(
                sender: androidx.databinding.Observable?,
                propertyId: Int
            ) {
                if(TextUtils.isEmpty(viewModel?.otp4?.get())){
                    dataBinding?.buttonVerifyLoginSubmit?.setBackgroundResource(R.drawable.shape_grey_big_radius)
                    dataBinding?.buttonVerifyLoginSubmit?.setTextColor(ContextCompat.getColor(activity,R.color.color_white))
                    dataBinding?.buttonVerifyLoginSubmit?.isEnabled = false
                } else {
                    dataBinding?.buttonVerifyLoginSubmit?.setBackgroundResource(R.drawable.select_button_main)
                    dataBinding?.buttonVerifyLoginSubmit?.setTextColor(ContextCompat.getColor(activity,R.color.color_config_text_main))
                    dataBinding?.buttonVerifyLoginSubmit?.isEnabled = true
                }
            }
        })
        dataBinding?.otpviewVerifyCode?.setFinishedCallBack {

        }
    }

    fun clickConfirm(){
        if (TextUtils.isEmpty(viewModel?.otp1?.get()) || TextUtils.isEmpty(viewModel?.otp2?.get()) || TextUtils.isEmpty(viewModel?.otp3?.get()) || TextUtils.isEmpty(viewModel?.otp4?.get())) {
            showToast(getString(R.string.toast_otp_warnning))
            return
        }
        showLoading()
        viewModel?.login(EquivalentRegardingOrganizeEntity(mobile = viewModel?.phoneNumber?.get().toString(),code = "${viewModel?.otp1?.get()}${viewModel?.otp2?.get()}${viewModel?.otp3?.get()}${viewModel?.otp4?.get()}"))
    }

    fun clickContainer(){
        dataBinding?.viewCodeKeyContainer?.visibility = View.GONE
    }

    fun clickKeyboard(number:Int){
        if(TextUtils.isEmpty(viewModel?.otp1?.get())){
            viewModel?.otp1?.set(number.toString())
            return
        }
        if(TextUtils.isEmpty(viewModel?.otp2?.get())){
            viewModel?.otp2?.set(number.toString())
            return
        }
        if(TextUtils.isEmpty(viewModel?.otp3?.get())){
            viewModel?.otp3?.set(number.toString())
            return
        }
        if(TextUtils.isEmpty(viewModel?.otp4?.get())){
            viewModel?.otp4?.set(number.toString())
            return
        }
    }

    fun clickDownKey(){
        dataBinding?.viewCodeKeyContainer?.visibility = View.GONE
    }
    fun clickDelete(){
        if(!TextUtils.isEmpty(viewModel?.otp4?.get())){
            viewModel?.otp4?.set("")
            return
        }
        if(!TextUtils.isEmpty(viewModel?.otp3?.get())){
            viewModel?.otp3?.set("")
            return
        }
        if(!TextUtils.isEmpty(viewModel?.otp2?.get())){
            viewModel?.otp2?.set("")
            return
        }
        if(!TextUtils.isEmpty(viewModel?.otp1?.get())){
            viewModel?.otp1?.set("")
            return
        }
    }
    fun clickInputContainer(){
        dataBinding?.viewCodeKeyContainer?.visibility = View.VISIBLE
    }

    fun clickGoBack(){
        finish()
    }

    override fun attachViewModel(): Class<VerifyCodeViewModel> {
        return VerifyCodeViewModel::class.java
    }

    override fun attachMvvmView(): VerifyCodeMvvmView {
        return this
    }

    override fun attachTitleText(): String? {
        return ""
    }

    override fun verifySendSuccess(seconds: Int?) {
        dismissLoading()
        if (seconds != null) {
            dataBinding?.otpviewVerifyCode?.startTimer(seconds)
        }
    }

    override fun loginSuccess() {
        dismissLoading()

        startActivity(MainActivity.callIntent(activity))
        finish()
    }

    override fun requestError(message: String, code: Int) {
        showToast(message)
        dismissLoading()
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {
        showLoading()
    }
}