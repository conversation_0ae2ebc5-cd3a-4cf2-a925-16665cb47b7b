package com.fenqi.main.page.verifycode

import android.text.TextUtils
import androidx.databinding.ObservableField
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.postentity.EquivalentRegardingOrganizeEntity
import com.fenqi.main.bean.LoginResponseEntity
import com.fenqi.main.postentity.FailCreatedThroatMatchEntity
import com.fenqi.main.bean.VerifyCodeSendResponseEntity
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.sharedata.SharePreferenceData
import com.fenqi.main.util.AppInfoUploadUtil

class VerifyCodeViewModel : BaseViewModel<MainApiService, VerifyCodeMvvmView>() {

    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }

    var phoneNumber: ObservableField<String> = ObservableField<String>().apply { set("") }
    var code: ObservableField<String> = ObservableField<String>().apply { set("") }

    var otp1: ObservableField<String> = ObservableField<String>().apply { set("") }
    var otp2: ObservableField<String> = ObservableField<String>().apply { set("") }
    var otp3: ObservableField<String> = ObservableField<String>().apply { set("") }
    var otp4: ObservableField<String> = ObservableField<String>().apply { set("") }

    fun verifyCodeSend(verifyCodeSendPostEntity: FailCreatedThroatMatchEntity){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val verifyCodeSendResponseEntity: VerifyCodeSendResponseEntity = json.decodeFromString(it)
                    if(verifyCodeSendResponseEntity!=null){
                        val verifyCodeSendVo = verifyCodeSendResponseEntity.sendVerifyCodeSuccessVo
                        mvvmView?.verifySendSuccess(verifyCodeSendVo?.effectiveSeconds)
                    }
                }

            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.verifyCodeSend(RetrofitBuilder.getInstance().getRequestBody(verifyCodeSendPostEntity))
        }
    }


    fun login(loginPostEntity: EquivalentRegardingOrganizeEntity){
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val data: LoginResponseEntity = json.decodeFromString(it)
                    if(!TextUtils.isEmpty(data.token)){
                        SharePreferenceData.setToken(data.token.toString())
                        SharePreferenceData.setPhoneNumber(phoneNumber.get().toString())

                        AppInfoUploadUtil.getInstance().deviceInfoUpload()
                        mvvmView?.loginSuccess()
                    }
                    if (data.status==2){
                        SharePreferenceData.setIsRc(true)
                    }
                }
            }
            onFailure { msg, code ->
                mvvmView?.requestError(msg,code)
            }
        }) {
            mService?.userLogin(RetrofitBuilder.getInstance().getRequestBody(loginPostEntity))
        }
    }
}