package com.fenqi.main.page.web

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.text.TextUtils
import android.view.View
import android.webkit.*
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.FileProvider
import com.fenqi.main.BuildConfig
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.databinding.ActivityCommonWebBinding
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.util.LogUtil
import com.fenqi.main.util.TakePhotoUtil
import com.fenqi.main.view.dialog.TakePhotoSelecterDialog
import com.tbruyelle.rxpermissions3.RxPermissions
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.*


public class CommonWebActivity:
    BaseActivity<MainApiService, CommonWebMvvmView, CommonWebViewModel, ActivityCommonWebBinding>(),CommonWebMvvmView {

    companion object {

        private const val COMMON_WEB_URL = "COMMON_WEB_URL"
        private const val COMMON_WEB_TITLE = "COMMON_WEB_TITLE"
        private const val COMMON_WEB_IS_CARRIER = "COMMON_WEB_IS_CARRIER"

        private const val COMMON_WEB_FROM = "COMMON_WEB_FROM"

        const val REQUEST_VIDEO_CAPTURE = 1000

        fun callIntent(context: Context, title: String, url: String): Intent {
            val intent = Intent(context, CommonWebActivity::class.java)
            intent.putExtra(COMMON_WEB_URL,url)
            intent.putExtra(COMMON_WEB_TITLE,title)
            return intent
        }

        fun callIntent(context: Context, title: String, url: String,isCarrier:Boolean): Intent {
            val intent = Intent(context, CommonWebActivity::class.java)
            intent.putExtra(COMMON_WEB_URL,url)
            intent.putExtra(COMMON_WEB_TITLE,title)
            intent.putExtra(COMMON_WEB_IS_CARRIER,isCarrier)
            return intent
        }

        fun callIntentFrom(context: Context, url: String,from:String): Intent {
            val intent = Intent(context, CommonWebActivity::class.java)
            intent.putExtra(COMMON_WEB_URL,url)
            intent.putExtra(COMMON_WEB_FROM,from)
            return intent
        }
    }

    private lateinit var tvTitle:TextView
    private var isCarrier = false
    private lateinit var filePathCallbackLevel: ValueCallback<Array<Uri>>
    private var photoUri: Uri? = null

    private var from:String = ""

    override fun onShowViewById(): Int {
        return R.layout.activity_common_web
    }
    override fun kinit() {
        initLauncher()
        initData()
        initWebview()
        initView()
    }

    private fun initLauncher() {

    }

    private fun initData() {
        val bundle = intent.extras
        bundle?.also {
            viewModel?.title?.set(it.getString(COMMON_WEB_TITLE))
            viewModel?.url?.set(it.getString(COMMON_WEB_URL))
            isCarrier = it.getBoolean(COMMON_WEB_IS_CARRIER)

            from = it.getString(COMMON_WEB_FROM).toString()
        }

        tvTitle = findViewById(R.id.text_base_title)
        tvTitle.text = viewModel?.title?.get()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        dataBinding?.webview?.saveState(outState)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        dataBinding?.webview?.restoreState(savedInstanceState)
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initWebview() {
        val webView = dataBinding?.webview
        val webSettings = webView?.settings

        webSettings?.apply {
            javaScriptEnabled = true
            layoutAlgorithm = WebSettings.LayoutAlgorithm.NORMAL
            useWideViewPort = true
            javaScriptCanOpenWindowsAutomatically = true
            domStorageEnabled = true
            loadWithOverviewMode = true
        }

        if(Build.VERSION.SDK_INT > Build.VERSION_CODES.LOLLIPOP){
            webSettings?.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        }

        webView?.webViewClient = object :WebViewClient(){

            override fun shouldOverrideUrlLoading(
                view: WebView?,
                request: WebResourceRequest?
            ): Boolean {
                val url = request?.url.toString()
                if(url.endsWith(".apk") || url.startsWith("tel:") || url.startsWith("upi:")){
                    val intent = Intent(Intent.ACTION_VIEW,Uri.parse(url))
                    startActivity(intent)
                    return true
                }
                LogUtil.log("shouldOverrideUrlLoading-->${url}")
                return super.shouldOverrideUrlLoading(view, request)
            }

            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                LogUtil.log("onPageStarted--->${url}")
                super.onPageStarted(view, url, favicon)
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                LogUtil.log("onPageFinished--->${url}")
                if(isCarrier){
                    val js = "javascript:(function() {document.getElementsByClassName(\"title\")[0].style.display=\'none\';" + "})()"
                    dataBinding?.webview?.loadUrl(js)
                }
                super.onPageFinished(view, url)

            }
        }

        webView?.webChromeClient = object :WebChromeClient(){
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                if(newProgress == 100){
                    dataBinding?.webviewProgressbar?.visibility = View.GONE
                } else {
                    dataBinding?.webviewProgressbar?.visibility = View.VISIBLE
                    dataBinding?.webviewProgressbar?.progress = newProgress
                }
            }


            override fun onReceivedTitle(view: WebView?, title: String?) {
                super.onReceivedTitle(view, title)
                CoroutineScope(Dispatchers.Main).launch {
                    if(!TextUtils.isEmpty(title)){
                        tvTitle.text = title
                    }
                }
            }

            override fun onShowFileChooser(
                webView: WebView?,
                filePathCallback: ValueCallback<Array<Uri>>?,
                fileChooserParams: FileChooserParams?
            ): Boolean {

                filePathCallbackLevel = filePathCallback!!

                if(from == CommonConstant.WEB_FROM_OCR){
                    startGetVideo()
                } else {
                    val takePhotoSelectDialog = TakePhotoSelecterDialog(activity)
                    takePhotoSelectDialog.setTakePhotoListener(object: TakePhotoSelecterDialog.TakePhotoListener{
                        override fun clickTakePicture() {
                            takePhotoSelectDialog.dismissDialog()
                            judgePermissionCamera()
                        }

                        override fun clickAlbum() {
                            takePhotoSelectDialog.dismissDialog()
                            judgePermissionImage()
                        }
                        override fun clickCancelCallback() {
                            takePhotoSelectDialog.dismissDialog()
                            filePathCallbackLevel.onReceiveValue(arrayOf())
                        }
                    }).setTakePhotoDismissListener {
                        filePathCallbackLevel.onReceiveValue(arrayOf())
                    }
                    takePhotoSelectDialog.showDialog()
                }
                return true
            }
        }

        webView?.addJavascriptInterface(CommonWebJavaScript(activity as CommonWebActivity,{
            dismissLoading()
        },{
            showLoading()
        }),"android")

        webView?.loadUrl(viewModel?.url?.get().toString())
    }


    @SuppressLint("CheckResult")
    fun startGetVideo(){
        RxPermissions(this).request(
            Manifest.permission.CAMERA,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
        )
            .subscribe {
                try {
                    if(it){
                        TakePhotoUtil.getInstance().takeVideo(activity, successListener = { filePath->
                            LogUtil.log("path----${filePath}")
                            val results = arrayOf(Uri.parse(filePath))
                            filePathCallbackLevel.onReceiveValue(results)
                        }, cancelListener = {
                            filePathCallbackLevel.onReceiveValue(arrayOf())
                        })
                    } else {
                        filePathCallbackLevel.onReceiveValue(arrayOf())
                    }
                } catch (exception: Exception) {
                    exception.printStackTrace()
                    filePathCallbackLevel.onReceiveValue(arrayOf())
                }
            }
    }

    @SuppressLint("CheckResult")
    private fun judgePermissionCamera(){
        RxPermissions(this).request(
            Manifest.permission.CAMERA,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
        )
            .subscribe {
                try {
                    if(it){
                       TakePhotoUtil.getInstance().takeCameraWeb(activity, successListener = {filePath->
                           filePathCallbackLevel.onReceiveValue(arrayOf(Uri.parse(filePath)))
                       }, cancelListener = {
                           filePathCallbackLevel.onReceiveValue(arrayOf())
                       })
                    } else {
                        filePathCallbackLevel.onReceiveValue(arrayOf())
                    }
                } catch (exception: Exception) {
                    exception.printStackTrace()
                    filePathCallbackLevel.onReceiveValue(arrayOf())
                }
            }
    }

    @SuppressLint("CheckResult")
    private fun judgePermissionImage(){
        RxPermissions(this).request(
            Manifest.permission.READ_EXTERNAL_STORAGE,
        )
            .subscribe {
                try {
                    if(it){
                        TakePhotoUtil.getInstance().selectImageWeb(activity, successListener = {filePath->
                            filePathCallbackLevel.onReceiveValue(arrayOf(Uri.parse(filePath)))
                        }, cancelListener = {
                            filePathCallbackLevel.onReceiveValue(arrayOf())
                        })
                    } else {
                        filePathCallbackLevel.onReceiveValue(arrayOf())
                    }
                } catch (exception: Exception) {
                    exception.printStackTrace()
                    filePathCallbackLevel.onReceiveValue(arrayOf())
                }
            }
    }

    private fun initView() {

    }

    override fun attachViewModel(): Class<CommonWebViewModel> {
        return CommonWebViewModel::class.java
    }

    override fun attachMvvmView(): CommonWebMvvmView {
        return this
    }

    override fun attachTitleText(): String {
        return getString(R.string.app_name)
    }

    override fun requestError(message: String, code: Int) {
        dismissLoading()
        showToast(message)
    }

    override fun showModelToast(message: String) {
        showToast(message)
    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {
        showLoading()
    }
}