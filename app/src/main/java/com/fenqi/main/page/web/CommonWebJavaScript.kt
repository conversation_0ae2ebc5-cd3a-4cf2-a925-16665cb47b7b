package com.fenqi.main.page.web

import android.content.Context
import android.webkit.JavascriptInterface
import com.fenqi.main.page.main.MainActivity
import com.fenqi.main.util.AuthJumpUtil
import com.fenqi.main.util.LogUtil

class CommonWebJavaScript(var activity: CommonWebActivity,var webDismissLoadCallBack:()->Unit,var webShowLoadCallBack:()->Unit) {

    @JavascriptInterface
    fun jsAppPay(payInfo:String){
        activity.runOnUiThread {

        }
    }

    @JavascriptInterface
    fun jsJumpAppHome(){
        activity.startActivity(MainActivity.callIntent(activity))
        activity.finish()
    }

    @JavascriptInterface
    fun jsGoBack(){
        activity.finish()
    }

    @JavascriptInterface
    fun jsIdentityBack(pass:<PERSON>olean){
        if(pass){
            webShowLoadCallBack.invoke()
            AuthJumpUtil.getInstance().setFinishedCallBack {
                webDismissLoadCallBack.invoke()
                activity.finish()
            }.setOnFailed {
                webDismissLoadCallBack.invoke()
                activity.finish()
            }.judgeJump(activity as Context)
        } else {
            activity.finish()
        }
    }

}