package com.fenqi.main.page.web

import android.content.Context
import androidx.databinding.ObservableField
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.bean.MainHomeCenterVoEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.constant.CommonData
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.util.AuthJumpUtil

class CommonWebViewModel: BaseViewModel<MainApiService, CommonWebMvvmView>() {

    var url: ObservableField<String> = ObservableField<String>().apply { set("") }
    var title: ObservableField<String> = ObservableField<String>().apply { set("") }

    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }
}