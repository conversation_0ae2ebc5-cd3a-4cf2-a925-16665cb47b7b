package com.fenqi.main.page.webprivacy

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Build
import android.webkit.WebSettings
import android.widget.TextView
import com.fenqi.main.R
import com.fenqi.main.base.BaseActivity
import com.fenqi.main.bean.PrivacyPolicyBean
import com.fenqi.main.constant.CommonData
import com.fenqi.main.databinding.ActivityWebPrivacyBinding
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.util.LogUtil

class WebPrivacyActivity :
    BaseActivity<MainApiService, WebPrivacyMvvmView, WebPrivacyViewModel, ActivityWebPrivacyBinding>(),
    WebPrivacyMvvmView {

    companion object {
        private const val PRIVACY_INDEX = "PRIVACY_INDEX"
        fun callIntent(activity: Activity?,index:Int): Intent {
            val intent = Intent(activity, WebPrivacyActivity::class.java)
            intent.putExtra(PRIVACY_INDEX,index)
            return intent
        }
    }

    private var nextTag = 50000
    private var dataSize = 0
    private var targetIndex = 0

    private var privacyPolicyBeanList:MutableList<PrivacyPolicyBean> = mutableListOf()

    override fun onShowViewById(): Int {
        return R.layout.activity_web_privacy
    }

    @SuppressLint("SetJavaScriptEnabled")
    override fun kinit() {
        dataBinding?.click = this
        dataBinding?.viewModel = viewModel

        targetIndex = intent.getIntExtra(PRIVACY_INDEX,0)

        val textTitle:TextView = findViewById(R.id.text_base_title)

        val webView = dataBinding?.webviewPrivacyPolicy
        val webSettings = webView?.settings

        webSettings?.apply {
            javaScriptEnabled = true
            layoutAlgorithm = WebSettings.LayoutAlgorithm.NORMAL
            useWideViewPort = true
            javaScriptCanOpenWindowsAutomatically = true
            domStorageEnabled = true
            loadWithOverviewMode = true
            textZoom = 250
        }

        if(Build.VERSION.SDK_INT > Build.VERSION_CODES.LOLLIPOP){
            webSettings?.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        }

        privacyPolicyBeanList = CommonData.privacyPolicyList

        LogUtil.log("targetIndex-->${targetIndex}")

        if(privacyPolicyBeanList.size>1){
            viewModel?.showNextStepView?.set(true)

            val privacyPolicyBean = privacyPolicyBeanList[targetIndex]
            textTitle.text = privacyPolicyBean.title

            dataSize = privacyPolicyBeanList.size

            nextTag = dataSize*30000+targetIndex

            LogUtil.log("nextTag-->${nextTag}")

            targetIndex = nextTag%dataSize

            LogUtil.log("targetIndex--new-->${targetIndex}")

            showTargetView()
        } else {
            dataSize = 1
            viewModel?.showNextStepView?.set(false)

            if(privacyPolicyBeanList.size>0){
                val privacyPolicyBean = privacyPolicyBeanList[0]
                textTitle.text = privacyPolicyBean.title

                val content = privacyPolicyBean.text?.replace("{{ appName }}",getString(R.string.app_name))?.replace("{{ companyName }}",
                    privacyPolicyBean.companyName.toString()
                )
                LogUtil.log("content-web-${content}")
                webView?.loadDataWithBaseURL(null, content.toString(),"text/html","utf-8",null)
            }
        }
    }

    fun clickPrePage(){
        nextTag--
        targetIndex = nextTag%dataSize
        showTargetView()
    }

    fun clickNextPage(){
        nextTag++
        targetIndex = nextTag%dataSize
        showTargetView()
    }

    private fun showTargetView(){
        if(targetIndex<privacyPolicyBeanList.size){
            val privacyPolicyBean = privacyPolicyBeanList[targetIndex]
            viewModel?.targetTitle?.set(privacyPolicyBean.title)

            dataBinding?.webviewPrivacyPolicy?.loadDataWithBaseURL(null,
                privacyPolicyBean.text?.replace("{{ appName }}",getString(R.string.app_name))
                    ?.replace("{{ companyName }}", privacyPolicyBean.companyName.toString()).toString(),"text/html","utf-8",null)
        }
    }

    override fun attachViewModel(): Class<WebPrivacyViewModel> {
        return WebPrivacyViewModel::class.java
    }

    override fun attachMvvmView(): WebPrivacyMvvmView {
        return this
    }

    override fun attachTitleText(): String {
        return getString(R.string.privacy_policy_common_title)
    }

    override fun requestError(message: String, code: Int) {
        showToast(message)
        dismissLoading()
    }

    override fun showModelToast(message: String) {

    }

    override fun dismissModelLoading() {
        dismissLoading()
    }

    override fun showModelLoading() {
        showLoading()
    }
}