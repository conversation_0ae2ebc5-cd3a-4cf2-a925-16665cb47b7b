package com.fenqi.main.page.webprivacy

import androidx.databinding.ObservableField
import com.fenqi.main.base.BaseViewModel
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService

class WebPrivacyViewModel: BaseViewModel<MainApiService, WebPrivacyMvvmView>() {

    var showNextStepView:ObservableField<Boolean> = ObservableField<Boolean>().apply { set(false) }
    var targetTitle:ObservableField<String> = ObservableField<String>().apply { set("") }

    override fun bindService(): MainApiService {
        return RetrofitBuilder.getInstance().createApi(activity!!)
    }
}