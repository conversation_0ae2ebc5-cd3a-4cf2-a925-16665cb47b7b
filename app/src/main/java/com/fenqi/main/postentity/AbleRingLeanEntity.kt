package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class AbleRingLeanEntity(
    var bellIncreasing: Int = 0,
    var actionNear: Boolean = false,
    var convert: Int = 0,
    var destroyCapitalHer: String? = "",
    var usFleeBorn: Int = 0,
    var programInvestorEnable: Boolean = false,
    var interpretableNumber: Int = 0,
    var survivorClickPut: String? = "",
    var relation: Int = 0,
    var distinctionTurnBroom: Boolean = false,
    var markSafety: Boolean = false,
    var landDestroy: Int = 0,
    var shapePound: Int = 0,
    var friendMobile: String? = "",
    var thusParenthesisPowerful: Int = 0,
    var transitionFilmTongue: String? = "",
    var problemWhichApply: Int = 0,
    var precedingSurePursue: Int = 0,
    var type: Int = 0,
    var specificDriver: String? = "",
    var validBranch: Int = 0,
    var four: String? = "",
    var presentation: Int = 0,
    var friendName: String? = "",
)
