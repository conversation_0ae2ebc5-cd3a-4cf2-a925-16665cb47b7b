package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class AccidentallyPasswordFriendshipAwareEntity(
    var cholesterolHeatCentury: Boolean = false,
    var subdireBlackPlan: Boolean = false,
    var preserve: Boolean = false,
    var interruptHeadquarters: Boolean = false,
    var indus: Int = 0,
    var versusConflict: String? = "",
    var factory: Int = 0,
    var ally: Int = 0,
    var bankName: String? = "",
    var mobile: String? = "",
    var overlook: Int = 0,
    var death: Boolean = false,
    var accessibleAbove: String? = "",
    var orderType: Int? = 0,
    var unusedLengthExtra: Boolean = false,
    var successfulOhQuotation: String? = "",
    var cardNo: String? = "",
    var externalExceedHear: Int = 0,
    var terminateConcertDensity: String? = "",
    var bankCode: String? = "",
    var entrySignalWorker: String? = "",
)
