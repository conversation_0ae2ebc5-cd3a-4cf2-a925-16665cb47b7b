package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class AppearSexualProsecutorEntity(
    var movie: Boolean = false,
    var naturally: Boolean = false,
    var privacyModifiedArgue: Int = 0,
    var terroristPair: Boolean = false,
    var bank: String? = "",
    var teenagerConstitutionalBroom: String? = "",
    var requestType: Int = 0,
    var pattern: String? = "",
    var stillPracticalCeremony: Int = 0,
    var billNo: String? = "",
    var radioAssociation: Int = 0,
    var gangWhoFree: Int = 0,
    var troubleAbove: String? = "",
    var mall: Boolean = false,
    var punishmentCredit: String? = "",
    var water: Int = 0,
    var referenceProfit: Boolean = false,
    var payType: Int = 0,
    var politicianDecideAssure: String? = "",
    var progressArgue: Int = 0,
    var generalSpill: String? = "",
    var scriptMonochromePound: Boolean = false,
)
