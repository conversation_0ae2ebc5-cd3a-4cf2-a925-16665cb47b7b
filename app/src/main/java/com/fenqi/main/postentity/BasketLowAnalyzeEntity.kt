package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class BasketLowAnalyzeEntity(
    var expressionCongressionalCollection: String? = "",
    var back: String? = "",
    var darkOperating: String? = "",
    var sourceWantMovement: Int = 0,
    var tubeHillWash: Boolean = false,
    var extraQuestion: Boolean = false,
    var admission: Boolean = false,
    var roadAnticipate: Int = 0,
    var installNearlyGaze: Int = 0,
    var lng: String? = "",
    var designated: Int = 0,
    var eagerPrevious: Int = 0,
    var emulationBenchCloser: Int = 0,
    var redrawRice: String? = "",
    var freezeAnybody: Int = 0,
    var blood: Boolean = false,
    var arithmeticDirtArrive: Int = 0,
    var spending: Int = 0,
    var lat: String? = "",
)
