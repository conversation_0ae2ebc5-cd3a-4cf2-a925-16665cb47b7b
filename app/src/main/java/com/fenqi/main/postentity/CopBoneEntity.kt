package com.fenqi.main.postentity

import com.fenqi.main.bean.FaceCardConfigVoEntity
import kotlinx.serialization.Serializable

@Serializable
data class CopBoneEntity(
    var bladeEven: Int = 0,
    var loadingSpread: String? = "",
    var wheneverExhibit: Boolean = false,
    var back: String? = "",
    var publiclyBritishVt: Boolean = false,
    var coat: Int = 0,
    var pictureType: Int = 0,
    var developing: Int = 0,
    var assistance: Boolean = false,
    var panelCareerEach: Boolean = false,
    var etcFrequency: String? = "",
    var onceDoctor: Boolean = false,
    var configVOList: MutableList<FaceCardConfigVoEntity>? = mutableListOf(),
    var tPopTurnkey: Boolean = false,
    var southShell: String? = "",
    var distributionSchemeShell: Boolean = false,
    var cornerOccasionFilm: Boolean = false,
    var bookWhere: Int = 0,
    var post: Boolean = false,
    var translation: Boolean = false,
)
