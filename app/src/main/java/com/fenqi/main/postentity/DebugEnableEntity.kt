package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class DebugEnableEntity(
    var offense: Int = 0,
    var apparent: String? = "",
    var winter: Int = 0,
    var locationAttribute: Boolean = false,
    var splashHostRedraw: Int = 0,
    var saveDeskGas: Int = 0,
    var native: Boolean = false,
    var result: String? = "",
    var courageEngineer: Int = 0,
    var productBlockFine: String? = "",
    var believeProfile: String? = "",
    var may: Boolean = false,
    var smileSymbol: Boolean = false,
    var abbreviateGoogle: String? = "",
    var armsCostDefensive: Int = 0,
    var obviousCustom: Int = 0,
    var vesselIsland: String? = "",
    var popularPerspectiveFrequency: String? = "",
    var sensitiveWestInsight: Boolean = false,
    var flowId: String? = "",
)
