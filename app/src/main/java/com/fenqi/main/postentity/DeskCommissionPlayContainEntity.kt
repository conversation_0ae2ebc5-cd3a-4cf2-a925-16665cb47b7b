package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class DeskCommissionPlayContainEntity(
    var quickly: Boolean = false,
    var ultimatelyOnProject: Int = 0,
    var situation: Boolean = false,
    var musicalWave: String? = "",
    var connectLogarithmMouth: Int = 0,
    var accordingGroup: String? = "",
    var detectTestimonyHardly: Boolean = false,
    var awayPrivacy: Int = 0,
    var coast: Int = 0,
    var foundationFindAccuracy: Int = 0,
    var review: String? = "",
    var repaymentCard: String? = "",
    var province: Boolean = false,
    var streamTicketGolden: String? = "",
    var though: Int = 0,
    var figureExplainHow: String? = "",
    var supportKillingArchitecture: String? = "",
    var voucherUrl: String? = "",
    var integerPileHandling: Boolean = false,
    var requestNo: String? = "",
    var tieFurniture: Int = 0,
)
