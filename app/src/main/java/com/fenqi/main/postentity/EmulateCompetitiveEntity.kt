package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class EmulateCompetitiveEntity(
    var promptMapConstitutional: Boolean = false,
    var leading: Int = 0,
    var longitude: String? = "",
    var exponentialLikeMistake: Int = 0,
    var sound: Boolean = false,
    var memory: Boolean = false,
    var heavy: Boolean = false,
    var operate: String? = "",
    var latitude: String? = "",
    var regardlessCodeLesson: String? = "",
    var loudIncreasedSubject: Boolean = false,
    var otherTestimony: String? = "",
    var uponBarShop: Boolean = false,
    var headquartersGroupReappears: Int = 0,
    var defeatRecent: Int = 0,
    var evaluateChildhoodLimiting: Boolean = false,
    var payType: String? = "",
    var stable: String? = "",
    var earlyParticipantIncrement: String? = "",
    var payAmount: String? = "",
    var auth: Int = 0,
    var sky: String? = "",
)
