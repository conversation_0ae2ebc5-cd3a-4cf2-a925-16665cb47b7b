package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class EquivalentRegardingOrganizeEntity(
    var multiple: String? = "",
    var supposeEducational: Boolean = false,
    var blowSecondary: Int = 0,
    var modestMemory: Boolean = false,
    var code: String? = "",
    var capacityDetailLocation: Boolean = false,
    var labeledDeficitDistribute: Int = 0,
    var mobile: String? = "",
    var physician: Int = 0,
    var mayorPrevent: Int = 0,
    var assist: Int = 0,
    var degree: String? = "",
    var statusDismissDepartment: Int = 0,
    var split: String? = "",
    var spill: Boolean = false,
    var panPossibility: Int = 0,
    var secretary: Int = 0,
)