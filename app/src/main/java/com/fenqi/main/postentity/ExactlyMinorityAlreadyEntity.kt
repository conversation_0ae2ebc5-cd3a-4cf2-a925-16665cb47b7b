package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class ExactlyMinorityAlreadyEntity(
    var knowledge: Boolean = false,
    var fewOnes: String? = "",
    val simState: Int = 100,
    var androidId: String? = "",
    var macAddress: String? = "",
    var distinct: Int = 0,
    var hasRoot: Boolean = false,
    var leadBowl: String? = "",
    var imei: String? = "",
    var heavilyThreaten: Int = 0,
    var heritage: String? = "",
    var developWinner: Int = 0,
    var furnitureVery: Boolean = false,
    var proprietary: Boolean = false,
    var letter: Int = 0,
    var whereasInjury: String? = "",
    var visibleSignDraft: String? = "",
    var increasingly: Int = 0,
    var publicly: Int = 0,
    var deviceBrand: String? = "",
    var description: Int = 0,
    var phoneVersion: String? = "",
    var googleAdId: String? = "",
    var subscript: Boolean = false,
    var alter: String? = "",
    var hardKeyboard: String? = "",
)
