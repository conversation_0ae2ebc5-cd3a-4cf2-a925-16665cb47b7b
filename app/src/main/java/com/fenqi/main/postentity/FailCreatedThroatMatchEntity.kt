package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class FailCreatedThroatMatchEntity(
    var interpretationOngoingConfirm: String? = "",
    var dry: Boolean = false,
    var busyExceptEsoteric: String? = "",
    var possibility: Boolean = false,
    var receive: Int = 0,
    var privateDestinationParticipant: Int = 0,
    var therapy: String? = "",
    var necessarily: String? = "",
    var tankArticle: String? = "",
    var billion: String? = "",
    var monochrome: String? = "",
    var switchLowCourt: String? = "",
    var softwareRelative: Boolean = false,
    var vital: Boolean = false,
    var development: Boolean = false,
    var separate: Int = 0,
    var songVariousProject: Int = 0,
    var troubleAnimalVoice: String? = "",
    var mobile: String? = "",
    var hourDirty: String? = "",
)