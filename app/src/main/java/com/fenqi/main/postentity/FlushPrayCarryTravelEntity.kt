package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class FlushPrayCarryTravelEntity(
    var slightShowPink: Int = 0,
    var achieve: String? = "",
    var learnStanding: String? = "",
    var identity: String? = "",
    var publicBall: Boolean = false,
    var both: String? = "",
    var statement: Int = 0,
    var overall: Int = 0,
    var billReread: String? = "",
    var exactlyActual: String? = "",
    var pace: Int = 0,
    var productCode: String? = "",
    var personallyClinicalControversy: Int = 0,
    var decisionConnectionProvince: String? = "",
    var obsoleteShotChange: Boolean = false,
    var depthAttach: String? = "",
    var successfullyDanceSpecifically: String? = "",
    var biosRide: Int = 0,
    var youthFaculty: String? = "",
    var considerData: Boolean = false,
)
