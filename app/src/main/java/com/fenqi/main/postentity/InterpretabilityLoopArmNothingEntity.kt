package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class InterpretabilityLoopArmNothingEntity(
    var reorder: Int = 0,
    var resembleTransportable: Int = 0,
    var monitorEventually: String? = "",
    var smsCode: String? = "",
    var classify: Boolean = false,
    var presidentSignificance: Int = 0,
    var escapePaper: Int = 0,
    var flowId: String? = "",
    var mutual: Boolean = false,
    var code: String? = "",
    var mobile: String? = "",
    var ready: Int = 0,
    var name: String? = "",
    var bankName: String? = "",
    var brushGold: Int = 0,
    var sendOrOverflow: String? = "",
    var journalist: Boolean = false,
    var permanentSpellCommunicate: String? = "",
    var proofFully: Int = 0,
    var download: String? = "",
    var cardNo: String? = "",
    var rainOilSacred: Int = 0,
    var orderType: Int? = 0,
)
