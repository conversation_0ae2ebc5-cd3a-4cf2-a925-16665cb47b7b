package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class KernelSecurityEntity(
    var authorityMilk: Int = 0,
    var capability: String? = "",
    var thatAdGap: Int = 0,
    var hungryHot: Boolean = false,
    var typeDevelopingWine: String? = "",
    var rowThat: String? = "",
    var historicConstantly: Int = 0,
    var possibleCircle: String? = "",
    var shopDevelopNow: Int = 0,
    var collapse: String? = "",
    var templePeak: Int = 0,
    var fateExperience: Boolean = false,
    val idCard: String? = "",
    var prefixTypewriterRefugee: Int = 0,
    var heavy: String? = "",
    var insertChange: Boolean = false,
    var plusMistake: Int = 0,
    var accessMinisterAssert: Int = 0,
    val realName: String? = "",
    var destinationFound: Int = 0,
    var may: Boolean = false,
    var boyfriendPaint: Boolean = false,
    val frontUrl: String? = "",
    val faceUrl: String? = "",
)
