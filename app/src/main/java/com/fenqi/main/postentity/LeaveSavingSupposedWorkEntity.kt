package com.fenqi.main.postentity

import com.fenqi.main.bean.WifiEntity
import kotlinx.serialization.Serializable

@Serializable
data class LeaveSavingSupposedWorkEntity(
    var gatePersonality: Boolean = false,
    var protein: String? = "",
    var subroutine: Int = 0,
    var airAgainst: Int = 0,
    var privacySatellite: Boolean = false,
    var currentWifi: WifiEntity? = null,
    var collectionLeftBroom: Boolean = false,
    var incorporateArab: String? = "",
    var environmentRise: Boolean = false,
    var updatedFortune: Int = 0,
    var down: String? = "",
    var nearbyWifis: MutableList<WifiEntity>? = mutableListOf(),
    var saving: Boolean = false,
    var shelfDomestic: Int = 0,
    var step: String? = "",
    var near: String? = "",
    var imageCorrectBeach: Boolean = false,
    var blinking: Int = 0,
    var internally: Boolean = false,
)