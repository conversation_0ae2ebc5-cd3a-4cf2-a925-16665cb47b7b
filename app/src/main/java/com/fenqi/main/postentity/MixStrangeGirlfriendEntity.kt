package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class MixStrangeGirlfriendEntity(
    var lunchSoulPace: String? = "",
    var blinkingBaseGroup: String? = "",
    var cardNo: String? = "",
    var sameOnionObligation: String? = "",
    var testingMixUtility: Int = 0,
    var linkLarge: Boolean = false,
    var autoMentalCheese: String? = "",
    var fourscoreDelete: String? = "",
    var pileNot: String? = "",
    var the: String? = "",
    var rifleSolidBeginning: String? = "",
    var unable: Boolean = false,
    var grocery: Int = 0,
    var floating: Boolean = false,
    var abortionWanderContinue: Int = 0,
    var like: Boolean = false,
    var matterChapterProgress: Int = 0,
    var screen: String? = "",
    var sidePositiveRam: Int = 0,
)
