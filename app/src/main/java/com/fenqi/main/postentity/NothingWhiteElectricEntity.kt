package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class NothingWhiteElectricEntity(
    var houseMaybeGod: String? = "",
    var surelyValid: String? = "",
    var data: String? = "",
    var listen: Boolean = false,
    var sourceType: Int = 0,
    var reliefSupporterTerrorism: Boolean = false,
    var ohPertainWill: String? = "",
    var contiguousInstanceGain: Int = 0,
    var novelGerman: String? = "",
    var filterTomorrowGain: String? = "",
    var few: Boolean = false,
    var uniqueLemonStarting: Boolean = false,
    var exhibitAdolescent: Boolean = false,
    var shrugMode: String? = "",
    var exactDemonstrationLeather: String? = "",
    var dataUrl: String? = "",
    var field: Boolean = false,
    var type: Int = 0,
    var live: Boolean = false,
)
