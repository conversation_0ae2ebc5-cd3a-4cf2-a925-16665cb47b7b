package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class PopPhysicalAbstractEntity(
    var atmosphere: String? = "",
    var publicationFromCapable: Int = 0,
    val pictureUrl: String? = "",
    var configuration: String? = "",
    var produceThroat: String? = "",
    var unsignedTelephone: Int = 0,
    var lose: String? = "",
    val pictureType: Int = 0,
    var feelingPause: Boolean = false,
    var cowEnjoyCluster: Int = 0,
    var dramaStructureAssist: String? = "",
    var muchFather: String? = "",
    var tonight: String? = "",
    var structureAssignedSpeak: Int = 0,
    val type: Int? = 0,
    var exceptionOne: Boolean = false,
    var course: Boolean = false,
    var periodAllEventually: String? = "",
)