package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class SituationFatherEntity(
    var teachPassionLanguage: String? = "",
    var middleAnim: Boolean = false,
    var championParticipantWhite: Boolean = false,
    var sensitiveDeclarationEye: Int = 0,
    var whisperConstitute: Boolean = false,
    var readily: Int = 0,
    var emphasisConsistentCatholic: Boolean = false,
    var enableAdjustmentRegime: Boolean = false,
    var during: Boolean = false,
    var matchingSugarVision: Int = 0,
    var convictionCheekCable: String? = "",
    var year: String? = "",
    var tale: Boolean = false,
    var certainty: Boolean = false,
    var contactVos: MutableList<AbleRingLeanEntity>? = mutableListOf(),
    var help: Boolean = false,
    var neitherFrom: String? = "",
)
