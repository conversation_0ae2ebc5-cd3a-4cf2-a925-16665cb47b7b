package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class StarEagerEntity(
    var unusualSickNeed: Int = 0,
    var againstTension: Int = 0,
    var priestPeace: String? = "",
    var interventionSectorFormed: String? = "",
    var disappearNeed: String? = "",
    var outcome: String? = "",
    var alphabetInsertBesides: Boolean = false,
    var psychologist: String? = "",
    var hungryTalentCelebrate: Boolean = false,
    var borrowLinkerLimited: Int = 0,
    var briefPopPressing: Boolean = false,
    var aheadPrivateConflict: Boolean = false,
    var memoConviction: String? = "",
    var fitFocusFull: Boolean = false,
    var washTurn: String? = "",
    var pay: Boolean = false,
    var bathroomTickStudio: String? = "",
    var borrowPurpose: String? = "",
    var investigationAppropriatelyPrivacy: Boolean = false,
    var tradeNo: String? = "",
    var workshopWorkshop: String? = "",
)
