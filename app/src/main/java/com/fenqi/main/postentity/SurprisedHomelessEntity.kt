package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class SurprisedHomelessEntity(
    var indicatorFewOrder: Int = 0,
    var followFootball: Boolean = false,
    var orderType: Int = 0,
    var forwardCivilian: Int = 0,
    var itemSupposed: String? = "",
    var bagTaskOperating: String? = "",
    var unsignedAlterHusband: Int = 0,
    var institutionalCritic: Int = 0,
    var nullLikeAt: Boolean = false,
    var modificationDifferentiateHandling: String? = "",
    var issue: String? = "",
    var significanceProsecutorHmm: String? = "",
    var benchTechnicalConventional: String? = "",
    var sharpBlue: Int = 0,
    var althoughTerminatingComfortable: String? = "",
    var backupRoutineArgument: Int = 0,
    var humorSoftwareReact: Boolean = false,
    var transportableFifteen: Boolean = false,
    var tired: Boolean = false,
    var departmentDager: Boolean = false,
)
