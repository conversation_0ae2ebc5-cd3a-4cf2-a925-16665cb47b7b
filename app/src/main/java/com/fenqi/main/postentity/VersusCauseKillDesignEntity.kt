package com.fenqi.main.postentity

import kotlinx.serialization.Serializable

@Serializable
data class VersusCauseKillDesignEntity(
    var what: Int = 0,
    var summit: String? = "",
    var mechanism: Int = 0,
    var watchEmphasize: Int = 0,
    var delay: Int = 0,
    var orderType: Int = 0,
    var silentDirt: Boolean = false,
    var flowId: String? = "",
    var identicallyModest: String? = "",
    var pcBad: Int = 0,
    var internationalTimeCurriculum: String? = "",
    var upper: String? = "",
    var responsibility: String? = "",
    var sadUs: String? = "",
    var satellite: Boolean = false,
    var fear: Int = 0,
    var actualMiracle: Int = 0,
    var seed: Int = 0,
    var factor: Boolean = false,
    var assureReachEasel: Boolean = false,
    var focusHttpCharge: Int = 0,
    var comfortableStroke: Int = 0,
)
