package com.fenqi.main.presenter.base

import android.app.Activity
import com.fenqi.request.HttpRequestCallBack

abstract class BasePresenter<A>: Presenter<MvvmView> {
  protected var api:A?=null
  protected var activity:Activity?=null

  protected abstract fun api():A

  protected var httpCallBack:HttpRequestCallBack?=null

  override fun attachApi() {
    api = api()
  }

  override fun attachActivity(dispatchActivity: Activity?) {
    activity = dispatchActivity
  }

  override fun attachHttpCallBack(callBack: HttpRequestCallBack) {
    this.httpCallBack = callBack
  }
}