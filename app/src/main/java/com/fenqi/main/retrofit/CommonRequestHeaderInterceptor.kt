package com.fenqi.main.retrofit

import android.content.Context
import android.os.Build
import android.text.TextUtils
import cn.hutool.crypto.asymmetric.KeyType
import com.fenqi.main.sharedata.SharePreferenceData
import com.fenqi.main.BuildConfig
import com.fenqi.main.AppUrlConfig
import com.fenqi.main.config.AppConfig
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.rsa.RSAUtil
import com.fenqi.main.util.NetworkUtil
import okhttp3.Interceptor
import okhttp3.MediaType
import okhttp3.Request
import okhttp3.Response
import okio.Buffer
import org.json.JSONException
import org.json.JSONObject
import java.lang.Exception
import java.lang.StringBuilder
import java.nio.charset.Charset
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.util.*

class CommonRequestHeaderInterceptor(var context: Context,val type:Int) : Interceptor {

    companion object {
        val UTF8: Charset = Charset.forName("UTF-8")
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val requestBody = request.body
        var body: String? = null
        if (requestBody != null) {
            val buffer = Buffer()
            requestBody.writeTo(buffer)
            var charset: Charset = UTF8
            val contentType: MediaType? = requestBody.contentType()
            if (contentType != null) {
                charset = contentType.charset(UTF8)!!
            }
            body = buffer.readString(charset)
        }
        val builder: Request.Builder = request.newBuilder()
        try {
            var basicParams = genBasicParams(body)

            if (type == CommonConstant.HEADER_TYPE_RSA){
                basicParams= RSAUtil.getInstance().encryptBase64(basicParams, KeyType.PublicKey)
            }
            builder.addHeader("requestHeader", basicParams)
            builder.addHeader("Accept-Language", getAcceptLanguage())
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        return chain.proceed(builder.build())
    }

    private fun genBasicParams(body: String?): String {
        val time = System.currentTimeMillis()
        val versionName: String = BuildConfig.VERSION_NAME
        val versionCode: Int = BuildConfig.VERSION_CODE
        val imei: String = UUID.randomUUID().toString()
        var token = SharePreferenceData.getToken()
        if(TextUtils.isEmpty(token)){
            token = ""
        }

        var netType = "unknown"
        try {
            netType = NetworkUtil.getInstance().acquireHttpNetTypeConnectedType(context)
        } catch (ex:Exception){
            ex.printStackTrace()
        }

        val appClient = "android"
        val appKey = if(BuildConfig.DEBUG) AppConfig.API_KEY_DEBUG else AppConfig.API_KEY_RELEASE

        val channel = "${AppUrlConfig.APP_NAME}_${BuildConfig.VERSION_CODE}"
        val openId = UUID.randomUUID().toString()

        var guestId = SharePreferenceData.getGuestId()
        if(guestId == null){
            guestId = ""
        }
        val origin = SharePreferenceData.getUserOrigin()

        val basicJson = JSONObject()
        basicJson.put("appKey", appKey)
            .put("appVersion", versionName)
            .put("channel", channel)
            .put("appClient", appClient)
            .put("openId", openId)
            .put("clientId", "")
            .put("timestamp", time)
            .put("net", netType)
            .put("versionCode", versionCode)
            .put("token", token)
            .put("guestId", guestId)

        if(TextUtils.isEmpty(token)){
            basicJson.put("horse", origin)
        }
        if(type == CommonConstant.HEADER_TYPE_RSA){
            basicJson.put("cryptoType","RSA")
        }


        val signSB = StringBuilder()
        signSB.append(appKey)
        signSB.append(versionName)
        signSB.append(appClient)
        signSB.append(versionCode)
        signSB.append(time)
        signSB.append(guestId)
        signSB.append(token)

        if(!TextUtils.isEmpty(body)){
            signSB.append(body)
        }

        signSB.append(if(BuildConfig.DEBUG) AppConfig.APP_SECRET_DEBUG else AppConfig.APP_SECRET_RELEASE)

        val appSign: String = md5(md5(signSB.toString()))

        basicJson.put("appSign", appSign)
        return basicJson.toString()
    }

    private fun getAcceptLanguage(): String {
        try {
            var locale: Locale? = null
            locale = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                context.resources.configuration.locales.get(0)
            } else {
                context.resources.configuration.locale
            }
            val sb = StringBuilder()
            if (locale.country.isNotBlank()) {
                sb.append(locale.language).append("-").append(locale.country).append(",")
            }
            sb.append(locale.language).append(";q=0.8")
            return sb.toString()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return "en-US,en;q=0.8"
    }

    private fun md5(plainText: String): String {
        return try {
            val md = MessageDigest.getInstance("MD5")
            md.update(plainText.toByteArray())
            val b = md.digest()
            val buf = StringBuffer("")
            for (offset in b.indices) {
                var i = b[offset].toInt()
                if (i < 0) {
                    i += 256
                }
                if (i < 16) {
                    buf.append("0")
                }
                buf.append(Integer.toHexString(i))
            }
            buf.toString()
        } catch (exception: NoSuchAlgorithmException) {
            exception.printStackTrace()
            ""
        }
    }

}