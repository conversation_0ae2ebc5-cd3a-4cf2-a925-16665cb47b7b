package com.fenqi.main.retrofit

typealias OnSuccessCallback= (data: String?) -> Unit
typealias OnFailureCallback = (msg:String,code:Int) -> Unit
typealias OnForceLogoutCallback = () -> Unit
typealias OnSuccessCallbackNoData = () -> Unit

class HttpRequestCallback {
    var successCallback: OnSuccessCallback? = null
    var successCallbackNoData: OnSuccessCallbackNoData? = null
    var failureCallback: OnFailureCallback? = null
    var forceLogoutCallback: OnForceLogoutCallback?=null

    fun onSuccess(block: OnSuccessCallback) {
        successCallback = block
    }

    fun onSuccessNoData(block: OnSuccessCallbackNoData){
        successCallbackNoData = block
    }

    fun onFailure(block: OnFailureCallback) {
        failureCallback = block
    }

    fun onForceLogout(block: OnForceLogoutCallback){
        forceLogoutCallback = block
    }
}