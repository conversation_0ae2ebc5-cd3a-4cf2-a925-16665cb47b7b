package com.fenqi.main.retrofit

import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.widget.Toast
import cn.hutool.crypto.asymmetric.KeyType
import com.fenqi.main.AppUrlConfig
import com.fenqi.main.BuildConfig
import com.fenqi.main.MainApplication
import com.fenqi.main.R
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.page.login.LoginActivity
import com.fenqi.main.rsa.RSAUtil
import com.fenqi.main.sharedata.SharePreferenceData
import com.fenqi.main.util.LogUtil
import com.fenqi.platformtools.utils.ActivityManager
import com.fenqi.request.ResponseBean
import com.google.gson.Gson
import com.google.gson.JsonElement
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.json.Json
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.scalars.ScalarsConverterFactory
import java.net.URLDecoder
import java.util.concurrent.TimeUnit
import kotlin.coroutines.CoroutineContext

public class RetrofitBuilder private constructor() {

    companion object {

        const val MAX_TIME = 30L

        fun getInstance(): RetrofitBuilder {
            return RetrofitBuilderHelper.instance
        }
    }

    private object RetrofitBuilderHelper {
        val instance = RetrofitBuilder()
    }

    private val sDispatcher = Dispatcher()
    private val sConnectionPool = ConnectionPool()

    inline fun <reified T> createApi(context: Context): T {
//        var api = SharePreferenceData.getApiUrl()
//        if(TextUtils.isEmpty(api)){
//            val url = if (BuildConfig.DEBUG) AppUrlConfig.API_URL_DEBUG else AppUrlConfig.API_URL_RELEASE
//            api = url.split(";")[0]
//        }

        return Retrofit.Builder()
            .baseUrl(if(BuildConfig.DEBUG) AppUrlConfig.API_URL_DEBUG else AppUrlConfig.API_URL_RELEASE)
            .client(getRetrofitClient(context, mutableListOf()))
            .addConverterFactory(ScalarsConverterFactory.create())
            .build()
            .create(T::class.java)
    }

    fun <T> getRequestBody(body: T): RequestBody {
        var requestBody = Gson().toJson(body)
        try {
            if(SharePreferenceData.getIsRSAOpen() == true){
                requestBody = RSAUtil.getInstance().encryptBase64(requestBody,KeyType.PublicKey)
            }
        } catch (ex:Exception){
            ex.printStackTrace()
        }
        return requestBody.toRequestBody("application/json;charset=utf-8".toMediaType())
    }

    fun getRetrofitClient(context: Context, interceptors: MutableList<Interceptor>): OkHttpClient {
        val builder = OkHttpClient().newBuilder()

        val logger = HttpLoggingInterceptor()
        logger.level = HttpLoggingInterceptor.Level.BODY

        if (LogUtil.isLog()) {
            builder.addInterceptor(logger)
        }

        try {
            if(SharePreferenceData.getIsRSAOpen() == true){
                interceptors.add(CommonRequestHeaderInterceptor(context, CommonConstant.HEADER_TYPE_RSA))
            } else {
                interceptors.add(CommonRequestHeaderInterceptor(context,CommonConstant.HEADER_TYPE_NORMAL))
            }
        } catch (exception:Exception){
            exception.printStackTrace()
            interceptors.add(CommonRequestHeaderInterceptor(context,CommonConstant.HEADER_TYPE_NORMAL))
        }

        for (item in interceptors) {
            builder.addInterceptor(item)

            if (item is RetryInterceptor) {
                builder.retryOnConnectionFailure(true)
            }
        }

        return builder.connectTimeout(MAX_TIME, TimeUnit.SECONDS)
            .readTimeout(MAX_TIME, TimeUnit.SECONDS)
            .writeTimeout(MAX_TIME, TimeUnit.SECONDS)
            .dispatcher(sDispatcher)
            .connectionPool(sConnectionPool)
            .build()
    }


    private fun httpGo(
        request: suspend () -> String?
    ): Flow<String> = flow {
        runCatching {
            request()
        }.onSuccess {
            if (it == null) {
                emit("")
            } else {
                emit(it)
            }
        }.onFailure {
            if (it is CancellationException) {
                throw it
            } else {
                if (it.message.toString().contains("400")) {
                    emit("")
                } else {
                    emit("")
                }
            }
        }
    }

    fun start(
        callback: HttpRequestCallback.() -> Unit,
        request: suspend () -> String?
    ) = CoroutineScope(Dispatchers.IO).launch {
        httpGo { request() }.collect {
            responseLogic(Dispatchers.Main, callback, it);
        }
    }

    private fun responseLogic(
        observerContext: CoroutineContext = Dispatchers.Main,
        callback: HttpRequestCallback.() -> Unit,
        value: String,
    ) {
        CoroutineScope(observerContext).launch {
            val requestCallback = HttpRequestCallback().apply(callback)
            var decryptStr = value
            if(TextUtils.isEmpty(decryptStr)){
                requestCallback.failureCallback?.invoke(MainApplication.getInstance()?.getString(R.string.reset_network_tip)
                    .toString(), 555)
            } else {
                if(!value.startsWith("{")){
                    decryptStr = RSAUtil.getInstance().decryptStr(value, KeyType.PrivateKey)
                    decryptStr = URLDecoder.decode(decryptStr, "UTF-8")
                }

                val jsonElement: JsonElement = Gson().fromJson(
                    decryptStr,
                    JsonElement::class.java
                )

                val json = Json {
                    ignoreUnknownKeys = true
                }
                val responseBean:ResponseBean = json.decodeFromString(decryptStr)

                when (responseBean.code) {
                    RequestCode.SUCCESS -> {
                        try {
                            val jsonObject = jsonElement.asJsonObject
                            if(jsonObject.has("data")){
                                val dataString = Gson().toJson(jsonObject.get("data").asJsonObject)
                                LogUtil.log("dataString-->${dataString}")
                                requestCallback.successCallback?.invoke(dataString)
                            }else {
                                requestCallback.successCallback?.invoke("{}")
                            }
                        } catch (exception:Exception){
                            exception.printStackTrace()
                            LogUtil.log("exception--${exception.message}")
                            requestCallback.successCallback?.invoke("{}")
                        }
                    }
                    RequestCode.FORCE_LOGOUT -> {
                        forceSignOut()
                    }
                    else -> {
                        requestCallback.failureCallback?.invoke(responseBean.msg.toString(), responseBean.code)
                    }
                }
            }
        }
    }

    private fun forceSignOut() {
        CoroutineScope(Dispatchers.Main).launch {
            SharePreferenceData.setToken("")
            SharePreferenceData.setIsRc(false)
            Toast.makeText(
                MainApplication.getInstance()?.applicationContext,
                MainApplication.getInstance()?.getString(R.string.toast_force_logout),
                Toast.LENGTH_SHORT
            ).show()
            val intent =
                LoginActivity.callIntent(MainApplication.getInstance()?.applicationContext!!)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            MainApplication.getInstance()?.applicationContext?.startActivity(intent)

            ActivityManager.getActivityManager().destroyAllActivity()
        }
    }
}