package com.fenqi.main.retrofit

import okhttp3.Interceptor
import okhttp3.Response

public class RetryInterceptor:Interceptor {

    companion object{
        const val RETRY_MAX = 3
    }

    private var retryNumber = 0

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        var response = chain.proceed(request)

        while (!response.isSuccessful && retryNumber< RETRY_MAX){
            retryNumber++
            response = chain.proceed(request)
        }

        return response
    }
}