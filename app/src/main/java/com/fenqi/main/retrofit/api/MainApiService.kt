package com.fenqi.main.retrofit.api

import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.http.*

interface MainApiService {

    @POST("8080a3724d804a508e434d9cabac5f34")
    suspend fun verifyCodeSend(@Body body: RequestBody): String

    @GET("536f139b5cdd48b590f11d7d5c773334/{key}")
    suspend fun userProtocolInfos(@Path("key") key:String): String

    @POST("baa2d25b8f9440a6a6c6e412dd34bdc4")
    suspend fun userLogin(@Body body: RequestBody): String

    @POST("2de4944003b74edf9af0f69371d6592b")
    suspend fun userDeviceSave(@Body body: RequestBody): String

    @POST("c17a811b94e34c3a8c55ea40678627f3")
    suspend fun userHome(): String

    @GET("c48e6fbbe03a424b84a66341348f86df")
    suspend fun userPersonalCenter(): String

    @GET("ae611976f2fb4dcfa9d4caf6aeabdf1a")
    suspend fun authActionList(): String

    @GET("6106c54c02534e7bac68261a9f57c911")
    suspend fun userAuthInfo(): String

    @POST("cc8f26763fc44526a7f822d86ed13dda")
    suspend fun userAuthBaseSave(@Body body: RequestBody): String

    @GET("c93170b5658e46eb8a9ff72711c34093")
    suspend fun userAuthContactInfo(): String

    @POST("88f42b2cc10a40cab5fb3df7ecf94856")
    suspend fun userAuthContactSave(@Body body: RequestBody): String

    @GET("80f15b88c3bb4b8b82a0a93321f650be/authCode")
    suspend fun userAuthProfileInfoGet(): String

    @POST("48c4a0dc25a7426db7d85c029948d64b")
    suspend fun userAuthBankSave(@Body body: RequestBody): String

    @GET("fa834a7dc9a34c40a4a381db0f4ec9d2/{fileType}")
    suspend fun fileTokenGet(@Path(value = "fileType") fileType:Int): String

    @GET("ef2a719f95e74177ace9018da4ec3876")
    suspend fun userSignOut(): String

    @GET("65b18ad08beb48e9909c730549ba3fdb")
    suspend fun orderSubmitConfigGet(): String

    @POST("cd2b16e93baa4081b864cad48d8a41aa")
    suspend fun orderSubmitConfirm(@Body body: RequestBody): String

    @POST("a9120a8aba4d4336b911227564fa9f97")
    suspend fun userGpsSave(@Body body: RequestBody): String

    @POST("872fe7dc5b5b4ac3a50278ce2ea24136")
    suspend fun configAppCodeGet(): String

    @POST("72c246716cea499b98bd1f31ddc009a4")
    suspend fun userWifiInfoUrlSave(@Body body: RequestBody): String
    @POST("72c246716cea499b98bd1f31ddc009a4")
    suspend fun userAppInfoListUrlSave(@Body body: RequestBody): String
    @POST("72c246716cea499b98bd1f31ddc009a4")
    suspend fun userSmsUrlSave(@Body body: RequestBody): String
    @POST("72c246716cea499b98bd1f31ddc009a4")
    suspend fun userContactListUrlSave(@Body body: RequestBody): String

    @POST("010b49a7600449ccb0df05f282fb21c9")
    suspend fun userContactListSave(@Body body: RequestBody): String

    @POST("e6a337c788a34ee39c069097fd4f3d81")
    suspend fun payClientVoucherUpload(@Body body: RequestBody): String

    @POST("f30cea6f25824759b6c682fd6a784f01")
    suspend fun orderRollOverInfoGet(): String

    @Multipart
    @POST("bea006e60c45405088e785be9525b5f6")
    suspend fun fileUpload(@Part file:MultipartBody.Part): String

    @Multipart
    @PUT("{url}")
    suspend fun huaweiFileUpload(@Part file:MultipartBody.Part,@Path(value = "url", encoded = true) url:String): String

    @POST("{url}")
    suspend fun facebookEvent(@Path(value = "url", encoded = true) url:String): String

    @GET("44de97b318074ff79425c383f27cca4f")
    suspend fun userAuthFaceConfigGet(): String

    @POST("0f31457f82524255bfae8ae84687d716")
    suspend fun userAuthFaceIdInfoSubmit(@Body body: RequestBody): String

    @POST("b121c63e6c574ce1abd69201d83096d6")
    suspend fun userAuthFaceOcrUrlSubmit(@Body body: RequestBody): String

    @GET("5d567ab220f04ec893f963088a3f7c86")
    suspend fun userCustomerServiceGet(): String

    @POST("234ce815eaf849e5a8e3a697c2fba3dc")
    suspend fun userAuthFaceLiveSubmit(@Body body: RequestBody): String

    @POST("ffbea4191f6e4a0db382b292cccf38cf")
    suspend fun payCardBin(@Body body: RequestBody): String

    @POST("385a71eb89f84c3093c73d81c18db7b5")
    suspend fun payBindCardRequest(@Body body: RequestBody): String

    @POST("af5ef4737d9a469ba86a778314abce5f")
    suspend fun payBindCardResendSms(@Body body: RequestBody): String

    @GET("2b7be5a46bad4f08a93dd635631f7828")
    suspend fun supportBankList(): String

    @POST("14583af626434bf7b5628235ed282e72")
    suspend fun payBindCardConfirm(@Body body: RequestBody): String

    @GET("d90057463da140068619ac898222b955/{orderType}")
    suspend fun bindCardListGet(@Path("orderType") orderType:String): String

    @GET("61340c820fcd4d658616ab1a73253890")
    suspend fun orderBillRepaymentList(): String

    @GET("a64c0982541f4c47bb28a025a3bfd74e")
    suspend fun orderBillPaidList(): String

    @GET("e8640c3359be40d8af87cd1dc64c9505")
    suspend fun orderBillDetail(@Query("tradeNo") tradeNo:String): String

    @GET("78a0d952c9ca40d18c113c8261eabd21")
    suspend fun orderConfirmDetail(@Query("tradeNo") tradeNo:String): String

    @POST("a1a953a16c7c492ea2af8a9b829ee908")
    suspend fun orderAuthConfirmRequest(@Body body: RequestBody): String

    @GET("47233a0cd33d40d08c732eb3f23c0c1a")
    suspend fun getContactUrl(@Query("tradeNo") tradeNo:String): String

    @POST("b8803d5b97cc444fba7cb09a235b9621")
    suspend fun payClientCharges(@Body body: RequestBody): String

    @GET("f0227a217f7c4223b4670c1ac1113047")
    suspend fun orderBorrowConfirm(): String

    @GET("029239205cd04f4b82dc09c7f291fde5/{key}")
    suspend fun cashierInfo(@Path(value = "key") key:String): String

    @POST("e6e47205e436470b917283476242a850/{key}")
    suspend fun cashierDoPay(@Path(value = "key") key:String,@Body body: RequestBody): String

    @GET("8d4295c8a823401dbfdd2e092aa0015e/{key}")
    suspend fun cashierStatus(@Path(value = "key") key:String): String

}
