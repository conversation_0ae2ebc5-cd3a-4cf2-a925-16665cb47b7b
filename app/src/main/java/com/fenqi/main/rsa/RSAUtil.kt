package com.fenqi.main.rsa

import cn.hutool.crypto.SecureUtil
import cn.hutool.crypto.asymmetric.RSA

class RSAUtil {

    companion object {
        fun getInstance(): RSA {
            return RSAUtilBuilderHelper.instance
        }
    }

    private object RSAUtilBuilderHelper {
        const val publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCgzWUFJf0tW3qOZxrFbvaalTW0xcp6T0MMfxKhGNrARvfEYaJD+yr4OELP6EPltemqzN2zI7UY9AzArYdfyRUPXcLAOXwLLngogkvMpKREW/BFbD7wI6vbOpK+6HNv589EfDcKxlSrAZqWMleufCuwl3q7AAUVGqoUYHvuZHwuVwIDAQAB"
        const val privateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKDNZQUl/S1beo5nGsVu9pqVNbTFynpPQwx/EqEY2sBG98RhokP7Kvg4Qs/oQ+W16arM3bMjtRj0DMCth1/JFQ9dwsA5fAsueCiCS8ykpERb8EVsPvAjq9s6kr7oc2/nz0R8NwrGVKsBmpYyV658K7CXersABRUaqhRge+5kfC5XAgMBAAECgYBEB2ErkmzP2Zm50SjkShOORn3YIq2MnSfSi5gIC4nQOrXroRqTBBmjtbmeV7cR4aofllhcx0iAbA9RHJjfDyUXFHmvbKdnHRgoeXLF1ff578sjfh0ExFKnokRqH8NEfBBV404vMJpyisUgXCnEyH1PvVapPe9JHa4zAnvTSvEwAQJBAOH7stuwAgAbCCCJCTv9ujNAt7Di4SVMaNGBIpMVy1LdAWytBdunkc/qSXoSLcsEyFq+CMi0/5QKFJbW/15FvAECQQC2KUtkWE8XI8YVr6OCq+VUbDtN/yoSTa7AC5GMJI+Xd/pOaqidP/OHomvEt3f97zqSsdBnNbkJgLqpW3U2cUpXAkEAv1UcWmTrTKuWdfWQm/p3bG2fGWT+u1W2aausWlxZig8U5a6ZByEZk7AKBhDeNMYX3LyJM2YL/ouKYywliuwAAQJAYpV+o9PXGeLWdS4VA8cb2dCpV9DcaAN6q5yXLI0s2QCpin7WuiO+HI2eXVwdqGQsAvAQpYrBlY8Bdl501P4DCQJBALNBZvJwpmUhT7J9YejTAK4DofEyVj6qEYBSf7/2Kme1oY5pnBOHqLoRPCs2C2opFap+exOYRtXJqZcYyZhOj/I="

        val instance: RSA = SecureUtil.rsa(privateKey, publicKey)
    }


}