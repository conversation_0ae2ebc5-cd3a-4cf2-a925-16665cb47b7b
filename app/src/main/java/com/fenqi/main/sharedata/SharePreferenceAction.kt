package com.fenqi.main.sharedata

import android.content.Context
import android.content.SharedPreferences
import com.fenqi.main.MainApplication

class SharePreferenceAction() {

  companion object {
    fun getInstance(): SharePreferenceAction {
      return SharePreferenceActionHelper.instance
    }
  }

  private object SharePreferenceActionHelper {
    val instance = SharePreferenceAction();
  }

  private val SHARED_NAME = "BEEFPORK"

  private var sharedPreferences:SharedPreferences? = null
  private var editor:SharedPreferences.Editor? = null

  init {
    sharedPreferences = MainApplication.getInstance()?.applicationContext?.getSharedPreferences(SHARED_NAME,Context.MODE_PRIVATE)!!
    editor = sharedPreferences?.edit()
  }

  fun putBoolean(key:String,value:Boolean):SharedPreferences.Editor?{
    return editor?.putBoolean(key,value)
  }

  fun putInt(key: String,value: Int):SharedPreferences.Editor?{
    return editor?.putInt(key,value)
  }

  fun putString(key: String,value:String):SharedPreferences.Editor?{
    return editor?.putString(key,value)
  }

  fun getShareBoolean(key: String):Boolean?{
    return sharedPreferences?.getBoolean(key,false)
  }

  fun getShareBoolean(key: String,value:Boolean):Boolean?{
    return sharedPreferences?.getBoolean(key,value)
  }

  fun getShareInt(key: String):Int?{
    return sharedPreferences?.getInt(key,0)
  }

  fun getShareString(key: String): String? {
    return sharedPreferences?.getString(key,"")
  }
}