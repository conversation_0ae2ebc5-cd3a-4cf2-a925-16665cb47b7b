package com.fenqi.main.sharedata

object SharePreferenceData {

  private const val TOKEN = "TOKEN"
  private const val API_URL = "API_URL"
  private const val IS_FIRST_OPEN = "IS_FIRST_OPEN"
  private const val GUEST_ID = "GUEST_ID"
  private const val USER_ORIGIN = "USER_ORIGIN"
  private const val PHONE_NUMBER = "PHONE_NUMBER"
  private const val FB_ID = "FB_ID"
  private const val FB_TOKEN = "FB_TOKEN"
  private const val FB_URL = "FB_URL"
  private const val FILE_UPLOAD_TYPE = "FILE_UPLOAD_TYPE"
  private const val IS_RC = "IS_RC"
  private const val IS_RSA_OPEN = "IS_RSA_OPEN"

  fun setHasAgree(agree:Boolean,type:String){
    SharePreferenceAction.getInstance().putBoolean(type,agree)?.apply()
  }

  fun getHasAgree(type:String):Boolean?{
    return SharePreferenceAction.getInstance().getShareBoolean(type)
  }

  fun setToken(token:String){
    SharePreferenceAction.getInstance().putString(TOKEN,token)?.apply()
  }

  fun getToken():String?{
    return SharePreferenceAction.getInstance().getShareString(TOKEN)
  }
//
//  fun setApiUrl(token:String){
//    SharePreferenceAction.getInstance().putString(API_URL,token)?.apply()
//  }
//
//  fun getApiUrl():String?{
//    return SharePreferenceAction.getInstance().getShareString(API_URL)
//  }

  fun setIsRSAOpen(value:Boolean){
    SharePreferenceAction.getInstance().putBoolean(IS_RSA_OPEN,value)?.apply()
  }

  fun getIsRSAOpen():Boolean?{
    return SharePreferenceAction.getInstance().getShareBoolean(IS_RSA_OPEN,false)
  }

  fun setIsFistOpen(value:Boolean){
    SharePreferenceAction.getInstance().putBoolean(IS_FIRST_OPEN,value)?.apply()
  }

  fun getIsFistOpen():Boolean?{
    return SharePreferenceAction.getInstance().getShareBoolean(IS_FIRST_OPEN,true)
  }

  fun setIsRc(value:Boolean){
    SharePreferenceAction.getInstance().putBoolean(IS_RC,value)?.apply()
  }

  fun getIsRc():Boolean?{
    return SharePreferenceAction.getInstance().getShareBoolean(IS_RC,false)
  }

  fun setApiUrl(apiUrl:String,tag:String){
    SharePreferenceAction.getInstance().putString(tag,apiUrl)?.apply()
  }

  fun getApiUrl(tag:String):String?{
    return SharePreferenceAction.getInstance().getShareString(tag)
  }


  fun setGuestId(guestId:String){
    SharePreferenceAction.getInstance().putString(GUEST_ID,guestId)?.apply()
  }

  fun getGuestId():String?{
    return SharePreferenceAction.getInstance().getShareString(GUEST_ID)
  }

  fun setUserOrigin(userOrigin:String){
    SharePreferenceAction.getInstance().putString(USER_ORIGIN,userOrigin)?.apply()
  }

  fun getUserOrigin():String?{
    return SharePreferenceAction.getInstance().getShareString(USER_ORIGIN)
  }

  fun setPhoneNumber(value:String){
      SharePreferenceAction.getInstance().putString(PHONE_NUMBER,value)?.apply()
  }

  fun getPhoneNumber():String?{
      return SharePreferenceAction.getInstance().getShareString(PHONE_NUMBER)
  }

  fun setFacebookId(value:String){
      SharePreferenceAction.getInstance().putString(FB_ID,value)?.apply()
  }

  fun getFacebookId():String?{
      return SharePreferenceAction.getInstance().getShareString(FB_ID)
  }

  fun setFacebookToken(value:String){
    SharePreferenceAction.getInstance().putString(FB_TOKEN,value)?.apply()
  }

  fun getFacebookToken():String?{
    return SharePreferenceAction.getInstance().getShareString(FB_TOKEN)
  }

  fun setFacebookUrl(value:String){
    SharePreferenceAction.getInstance().putString(FB_URL,value)?.apply()
  }

  fun getFacebookUrl():String?{
    return SharePreferenceAction.getInstance().getShareString(FB_URL)
  }

  fun setFileUploadType(value:Int){
      SharePreferenceAction.getInstance().putInt(FILE_UPLOAD_TYPE,value)?.apply()
  }

  fun getFileUploadType():Int?{
      return SharePreferenceAction.getInstance().getShareInt(FILE_UPLOAD_TYPE)
  }

  fun setHasReadFiveStar(value:Boolean,key:String){
    SharePreferenceAction.getInstance().putBoolean(key,value)?.apply()
  }

  fun getHasReadFiveStar(key:String):Boolean?{
    return SharePreferenceAction.getInstance().getShareBoolean(key,false)
  }
}