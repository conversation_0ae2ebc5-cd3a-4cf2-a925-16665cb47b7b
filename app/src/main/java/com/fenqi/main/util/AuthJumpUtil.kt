package com.fenqi.main.util

import android.content.Context
import com.fenqi.main.AppUrlConfig
import com.fenqi.main.BuildConfig
import com.fenqi.main.bean.AuthInfoListResponseEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.page.main.MainJumpUtil
import com.fenqi.main.retrofit.CommonRequestHeaderInterceptor
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.RetryInterceptor
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.sharedata.SharePreferenceData
import kotlinx.serialization.json.Json
import okhttp3.ConnectionPool
import okhttp3.Dispatcher
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.scalars.ScalarsConverterFactory
import java.util.concurrent.TimeUnit

class AuthJumpUtil {

    companion object {
        fun getInstance(): AuthJumpUtil {
            return AuthJumpUtilHelper.instance
        }
    }

    private object AuthJumpUtilHelper {
        val instance = AuthJumpUtil()
    }

    fun judgeJump(context: Context):AuthJumpUtil{
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val json = Json {
                        ignoreUnknownKeys = true
                    }
                    val data: AuthInfoListResponseEntity = json.decodeFromString(it)
                    LogUtil.log("data----jumpurl-->${data}")
                    MainJumpUtil.getInstance().authListJump(context,data.nextJumpUrl.toString())
                    finishedCallBack.invoke()
                }

            }
            onFailure { _, _ ->
                onFailed.invoke()
            }
        }) {
            RetrofitBuilder.getInstance()
                .createApi<MainApiService>(context)
                .authActionList()
        }
        return this
    }

    fun createApi(context: Context): MainApiService {
        return Retrofit.Builder()
            .baseUrl(if(BuildConfig.DEBUG) AppUrlConfig.API_URL_DEBUG else AppUrlConfig.API_URL_RELEASE)
            .client(getRetrofitClient(context, mutableListOf()))
            .addConverterFactory(ScalarsConverterFactory.create())
            .build()
            .create(MainApiService::class.java)
    }

    private fun getRetrofitClient(
        context: Context,
        interceptors: MutableList<Interceptor>
    ): OkHttpClient {
        val builder = OkHttpClient().newBuilder()

        val logger = HttpLoggingInterceptor()
        logger.level = HttpLoggingInterceptor.Level.BODY

        if (LogUtil.isLog()) {
            builder.addInterceptor(logger)
        }

        try {
            if(SharePreferenceData.getIsRSAOpen() == true){
                interceptors.add(CommonRequestHeaderInterceptor(context,CommonConstant.HEADER_TYPE_RSA))
            } else {
                interceptors.add(CommonRequestHeaderInterceptor(context,CommonConstant.HEADER_TYPE_NORMAL))
            }
        } catch (exception:Exception){
            exception.printStackTrace()
            interceptors.add(CommonRequestHeaderInterceptor(context,CommonConstant.HEADER_TYPE_NORMAL))
        }

        for (item in interceptors) {
            builder.addInterceptor(item)

            if (item is RetryInterceptor) {
                builder.retryOnConnectionFailure(true)
            }
        }

        return builder.connectTimeout(RetrofitBuilder.MAX_TIME, TimeUnit.SECONDS)
            .readTimeout(RetrofitBuilder.MAX_TIME, TimeUnit.SECONDS)
            .writeTimeout(RetrofitBuilder.MAX_TIME, TimeUnit.SECONDS)
            .dispatcher(Dispatcher())
            .connectionPool(ConnectionPool())
            .build()
    }

    private lateinit var finishedCallBack: () -> Unit

    fun setFinishedCallBack(finishedCallBack: () -> Unit): AuthJumpUtil {
        this.finishedCallBack = finishedCallBack
        return this
    }

    private lateinit var onFailed: () -> Unit

    fun setOnFailed(onFailed: () -> Unit): AuthJumpUtil {
        this.onFailed = onFailed
        return this
    }
}