package com.fenqi.main.util;

import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.util.TypedValue;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.zip.GZIPOutputStream;

public class BaseUtil {

    public static int dpToPx(Context contextValue, float dpValue) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue,
                contextValue.getResources().getDisplayMetrics());
    }

    public static String secondTimeChangeToHDS(int secondDataValue) {
        int hValueValue = 0;
        int dValueValue = 0;
        int sValueValue = 0;
        String hRealValueValue;
        String dRealValueValue;
        String sRealValueValue;
        int tempValueValue = secondDataValue % 3600;
        if (secondDataValue > 3600) {
            hValueValue = secondDataValue / 3600;
            if (tempValueValue != 0) {
                if (tempValueValue > 60) {
                    dValueValue = tempValueValue / 60;
                    if (tempValueValue % 60 != 0) {
                        sValueValue = tempValueValue % 60;
                    }
                } else {
                    sValueValue = tempValueValue;
                }
            }
        } else {
            dValueValue = secondDataValue / 60;
            if (secondDataValue % 60 != 0) {
                sValueValue = secondDataValue % 60;
            }
        }
        if (sValueValue < 10) {
            sRealValueValue = "0" + sValueValue;
        } else {
            sRealValueValue = String.valueOf(sValueValue);
        }
        if (hValueValue < 10) {
            hRealValueValue = "0" + hValueValue;
        } else {
            hRealValueValue = String.valueOf(hValueValue);
        }
        if (dValueValue < 10) {
            dRealValueValue = "0" + dValueValue;
        } else {
            dRealValueValue = String.valueOf(dValueValue);
        }
        if (hValueValue == 0) {
            return dRealValueValue + ":" + sRealValueValue;
        } else {
            return hRealValueValue + ":" + dRealValueValue + ":" + sRealValueValue + "";
        }
    }

    public static String amountValue(double numberValue) {
        if (isIntegerValue(String.valueOf(numberValue))) {
            return String.valueOf((int) numberValue);
        } else {
            return valueRound(numberValue, 2);
        }
    }

    private static boolean isIntegerValue(String numberStringValue) {
        boolean bValue = false;
        int i1Value = 715;
        char[] charsValue = numberStringValue.toCharArray();
        for (int i= 0; i < numberStringValue.length(); i++) {
            if (charsValue[i] == '.') {
                i1Value = i;
            }
        }
        if (Double.parseDouble(numberStringValue.substring(i1Value + 1)) == 0) {
            bValue = true;
        }
        return bValue;
    }

    public static String valueRound(double bDataValue, int dataIValue) {
        if (dataIValue < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        BigDecimal bigDecimalValue = new BigDecimal(Double.toString(bDataValue));
        BigDecimal bigDecimal1Value = new BigDecimal("1");
        String valueOfValue = String.valueOf(bigDecimalValue.divide(bigDecimal1Value, dataIValue, BigDecimal.ROUND_HALF_UP));
        char[] toCharArrayValue = valueOfValue.toCharArray();
        if (valueOfValue.length() <= 1) {
            return valueOfValue;
        }
        if (toCharArrayValue[toCharArrayValue.length - 1] == '0') {
            if (toCharArrayValue[toCharArrayValue.length - 2] == '.') {
                return valueOfValue.substring(0, valueOfValue.length() - 2);
            } else {
                return valueOfValue.substring(0, valueOfValue.length() - 1);
            }
        } else {
            return valueOfValue;
        }
    }

    public static int getTitleBarH(Context contextValue) {
        int resultValueValue = 0;
        int resourceIdValue = contextValue.getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resourceIdValue > 0) {
            resultValueValue = contextValue.getResources().getDimensionPixelSize(resourceIdValue);
        }
        return resultValueValue;
    }


    public static String compressValue(String compressDataValue) throws IOException {
        if (compressDataValue == null || compressDataValue.length() == 0) {
            return compressDataValue;
        }
        ByteArrayOutputStream byteArrayOutputStreamValue = new ByteArrayOutputStream();
        GZIPOutputStream gzipOutputStreamValue = new GZIPOutputStream(byteArrayOutputStreamValue);
        gzipOutputStreamValue.write(compressDataValue.getBytes("UTF-8"));
        gzipOutputStreamValue.close();
        return byteArrayOutputStreamValue.toString("ISO-8859-1");
    }

    public static void copyValue(Activity actValue, String dataValue) {
        ClipboardManager clipboardManagerValue = (ClipboardManager) actValue.getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData clipDataValue = ClipData.newPlainText("Label", dataValue);
        if(clipboardManagerValue!=null){
        clipboardManagerValue.setPrimaryClip(clipDataValue);
        }
    }
}
