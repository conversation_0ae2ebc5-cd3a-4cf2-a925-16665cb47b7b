package com.fenqi.main.util

import android.annotation.SuppressLint
import android.os.CountDownTimer
import android.widget.TextView
import com.fenqi.platformtools.utils.CommonUtil

class CountDownTimerUtil {
    companion object{
        const val COUNT_DOWN_TIMER_SECOND = 1000
    }

    private var countDownTimerValue: CountDownTimer? = null

    fun countDownStart(tvValue: TextView?, timeValueValue: Int) {
        countDownTimerValue = object : CountDownTimer(
            timeValueValue.toLong() * COUNT_DOWN_TIMER_SECOND, COUNT_DOWN_TIMER_SECOND.toLong()) {
            @SuppressLint("SetTextI18n")
            override fun onTick(millisUntilFinishedValue: Long) {
                tvValue?.text = CommonUtil.getInstance().secondTimeChangeToHDS(millisUntilFinishedValue.toInt() / COUNT_DOWN_TIMER_SECOND)
            }

            override fun onFinish() {
                tvValue?.text = CommonUtil.getInstance().secondTimeChangeToHDS(0)
                appLoanCountDownTimerCallBack!!.countTimerFinish()
                countDownFinish()
            }
        }
        (countDownTimerValue as CountDownTimer).start()
    }

    private fun countDownFinish() {
        if (countDownTimerValue != null) {
            countDownTimerValue!!.cancel()
        }
    }

    interface LoanCountDownTimerCallBack {
        fun countTimerFinish()
    }

    private var appLoanCountDownTimerCallBack: LoanCountDownTimerCallBack? = null

    fun setLoanCountDownTimerCallBack(
        appLoanCountDownTimerCallBack: LoanCountDownTimerCallBack?
    ) {
        this.appLoanCountDownTimerCallBack =
            appLoanCountDownTimerCallBack
    }
}