package com.fenqi.main.util;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.pm.PackageManager;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Looper;
import android.os.Parcel;
import android.os.RemoteException;

import java.util.concurrent.LinkedBlockingQueue;

public class GoldAdvertisingIdClient {

    public static String getGoogleAdIdSmartCrocodile(Context contextSmartCrocodile) throws Exception {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            return "Cannot call in the main thread, You must call in the other thread";
        }
        PackageManager pmSmartCrocodile = contextSmartCrocodile.getPackageManager();
        pmSmartCrocodile.getPackageInfo("com.android.vending", 0);
        SmartCrocodileAdvertisingConnection connectionSmartCrocodile = new SmartCrocodileAdvertisingConnection();
        Intent intentSmartCrocodile = new Intent(
                "com.google.android.gms.ads.identifier.service.START");
        intentSmartCrocodile.setPackage("com.google.android.gms");
        if (contextSmartCrocodile.bindService(intentSmartCrocodile, connectionSmartCrocodile, Context.BIND_AUTO_CREATE)) {
            try {
                SmartCrocodileAdvertisingInterface adInterfaceSmartCrocodile = new SmartCrocodileAdvertisingInterface(
                        connectionSmartCrocodile.getBinder());
                return adInterfaceSmartCrocodile.getId();
            } finally {
                contextSmartCrocodile.unbindService(connectionSmartCrocodile);
            }
        }
        return "";
    }

    private static final class SmartCrocodileAdvertisingConnection implements ServiceConnection {
        boolean retrievedSmartCrocodile = false;
        private final LinkedBlockingQueue<IBinder> queueSmartCrocodile = new LinkedBlockingQueue<>(1);

        public void onServiceConnected(ComponentName name, IBinder service) {
            try {
                this.queueSmartCrocodile.put(service);
            } catch (InterruptedException localInterruptedException) {
                localInterruptedException.printStackTrace();
            }
        }

        public void onServiceDisconnected(ComponentName name) {

        }

        public IBinder getBinder() throws InterruptedException {
            if (this.retrievedSmartCrocodile)
                throw new IllegalStateException();
            this.retrievedSmartCrocodile = true;
            return this.queueSmartCrocodile.take();
        }
    }

    private static final class SmartCrocodileAdvertisingInterface implements IInterface {
        private final IBinder binderSmartCrocodile;

        public SmartCrocodileAdvertisingInterface(IBinder pBinder) {
            binderSmartCrocodile = pBinder;
        }

        public IBinder asBinder() {
            return binderSmartCrocodile;
        }

        public String getId() throws RemoteException {
            Parcel dataSmartCrocodile = Parcel.obtain();
            Parcel replySmartCrocodile = Parcel.obtain();
            String idSmartCrocodile;
            try {
                dataSmartCrocodile.writeInterfaceToken("com.google.android.gms.ads.identifier.internal.IAdvertisingIdService");
                binderSmartCrocodile.transact(1, dataSmartCrocodile, replySmartCrocodile, 0);
                replySmartCrocodile.readException();
                idSmartCrocodile = replySmartCrocodile.readString();
            } finally {
                replySmartCrocodile.recycle();
                dataSmartCrocodile.recycle();
            }
            return idSmartCrocodile;
        }
    }
}
