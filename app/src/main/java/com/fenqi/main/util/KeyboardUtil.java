package com.fenqi.main.util;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.ResultReceiver;
import android.view.View;
import android.view.inputmethod.InputMethodManager;

import com.fenqi.main.MainApplication;

import java.util.Objects;


public class KeyboardUtil {

	private static void softInput() {
		InputMethodManager inputMethodManagerValue =
				(InputMethodManager) Objects.requireNonNull(MainApplication.Companion.getApplication()).getSystemService(Context.INPUT_METHOD_SERVICE);
		inputMethodManagerValue.toggleSoftInput(InputMethodManager.SHOW_FORCED, 0);
	}

	public static void keyboardHideSoftInput(final Activity actValue) {
		View currentFocusValue = actValue.getCurrentFocus();
		if (currentFocusValue == null) {
			currentFocusValue = new View(actValue);
		}
		keyboardHideSoftInput(currentFocusValue);
	}

	private static void keyboardHideSoftInput(final View vValue) {
		InputMethodManager inputMethodManagerValue =
				(InputMethodManager) Objects.requireNonNull(MainApplication.Companion.getApplication())
						.getSystemService(Context.INPUT_METHOD_SERVICE);
		if (inputMethodManagerValue == null) {
			return;
		}
		inputMethodManagerValue.hideSoftInputFromWindow(
				vValue.getWindowToken(), 0, new ResultReceiver(new Handler()) {
					@Override
					protected void onReceiveResult(int resultCodeValueValue, Bundle resultDataValueValue) {
						if (resultCodeValueValue == InputMethodManager.RESULT_UNCHANGED_SHOWN
								|| resultCodeValueValue == InputMethodManager.RESULT_SHOWN) {
							softInput();
						}
					}
				});
	}

}
