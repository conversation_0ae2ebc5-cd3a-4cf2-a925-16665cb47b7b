package com.fenqi.main.util

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.text.TextUtils
import com.fenqi.platformtools.utils.PermissionUtils
import com.fenqi.main.MainApplication

class LocationUtil {

    companion object {
        fun getInstance(): LocationUtil {
            return LocationUtilBuilderHelper.instance
        }
    }

    private object LocationUtilBuilderHelper {
        val instance = LocationUtil()
    }

    private lateinit var locationManager:LocationManager
    private lateinit var location:Location
    private lateinit var locationProvideType:String

    @SuppressLint("MissingPermission")
    fun getCurrentLocationInfo(){
        val context = MainApplication.application?.applicationContext
        locationManager = context?.getSystemService(Context.LOCATION_SERVICE) as LocationManager

        val locationProviders:MutableList<String> = locationManager.getProviders(true)
        locationProvideType = when {
            locationProviders.contains(LocationManager.NETWORK_PROVIDER) -> {
                LocationManager.NETWORK_PROVIDER
            }
            locationProviders.contains(LocationManager.GPS_PROVIDER) -> {
                LocationManager.GPS_PROVIDER
            }
            else -> LocationManager.PASSIVE_PROVIDER
        }

        if(PermissionUtils.hasTargetPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) && !TextUtils.isEmpty(locationProvideType)
        ){
            location = locationManager.getLastKnownLocation(locationProvideType)!!
            locationCallBack.invoke(location)
        }
    }

    private val locationListener = LocationListener {
        locationCallBack.invoke(it)
    }

    private lateinit var locationCallBack:(location:Location)->Unit

    fun setLocationCallBack(locationCallBack:(location:Location)->Unit):LocationUtil{
        this.locationCallBack = locationCallBack
        return this
    }
}