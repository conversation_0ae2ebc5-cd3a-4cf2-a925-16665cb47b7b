package com.fenqi.main.util

import android.content.Context
import android.net.ConnectivityManager

class NetworkUtil {

    companion object {
        const val NET_TYPE_WIFI = 1
        const val NET_TYPE_MOBILE = 2
        const val NET_TYPE_NONE = -1

        fun getInstance(): NetworkUtil {
            return NetworkUtilBuilderHelper.instance
        }
    }

    private object NetworkUtilBuilderHelper {
        val instance = NetworkUtil();
    }

    private fun acquireHttpNetTypeConnectState(context: Context): Int {
        val connectivityManager =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val connectivityManagerActiveNetworkInfo =
            connectivityManager.activeNetworkInfo
        return if (connectivityManagerActiveNetworkInfo == null || !connectivityManagerActiveNetworkInfo.isAvailable || !connectivityManagerActiveNetworkInfo.isConnected) {
            NET_TYPE_NONE
        } else if (connectivityManagerActiveNetworkInfo.type == ConnectivityManager.TYPE_WIFI) {
            NET_TYPE_WIFI
        } else {
            NET_TYPE_MOBILE
        }
    }

    fun acquireHttpNetTypeConnectedType(context: Context): String {
        var netType = "unknown"
        if (acquireHttpNetTypeConnectState(context) == NET_TYPE_WIFI) {
            netType = "wifi"
        } else if (acquireHttpNetTypeConnectState(context) == NET_TYPE_MOBILE) {
            netType = "mobile"
        }
        return netType
    }
}