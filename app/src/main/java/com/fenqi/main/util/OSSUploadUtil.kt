package com.fenqi.main.util

import android.content.Context
import com.alibaba.sdk.android.oss.*
import com.alibaba.sdk.android.oss.callback.OSSCompletedCallback
import com.alibaba.sdk.android.oss.common.auth.OSSCredentialProvider
import com.alibaba.sdk.android.oss.common.auth.OSSStsTokenCredentialProvider
import com.alibaba.sdk.android.oss.model.PutObjectRequest
import com.alibaba.sdk.android.oss.model.PutObjectResult
import com.amazonaws.auth.BasicSessionCredentials
import com.amazonaws.mobileconnectors.s3.transferutility.*
import com.amazonaws.regions.Region
import com.amazonaws.services.s3.AmazonS3Client
import com.fenqi.main.AppUrlConfig
import com.fenqi.main.BuildConfig
import com.fenqi.main.MainApplication
import com.fenqi.main.bean.FileUploadResponseBean
import com.fenqi.main.bean.OSSTokenEntity
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.retrofit.CommonRequestHeaderInterceptor
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.RetryInterceptor
import com.fenqi.main.retrofit.api.MainApiService
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody.Part
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.scalars.ScalarsConverterFactory
import java.io.ByteArrayInputStream
import java.io.File
import java.io.InputStream
import java.util.*
import java.util.concurrent.TimeUnit


class OSSUploadUtil {
    
    private var fileType:Int = CommonConstant.FILE_UPLOAD_FILE_TYPE_TEXT

    private fun getFileName(objectName: String): String {
        return objectName + "/" + UUID.randomUUID().toString() + System.currentTimeMillis()
    }

    fun startUploadFile(filePath: String){
        LogUtil.log("startUploadFile--filePath-->${filePath}")
        fileType = CommonConstant.FILE_UPLOAD_FILE_TYPE_IMAGE
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val json = Json {
                        ignoreUnknownKeys = true
                    }
                    val ossTokenEntity: OSSTokenEntity = json.decodeFromString(it)
                    if (ossTokenEntity != null) {
                        LogUtil.log("startUploadFile--ossTokenEntity-->${Gson().toJson(ossTokenEntity)}")
                        if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_OSS) {
                            upload(ossTokenEntity, filePath, "".toByteArray(), CommonConstant.OSS_UPLOAD_TYPE_FILE)
                        }
                        if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_AWS) {
                            uploadS3(
                                ossTokenEntity,
                                filePath,
                                "".toByteArray(),
                                CommonConstant.OSS_UPLOAD_TYPE_FILE
                            )
                        }
                        if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_NATIVE) {
                            uploadFile(filePath)
                        }
                        if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_HUAWEI) {
                            uploadHuaweiCloud(ossTokenEntity,"".toByteArray(),"")
                        }
                    } else {
                        onFailed.invoke("")
                    }
                }
            }
            onFailure { _, _ ->
                onFailed.invoke("")
            }
        }) {
            RetrofitBuilder.getInstance()
                .createApi<MainApiService>(MainApplication.getInstance()?.applicationContext!!)
                .fileTokenGet(fileType)
        }
    }

    fun startUploadFile(filePath: String,ossTokenEntity:OSSTokenEntity) {
        fileType = CommonConstant.FILE_UPLOAD_FILE_TYPE_IMAGE
        if (ossTokenEntity != null) {
            if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_OSS) {
                upload(ossTokenEntity, filePath, "".toByteArray(), CommonConstant.OSS_UPLOAD_TYPE_FILE)
            }
            if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_AWS) {
                uploadS3(
                    ossTokenEntity,
                    filePath,
                    "".toByteArray(),
                    CommonConstant.OSS_UPLOAD_TYPE_FILE
                )
            }
            if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_NATIVE) {
                uploadFile(filePath)
            }
            if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_HUAWEI) {
                uploadHuaweiCloud(ossTokenEntity,"".toByteArray(),filePath)
            }
        } else {
            onFailed.invoke("")
        }
    }

    fun startUploadFileByteArray(data: ByteArray,ossTokenEntity: OSSTokenEntity) {
        fileType = CommonConstant.FILE_UPLOAD_FILE_TYPE_IMAGE
        if (ossTokenEntity != null) {
            if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_OSS) {
                upload(ossTokenEntity, "", data, CommonConstant.OSS_UPLOAD_TYPE_BYTE)
            }
            if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_AWS) {
                uploadS3(
                    ossTokenEntity,
                    "",
                    data,
                    CommonConstant.OSS_UPLOAD_TYPE_BYTE
                )
            }
            if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_NATIVE) {
                uploadFileByteArray(data)
            }
            if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_HUAWEI) {
                uploadHuaweiCloud(ossTokenEntity,data,"")
            }
        } else {
            onFailed.invoke("")
        }
    }

    fun startUploadFileByteArray(data: ByteArray) {
        fileType = CommonConstant.FILE_UPLOAD_FILE_TYPE_IMAGE
        RetrofitBuilder.getInstance().start({
            onSuccess {
                if (it != null) {
                    val json = Json {
                        ignoreUnknownKeys = true
                    }
                    val ossTokenEntity: OSSTokenEntity = json.decodeFromString(it)
                    if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_OSS) {
                        upload(ossTokenEntity, "", data, CommonConstant.OSS_UPLOAD_TYPE_BYTE)
                    }
                    if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_AWS) {
                        uploadS3(
                            ossTokenEntity,
                            "",
                            data,
                            CommonConstant.OSS_UPLOAD_TYPE_BYTE
                        )
                    }
                    if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_NATIVE) {
                        uploadFileByteArray(data)
                    }
                    if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_HUAWEI) {
                        uploadHuaweiCloud(ossTokenEntity,data,"")
                    }
                } else {
                    onFailed.invoke("")
                }
            }
            onFailure { _, _ ->
                onFailed.invoke("")
            }
        }) {
            RetrofitBuilder.getInstance()
                .createApi<MainApiService>(MainApplication.getInstance()?.applicationContext!!)
                .fileTokenGet(fileType)
        }
    }

    fun startUploadByte(data: ByteArray,ossTokenEntity: OSSTokenEntity) {
        fileType = CommonConstant.FILE_UPLOAD_FILE_TYPE_TEXT
        if (ossTokenEntity != null) {
            if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_OSS) {
                upload(ossTokenEntity, "", data, CommonConstant.OSS_UPLOAD_TYPE_BYTE)
            }
            if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_AWS) {
                uploadS3(ossTokenEntity, "", data, CommonConstant.OSS_UPLOAD_TYPE_BYTE)
            }
            if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_NATIVE) {
                uploadString(data.toString())
            }
            if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_HUAWEI) {
                uploadHuaweiCloud(ossTokenEntity,data,"")
            }
        } else {
            onFailed.invoke("")
        }
    }

    fun startUploadByte(data: ByteArray) {
        fileType = CommonConstant.FILE_UPLOAD_FILE_TYPE_TEXT
        RetrofitBuilder.getInstance().start({
            onSuccess {
                if (it != null) {
                    val json = Json {
                        ignoreUnknownKeys = true
                    }
                    val ossTokenEntity: OSSTokenEntity = json.decodeFromString(it)
                    if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_OSS) {
                        upload(ossTokenEntity, "", data, CommonConstant.OSS_UPLOAD_TYPE_BYTE)
                    }
                    if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_AWS) {
                        uploadS3(ossTokenEntity, "", data, CommonConstant.OSS_UPLOAD_TYPE_BYTE)
                    }
                    if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_NATIVE) {
                        uploadString(data.toString())
                    }
                    if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_HUAWEI) {
                        uploadHuaweiCloud(ossTokenEntity,data,"")
                    }
                } else {
                    onFailed.invoke("")
                }
            }
            onFailure { _, _ ->
                onFailed.invoke("")
            }
        }) {
            RetrofitBuilder.getInstance()
                .createApi<MainApiService>(MainApplication.getInstance()?.applicationContext!!)
                .fileTokenGet(fileType)
        }
    }


    fun startUploadString(data: String,ossTokenEntity: OSSTokenEntity) {
        fileType = CommonConstant.FILE_UPLOAD_FILE_TYPE_TEXT
        if (ossTokenEntity != null) {
            if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_OSS) {
                upload(ossTokenEntity, "", data.toByteArray(), CommonConstant.OSS_UPLOAD_TYPE_BYTE)
            }
            if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_AWS) {
                uploadS3(ossTokenEntity, "", data.toByteArray(), CommonConstant.OSS_UPLOAD_TYPE_BYTE)
            }
            if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_NATIVE) {
                uploadString(data)
            }
            if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_HUAWEI) {
                uploadHuaweiCloud(ossTokenEntity,data.toByteArray(),"")
            }
        } else {
            onFailed.invoke("")
        }
    }

    fun startUploadString(data: String) {
        fileType = CommonConstant.FILE_UPLOAD_FILE_TYPE_TEXT
        RetrofitBuilder.getInstance().start({
            onSuccess {
                if (it != null) {
                    val json = Json {
                        ignoreUnknownKeys = true
                    }
                    val ossTokenEntity: OSSTokenEntity = json.decodeFromString(it)
                    if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_OSS) {
                        upload(ossTokenEntity, "", data.toByteArray(), CommonConstant.OSS_UPLOAD_TYPE_BYTE)
                    }
                    if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_AWS) {
                        uploadS3(ossTokenEntity, "", data.toByteArray(), CommonConstant.OSS_UPLOAD_TYPE_BYTE)
                    }
                    if (ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_NATIVE) {
                        uploadString(data)
                    }
                    if(ossTokenEntity.clientType == CommonConstant.FILE_UPLOAD_TYPE_HUAWEI){
                        uploadHuaweiCloud(ossTokenEntity,data.toByteArray(),"")
                    }
                } else {
                    onFailed.invoke("")
                }
            }
            onFailure { _, _ ->
                onFailed.invoke("")
            }
        }) {
            RetrofitBuilder.getInstance()
                .createApi<MainApiService>(MainApplication.getInstance()?.applicationContext!!)
                .fileTokenGet(fileType)
        }
    }

    private fun upload(
        ossTokenEntity: OSSTokenEntity,
        filePath: String,
        data: ByteArray,
        type: String
    ) {
        val baseUrl = ossTokenEntity.baseUrl
        val ossStsTokenCredentialProvider: OSSCredentialProvider =
            OSSStsTokenCredentialProvider(
                ossTokenEntity.accessKeyId,
                ossTokenEntity.accessKeySecret,
                ossTokenEntity.securityToken
            )
        val clientConfiguration = ClientConfiguration()
        clientConfiguration.connectionTimeout = 30 * 1000
        clientConfiguration.socketTimeout = 30 * 1000
        clientConfiguration.maxConcurrentRequest = 5
        clientConfiguration.maxErrorRetry = 1

        val ossClient: OSS = OSSClient(
            MainApplication.getInstance()?.applicationContext,
            baseUrl,
            ossStsTokenCredentialProvider,
            clientConfiguration
        )

        val uploadObjectKeyValue = getFileName(ossTokenEntity.objectName.toString())

        var putObjectRequest: PutObjectRequest? = null

        if (type == CommonConstant.OSS_UPLOAD_TYPE_FILE) {
            putObjectRequest =
                PutObjectRequest(ossTokenEntity.bucketName, uploadObjectKeyValue, filePath)
        }
        if (type == CommonConstant.OSS_UPLOAD_TYPE_BYTE) {
            putObjectRequest =
                PutObjectRequest(ossTokenEntity.bucketName, uploadObjectKeyValue, data)
        }

        if (putObjectRequest != null) {
            ossStartUpload(ossClient, putObjectRequest, ossTokenEntity)
        }
    }

    private fun uploadHuaweiCloud(ossTokenEntity: OSSTokenEntity, data: ByteArray,filePath: String){
        val part: Part
        if(fileType == CommonConstant.FILE_UPLOAD_FILE_TYPE_TEXT){
            val builder = MultipartBody.Builder().setType(MultipartBody.FORM)
            val body = data.toRequestBody("text/plain".toMediaType())
            builder.addFormDataPart("name", System.currentTimeMillis().toString(), body)
            part = builder.build().part(0)
        } else {
            val file = File(filePath)
            val builder = MultipartBody.Builder().setType(MultipartBody.FORM)
            val body = file.asRequestBody("multipart/form-data".toMediaTypeOrNull())
            builder.addFormDataPart("name", file.name, body)
            part = builder.build().part(0)
        }

        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val json = Json {
                        ignoreUnknownKeys = true
                    }
                    val data: FileUploadResponseBean = json.decodeFromString(it)
                    LogUtil.log("huaweiCloud--${Gson().toJson(data)}")
//                onSuccess.invoke(it?.filePath.toString(), it?.filePath.toString())
                }

            }
            onFailure { msg, code ->
                LogUtil.log("huaweiCloud-failed-${msg}")
                onFailed.invoke(msg)
            }
        }) {
            createApi(MainApplication.getInstance()?.applicationContext!!).huaweiFileUpload(part,
                ossTokenEntity.baseUrl.toString()
            )
        }
    }

    private fun uploadS3(
        ossTokenEntity: OSSTokenEntity,
        filePath: String,
        data: ByteArray,
        type: String
    ) {
        try {
            TransferNetworkLossHandler.getInstance(MainApplication.getInstance()?.applicationContext)
            val credentials = BasicSessionCredentials(
                ossTokenEntity.accessKeyId,
                ossTokenEntity.accessKeySecret,
                ossTokenEntity.securityToken
            )
            val s3Client = AmazonS3Client(credentials, Region.getRegion(ossTokenEntity.region))

            val transferUtility =
                TransferUtility.builder().context(MainApplication.getInstance()?.applicationContext)
                    .defaultBucket(ossTokenEntity.bucketName).s3Client(s3Client).build()

            val key = getFileName(ossTokenEntity.objectName.toString())
            if(type == CommonConstant.OSS_UPLOAD_TYPE_FILE){
                val observer: TransferObserver =
                    transferUtility.upload(key, File(filePath))
                observer.setTransferListener(object : TransferListener {
                    override fun onStateChanged(id: Int, state: TransferState?) {
                        if(state == TransferState.COMPLETED){
                            val url = ossTokenEntity.urlPrefix+"/"+key
                            onSuccess.invoke(url,url)
                        } else if(state == TransferState.FAILED || state == TransferState.CANCELED){
                            onFailed.invoke("")
                        }
                    }

                    override fun onProgressChanged(id: Int, bytesCurrent: Long, bytesTotal: Long) {

                    }

                    override fun onError(id: Int, ex: Exception?) {
                        onFailed.invoke("")
                    }
                })
            } else {
                val inputStream:InputStream = ByteArrayInputStream(data)
                val observer: TransferObserver =
                    transferUtility.upload(key, inputStream)
                observer.setTransferListener(object : TransferListener {
                    override fun onStateChanged(id: Int, state: TransferState?) {
                        if(state == TransferState.COMPLETED){
                            val url = ossTokenEntity.urlPrefix+"/"+key
                            onSuccess.invoke(url,url)
                        } else if(state == TransferState.FAILED || state == TransferState.CANCELED){
                            onFailed.invoke("")
                        }
                    }

                    override fun onProgressChanged(id: Int, bytesCurrent: Long, bytesTotal: Long) {

                    }

                    override fun onError(id: Int, ex: Exception?) {
                        onFailed.invoke("")
                    }
                })
            }

        }catch (exception:Exception){
            exception.printStackTrace()
            CoroutineScope(Dispatchers.Main).launch {
                onFailed.invoke("")
            }
        }
    }

    private fun ossStartUpload(
        ossClient: OSS,
        putObjectRequest: PutObjectRequest,
        ossTokenEntity: OSSTokenEntity
    ) {
        val putObjectResultOSSAsyncTask = ossClient.asyncPutObject(putObjectRequest,
            object : OSSCompletedCallback<PutObjectRequest, PutObjectResult> {
                override fun onSuccess(request: PutObjectRequest?, result: PutObjectResult?) {
                    LogUtil.log("oss--success-->${ossTokenEntity.urlPrefix + "/" + request?.objectKey.toString()}")
                    CoroutineScope(Dispatchers.Main).launch {
                        onSuccess.invoke(
                            request?.objectKey.toString(),
                            ossTokenEntity.urlPrefix + "/" + request?.objectKey.toString()
                        )
                    }
                }

                override fun onFailure(
                    request: PutObjectRequest?,
                    clientException: ClientException?,
                    serviceException: ServiceException?
                ) {
                    CoroutineScope(Dispatchers.Main).launch {
                        onFailed.invoke("")
                    }
                    LogUtil.log("oss--failed-->${clientException?.message}--serviceException-${serviceException?.message}")
                }
            })
    }

    private fun uploadFile(filePath: String) {
        LogUtil.log("uploadFile--filePath-->${filePath}")
        val file = File(filePath)
        val builder = MultipartBody.Builder().setType(MultipartBody.FORM)
        val body = file.asRequestBody("multipart/form-data".toMediaTypeOrNull())
        builder.addFormDataPart("name", file.name, body)
        val part = builder.build().part(0)

        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val json = Json {
                        ignoreUnknownKeys = true
                    }
                    val data: FileUploadResponseBean = json.decodeFromString(it)
                    LogUtil.log("uploadFile-success-->${data.filePath.toString()}")
                    onSuccess.invoke(data.filePath.toString(), data.filePath.toString())
                }
            }
            onFailure { msg, code ->
                onFailed.invoke(msg)
                LogUtil.log("uploadFile-failed-msg--${msg}>")
            }
        }) {
            createApi(MainApplication.getInstance()?.applicationContext!!).fileUpload(part)
        }
    }

    private fun uploadFileByteArray(data: ByteArray) {
        val builder = MultipartBody.Builder().setType(MultipartBody.FORM)
        val body = data.toRequestBody("multipart/form-data".toMediaTypeOrNull())
        builder.addFormDataPart("name", UUID.randomUUID().toString()+"-"+System.currentTimeMillis().toString()+".jpg", body)
        val part = builder.build().part(0)
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val json = Json {
                        ignoreUnknownKeys = true
                    }
                    val data: FileUploadResponseBean = json.decodeFromString(it)
                    onSuccess.invoke(data.filePath.toString(), data.filePath.toString())
                }
            }
            onFailure { msg, code ->
                onFailed.invoke(msg)
            }
        }) {
            createApi(MainApplication.getInstance()?.applicationContext!!).fileUpload(part)
        }
    }

    private fun uploadString(data: String) {
        val builder = MultipartBody.Builder().setType(MultipartBody.FORM)
        val body = data.toRequestBody("text/plain".toMediaType())
        builder.addFormDataPart("name", System.currentTimeMillis().toString(), body)
        val part = builder.build().part(0)
        RetrofitBuilder.getInstance().start({
            onSuccess {
                it?.let {
                    val json = Json {
                        ignoreUnknownKeys = true
                    }
                    val data: FileUploadResponseBean = json.decodeFromString(it)
                    onSuccess.invoke(data.filePath.toString(), data.filePath.toString())
                }

            }
            onFailure { msg, code ->
                onFailed.invoke(msg)
            }
        }) {
            createApi(MainApplication.getInstance()?.applicationContext!!).fileUpload(part)
        }
    }

    fun createApi(context: Context): MainApiService {
//        var api = SharePreferenceData.getApiUrl()
//        if(TextUtils.isEmpty(api)){
//            val url = if (BuildConfig.DEBUG) AppUrlConfig.API_URL_DEBUG else AppUrlConfig.API_URL_RELEASE
//            api = url.split(";")[0]
//        }
        return Retrofit.Builder()
            .baseUrl(if(BuildConfig.DEBUG) AppUrlConfig.API_URL_DEBUG else AppUrlConfig.API_URL_RELEASE)
            .client(getRetrofitClient(context, mutableListOf()))
            .addConverterFactory(ScalarsConverterFactory.create())
            .build()
            .create(MainApiService::class.java)
    }

    private fun getRetrofitClient(
        context: Context,
        interceptors: MutableList<Interceptor>
    ): OkHttpClient {
        val builder = OkHttpClient().newBuilder()

        val logger = HttpLoggingInterceptor()
        logger.level = HttpLoggingInterceptor.Level.BODY

        if (LogUtil.isLog()) {
            builder.addInterceptor(logger)
        }

        interceptors.add(CommonRequestHeaderInterceptor(context, CommonConstant.HEADER_TYPE_NORMAL))

        for (item in interceptors) {
            builder.addInterceptor(item)

            if (item is RetryInterceptor) {
                builder.retryOnConnectionFailure(true)
            }
        }

        return builder.connectTimeout(RetrofitBuilder.MAX_TIME, TimeUnit.SECONDS)
            .readTimeout(RetrofitBuilder.MAX_TIME, TimeUnit.SECONDS)
            .writeTimeout(RetrofitBuilder.MAX_TIME, TimeUnit.SECONDS)
            .dispatcher(Dispatcher())
            .connectionPool(ConnectionPool())
            .build()
    }


    private lateinit var onSuccess: (url: String, fullUrl: String) -> Unit

    fun setOnSuccess(onSuccess: (url: String, fullUrl: String) -> Unit): OSSUploadUtil {
        this.onSuccess = onSuccess
        return this
    }

    private lateinit var onFailed: (msg:String) -> Unit

    fun setOnFailed(onFailed: (msg:String) -> Unit): OSSUploadUtil {
        this.onFailed = onFailed
        return this
    }
}