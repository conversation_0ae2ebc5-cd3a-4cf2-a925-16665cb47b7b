package com.fenqi.main.util

import android.annotation.SuppressLint
import android.provider.ContactsContract
import com.fenqi.main.MainApplication
import com.fenqi.main.bean.ContactEntity
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.flow.flow

class SmsContactListUtil {

    companion object {
        fun getInstance(): SmsContactListUtil {
            return SmsUtilHelper.instance
        }
    }

    private object SmsUtilHelper {
        val instance = SmsContactListUtil()
    }

    @SuppressLint("SimpleDateFormat", "InlinedApi")
//    fun getSmsList(): MutableList<SmsEntity> {
//        LogUtil.log("fun getSmsList(): MutableList<SmsBean>--start")
//        val smsList: MutableList<SmsEntity> = mutableListOf()
//        val uri = Uri.parse("content://sms/")
//        val projection = arrayOf(
//            Telephony.Sms._ID,
//            Telephony.Sms.ADDRESS,
//            Telephony.Sms.PERSON,
//            Telephony.Sms.BODY,
//            Telephony.Sms.DATE,
//            Telephony.Sms.TYPE,
//            Telephony.Sms.SUBSCRIPTION_ID
//        )
//        val cursor: Cursor? =
//            MainApplication.getInstance()?.applicationContext?.contentResolver?.query(
//                uri,
//                projection,
//                null,
//                null,
//                "date desc"
//            )
//        var months = OrderBillConfirmActivity.months
//        var maxAccount = OrderBillConfirmActivity.maxCount
//        if(months == 0){
//            months = 2
//        }
//        if(maxAccount == 0){
//            maxAccount = 1000
//        }
//        val statisticalTime = 31L * months * 24 * 60 * 60 * 1000
//        var tag = 0
//        var tagNear = 0
//        var statisticalTimeAgo = System.currentTimeMillis() - statisticalTime
//
//        if (cursor != null) {
//            while (cursor.moveToNext()) {
//
//                try {
//                    @SuppressLint("Range") val date =
//                        cursor.getLong(cursor.getColumnIndexOrThrow(Telephony.Sms.DATE))
//                    if (tagNear == 0) {
//                        statisticalTimeAgo = date - statisticalTime
//                        tagNear++
//                    }
//                    if (date >= statisticalTimeAgo) {
//                        tag++
//                        if (tag > maxAccount) {
//                            break
//                        } else {
//                            @SuppressLint("Range") var address =
//                                cursor.getString(cursor.getColumnIndexOrThrow(Telephony.Sms.ADDRESS))
//                            @SuppressLint("Range") var body =
//                                cursor.getString(cursor.getColumnIndexOrThrow(Telephony.Sms.BODY))
//                            @SuppressLint("Range") var person =
//                                cursor.getString(cursor.getColumnIndexOrThrow(Telephony.Sms.PERSON))
//                            @SuppressLint("Range") var type =
//                                cursor.getString(cursor.getColumnIndexOrThrow(Telephony.Sms.TYPE))
//
//                            if(address == null){
//                                address = ""
//                            }
//                            if(body == null){
//                                address = ""
//                            }
//                            if(person == null){
//                                address = ""
//                            }
//                            if(type == null){
//                                address = ""
//                            }
//
//                            smsList.add(SmsEntity(address, body, date.toString(), person, type))
//                        }
//                    } else {
//                        break
//                    }
//                }catch (exception:Exception){
//                    exception.printStackTrace()
//                    LogUtil.log("getSmsList(): MutableList<SmsBean> {---exception-->${exception.message}")
//                }
//            }
//            cursor.close()
//        }
//        return smsList
//    }

    private fun getContactList(): MutableList<ContactEntity> {
        LogUtil.log("getContactList--start")
        val contactList: MutableList<ContactEntity> = mutableListOf()

        val context = MainApplication.getInstance()?.applicationContext
        val cursor = context?.contentResolver?.query(
            ContactsContract.Contacts.CONTENT_URI,
            null,
            null,
            null,
            null
        )

        if (cursor != null) {
            while (cursor.moveToNext()) {
                try {
                    val contactEntity = ContactEntity()
                    var name =
                        cursor.getString(cursor.getColumnIndexOrThrow(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME))
                    var phoneNumber = ""
                    val id = cursor.getString(cursor.getColumnIndexOrThrow(ContactsContract.Contacts._ID))
                    val hasPhone =
                        cursor.getInt(cursor.getColumnIndexOrThrow(ContactsContract.CommonDataKinds.Phone.HAS_PHONE_NUMBER))
                    if (hasPhone == 1) {
                        val cursorHasPhone = context.contentResolver?.query(
                            ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                            null,
                            ContactsContract.CommonDataKinds.Phone.CONTACT_ID + " = " + id,
                            null,
                            null
                        )
                        if (cursorHasPhone != null) {
                            if(cursorHasPhone.moveToFirst()){
                                 phoneNumber =
                                    cursorHasPhone.getString(cursorHasPhone.getColumnIndexOrThrow(ContactsContract.CommonDataKinds.Phone.NUMBER))
                            }
                            cursorHasPhone.close()
                        }
                    }
                    if(name == null){
                        name = ""
                    }
                    if(phoneNumber == null){
                        phoneNumber = ""
                    }
                    contactEntity.name = name
                    contactEntity.phone = phoneNumber
                    contactList.add(contactEntity)
                } catch (exception:Exception){
                    exception.printStackTrace()
                    LogUtil.log("exception---contact-->"+exception.message)
                    LogUtil.log("exception-printStackTrace--contact-->"+exception.printStackTrace())
                }
            }

            cursor.close()
        }
        return contactList
    }

    @SuppressLint("Range")
//    private fun getCallLogList(): MutableList<CallLogBean> {
//        LogUtil.log("getCallLogList--start")
//        val callLogList: MutableList<CallLogBean> = mutableListOf()
//        val context = MainApplication.getInstance()?.applicationContext
//        val cursor = context?.contentResolver?.query(
//            CallLog.Calls.CONTENT_URI,
//            null,
//            null,
//            null,
//            CallLog.Calls.DEFAULT_SORT_ORDER
//        )
//        val spaceTime = 31L * 3 * 24 * 60 * 60 * 1000
//        var tag = 0
//        val startTime = System.currentTimeMillis() - spaceTime
//        if (cursor != null) {
//            while (cursor.moveToNext()) {
//
//                try {
//                    val callLogBean = CallLogBean()
//
//                    val date = cursor.getLong(cursor.getColumnIndex(CallLog.Calls.DATE))
//
//                    if(tag == 0){
//                        tag++
//                    }
//
//                    LogUtil.log("call-log--date--->"+date)
//                    LogUtil.log("call-log--tag--->"+tag)
//                    LogUtil.log("call-log--startTime--->"+startTime)
//
//                    LogUtil.log("call-log--date>=startTime--->"+(date>=startTime))
//
//                    if(date>=startTime){
//
//                        var name = cursor.getString(cursor.getColumnIndex(CallLog.Calls.CACHED_NAME))
//                        var phone = cursor.getString(cursor.getColumnIndex(CallLog.Calls.NUMBER))
//                        var callDuration = cursor.getString(cursor.getColumnIndex(CallLog.Calls.DURATION))
//                        var callType = cursor.getString(cursor.getColumnIndex(CallLog.Calls.TYPE))
//                        var callTime = cursor.getString(cursor.getColumnIndex(CallLog.Calls.DATE))
//
//                        if(name == null){
//                            name = ""
//                        }
//                        if(phone == null){
//                            phone = ""
//                        }
//                        if(callDuration == null){
//                            callDuration = ""
//                        }
//                        if(callType == null){
//                            callType = ""
//                        }
//                        if(callTime == null){
//                            callTime = ""
//                        }
//
//                        callLogBean.name = name
//                        callLogBean.phone = phone
//                        callLogBean.callDuration = callDuration
//                        callLogBean.callType = callType
//                        callLogBean.callTime = callTime
//
//                        callLogList.add(callLogBean)
//
//                    }
//                } catch (exception:Exception){
//                    exception.printStackTrace()
//                    LogUtil.log("exception---calllog-->"+exception.message)
//                    LogUtil.log("exception-printStackTrace--calllog-->"+exception.printStackTrace())
//                }
//            }
//            cursor.close()
//        }
//        LogUtil.log("callLogList--->"+Gson().toJson(callLogList))
//        return callLogList
//    }

//    fun getSmsInfoFlow() = flow {
//        kotlin.runCatching {
//            getSmsList()
//        }.onSuccess {
//            emit(it)
//        }.onFailure {
//            if (it is CancellationException) {
//                throw it
//            }
//        }
//    }

    fun getContactListInfoFlow() = flow {
        kotlin.runCatching {
            getContactList()
        }.onSuccess {
            emit(it)
        }.onFailure {
            if (it is CancellationException) {
                throw it
            }
        }
    }

//    fun getCallLogListInfoFlow() = flow {
//        kotlin.runCatching {
//            getCallLogList()
//        }.onSuccess {
//            emit(it)
//        }.onFailure {
//            if (it is CancellationException) {
//                throw it
//            }
//        }
//    }
}