package com.fenqi.main.util

import android.annotation.SuppressLint
import android.os.Build
import com.fenqi.main.MainApplication
import java.io.File

class SystemUtil {

    companion object {
        fun getInstance(): SystemUtil {
            return SystemUtilBuilderHelper.instance
        }
    }

    private object SystemUtilBuilderHelper {
        val instance = SystemUtil();
    }

    fun getPhoneBrand():String{
        return Build.BRAND + "-" + Build.MODEL
    }

    fun hasRooted():Boolean{
        val suDataValue = "su"
        val locationsDatasValue = arrayOf(
            "/system/bin/", "/system/xbin/", "/sbin/", "/system/sd/xbin/",
            "/system/bin/failsafe/", "/data/local/xbin/", "/data/local/bin/", "/data/local/"
        )
        for (locationDataValue in locationsDatasValue) {
            if (File(locationDataValue + suDataValue).exists()) {
                return true
            }
        }
        return false
    }

    @SuppressLint("InternalInsetResource")
    fun getStatusBarHeight():Int{
        val context = MainApplication.getInstance()?.applicationContext
        var height = 0
        val resourceId: Int? = context?.resources?.getIdentifier("status_bar_height", "dimen", "android")
        if(resourceId!! > 0){
            height = context.resources.getDimensionPixelSize(resourceId);
        }
        return height
    }
}