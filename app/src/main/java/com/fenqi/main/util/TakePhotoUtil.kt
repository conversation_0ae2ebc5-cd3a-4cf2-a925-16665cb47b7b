package com.fenqi.main.util

import android.app.Activity
import android.net.Uri
import android.os.Build
import com.google.gson.Gson
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.engine.CompressFileEngine
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.luck.picture.lib.utils.SandboxTransformUtils
import top.zibin.luban.Luban
import top.zibin.luban.OnNewCompressListener
import java.io.File
import java.util.ArrayList

class TakePhotoUtil {

    companion object {
        fun getInstance(): TakePhotoUtil {
            return TakePhotoUtilBuilderHelper.instance
        }
    }

    private object TakePhotoUtilBuilderHelper {
        val instance = TakePhotoUtil()
    }

    fun takeCamera(activity:Activity,successListener:(path:String)->Unit,cancelListener:()->Unit){
        val pictureSelector = PictureSelector.create(activity)
            .openCamera(SelectMimeType.ofImage())
            .setCompressEngine(CompressFileEngine { context, source, call ->
                Luban.with(context).load(source).ignoreBy(100).setCompressListener(object :
                    OnNewCompressListener {
                    override fun onStart() {

                    }

                    override fun onSuccess(source: String?, compressFile: File?) {
                        call?.onCallback(source, compressFile?.absolutePath)
                    }

                    override fun onError(source: String?, e: Throwable?) {
                        call?.onCallback(source, null)
                    }
                }).launch()
            })
        if(Build.VERSION.SDK_INT>=Build.VERSION_CODES.Q){
            pictureSelector.setSandboxFileEngine { context, srcPath, mineType, call ->
                if (call != null) {
                    val sandboxPath =
                        SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType)
                    call.onCallback(srcPath, sandboxPath);
                }
            }
        }
        pictureSelector.forResult(object : OnResultCallbackListener<LocalMedia?> {
                override fun onResult(result: ArrayList<LocalMedia?>?) {
                    result?.let { localMediaList->
                        if(localMediaList.size>0){
                            val localMedia = localMediaList[0]
                            successListener.invoke(localMedia?.compressPath.toString())
                        }
                    }
                }
                override fun onCancel() {
                    cancelListener.invoke()
                }
            })
    }

    fun takeCameraWeb(activity:Activity,successListener:(path:String)->Unit,cancelListener:()->Unit){
        val pictureSelector = PictureSelector.create(activity)
            .openCamera(SelectMimeType.ofImage())
            .setCompressEngine(CompressFileEngine { context, source, call ->
                Luban.with(context).load(source).ignoreBy(100).setCompressListener(object :
                    OnNewCompressListener {
                    override fun onStart() {

                    }

                    override fun onSuccess(source: String?, compressFile: File?) {
                        call?.onCallback(source, compressFile?.absolutePath)
                    }

                    override fun onError(source: String?, e: Throwable?) {
                        call?.onCallback(source, null)
                    }
                }).launch()
            })
        if(Build.VERSION.SDK_INT>=Build.VERSION_CODES.Q){
            pictureSelector.setSandboxFileEngine { context, srcPath, mineType, call ->
                if (call != null) {
                    val sandboxPath =
                        SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType)
                    call.onCallback(srcPath, sandboxPath);
                }
            }
        }
        pictureSelector.forResult(object : OnResultCallbackListener<LocalMedia?> {
            override fun onResult(result: ArrayList<LocalMedia?>?) {
                result?.let { localMediaList->
                    if(localMediaList.size>0){
                        val localMedia = localMediaList[0]
                        successListener.invoke(localMedia?.path.toString())
                    }
                }
            }
            override fun onCancel() {
                cancelListener.invoke()
            }
        })
    }


    fun takeVideo(activity:Activity,successListener:(path: String)->Unit,cancelListener:()->Unit){
        val pictureSelector = PictureSelector.create(activity)
            .openCamera(SelectMimeType.ofVideo())
            .setRecordVideoMaxSecond(5)
            .setRecordVideoMinSecond(1)
            .setSelectMaxDurationSecond(10)
            .setSelectMinDurationSecond(1)
            .isCameraAroundState(true)
//            .setCompressEngine(CompressFileEngine { context, source, call ->
//                Luban.with(context).load(source).setCompressListener(object :
//                    OnNewCompressListener {
//                    override fun onStart() {
//
//                    }
//
//                    override fun onSuccess(source: String?, compressFile: File?) {
//                        call?.onCallback(source, compressFile?.absolutePath)
//                    }
//
//                    override fun onError(source: String?, e: Throwable?) {
//                        call?.onCallback(source, null)
//                    }
//                }).launch()
//            })
        pictureSelector.forResult(object : OnResultCallbackListener<LocalMedia?> {
            override fun onResult(result: ArrayList<LocalMedia?>?) {
                result?.let { localMediaList->
                    if(localMediaList.size>0){
                        val localMedia = localMediaList[0]
                        successListener.invoke(localMedia?.path.toString())
                    }
                }
            }
            override fun onCancel() {
                cancelListener.invoke()
            }
        })
    }

    fun selectImage(activity:Activity,successListener:(path:String)->Unit,cancelListener:()->Unit){
        val pictureSelector = PictureSelector.create(activity)
            .openSystemGallery(SelectMimeType.ofImage())
            .setCompressEngine(CompressFileEngine { context, source, call ->
                Luban.with(context).load(source).ignoreBy(100).setCompressListener(object :
                    OnNewCompressListener {
                    override fun onStart() {

                    }

                    override fun onSuccess(source: String?, compressFile: File?) {
                        call?.onCallback(source, compressFile?.absolutePath)
                    }

                    override fun onError(source: String?, e: Throwable?) {
                        call?.onCallback(source, null)
                        LogUtil.log("e---->${e?.message}")
                    }
                }).launch()
            })
        if(Build.VERSION.SDK_INT>=Build.VERSION_CODES.Q){
            pictureSelector.setSandboxFileEngine { context, srcPath, mineType, call ->
                LogUtil.log("setSandboxFileEngine-->")
                if (call != null) {
                    val sandboxPath =
                        SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType)
                    call.onCallback(srcPath, sandboxPath)
                }
            }
        }
        pictureSelector.forSystemResult(object : OnResultCallbackListener<LocalMedia?> {
            override fun onResult(result: ArrayList<LocalMedia?>?) {
                LogUtil.log("forSystemResult-->")
                result?.let { localMediaList->
                    LogUtil.log("localMediaList-->${Gson().toJson(localMediaList)}")
                    if(localMediaList.size>0){
                        val localMedia = localMediaList[0]
                        successListener.invoke(localMedia?.compressPath.toString())
                    }
                }
            }
            override fun onCancel() {
                cancelListener.invoke()
            }
        })
    }

    fun selectImageWeb(activity:Activity,successListener:(path:String)->Unit,cancelListener:()->Unit){
        val pictureSelector = PictureSelector.create(activity)
            .openSystemGallery(SelectMimeType.ofImage())
            .setCompressEngine(CompressFileEngine { context, source, call ->
                Luban.with(context).load(source).ignoreBy(100).setCompressListener(object :
                    OnNewCompressListener {
                    override fun onStart() {

                    }

                    override fun onSuccess(source: String?, compressFile: File?) {
                        call?.onCallback(source, compressFile?.absolutePath)
                    }

                    override fun onError(source: String?, e: Throwable?) {
                        call?.onCallback(source, null)
                        LogUtil.log("e---->${e?.message}")
                    }
                }).launch()
            })
        if(Build.VERSION.SDK_INT>=Build.VERSION_CODES.Q){
            pictureSelector.setSandboxFileEngine { context, srcPath, mineType, call ->
                LogUtil.log("setSandboxFileEngine-->")
                if (call != null) {
                    val sandboxPath =
                        SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType)
                    call.onCallback(srcPath, sandboxPath)
                }
            }
        }
        pictureSelector.forSystemResult(object : OnResultCallbackListener<LocalMedia?> {
            override fun onResult(result: ArrayList<LocalMedia?>?) {
                LogUtil.log("forSystemResult-->")
                result?.let { localMediaList->
                    LogUtil.log("localMediaList-->${Gson().toJson(localMediaList)}")
                    if(localMediaList.size>0){
                        val localMedia = localMediaList[0]
                        successListener.invoke(localMedia?.path.toString())
                    }
                }
            }
            override fun onCancel() {
                cancelListener.invoke()
            }
        })
    }
}