package com.fenqi.main.view

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import com.fenqi.main.R
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import android.widget.*
import com.fenqi.main.AppUrlConfig
import com.fenqi.main.BuildConfig
import com.fenqi.main.bean.PrivacyPolicyBean
import com.fenqi.main.constant.CommonConstant
import com.fenqi.main.retrofit.CommonRequestHeaderInterceptor
import com.fenqi.main.retrofit.RetrofitBuilder
import com.fenqi.main.retrofit.RetryInterceptor
import com.fenqi.main.retrofit.api.MainApiService
import com.fenqi.main.sharedata.SharePreferenceData
import com.fenqi.main.util.LogUtil
import com.hjq.toast.Toaster
import okhttp3.ConnectionPool
import okhttp3.Dispatcher
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.scalars.ScalarsConverterFactory
import java.util.concurrent.TimeUnit

class AgreementView : LinearLayout {

    private var isAgree = true

    private var privacyPolicyUrl: String? = null
    private var imgCheck: ImageView? = null
    private var tvText: TextView? = null
    private var viewContainer: View? = null
//    private var flowLayout: FlowLayout? = null
    private var viewCheckContainer:View?=null

    private var textSize = 12F

    private var type:String = ""

    private var privacyBeanList: MutableList<PrivacyPolicyBean> = mutableListOf()

    constructor(context: Context?) : super(context) {}
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {

        LayoutInflater.from(context).inflate(R.layout.layout_agreement, this, true)

        imgCheck = findViewById(R.id.img_agreement_check)
//        flowLayout = findViewById(R.id.agreement_flowLayout)
        viewContainer = findViewById(R.id.view_agreement_container)
        viewCheckContainer = findViewById(R.id.view_agreement_cc)

        setIsAgree(true)

        viewCheckContainer?.setOnClickListener {
            clickCheck()
        }
        viewContainer?.setOnClickListener {
            clickCheck()
        }
    }

    private fun clickCheck() {
        isAgree = if (isAgree) {
            imgCheck?.setBackgroundResource(R.drawable.check_not)
            false
        } else {
            imgCheck?.setBackgroundResource(R.drawable.check_yes)
            true
        }

        if(!TextUtils.isEmpty(type)){
            SharePreferenceData.setHasAgree(isAgree,"${type}${SharePreferenceData.getPhoneNumber()}")
        }
    }

    fun hiddenCheckBox() {
        imgCheck?.visibility = GONE
        setIsAgree(true)
    }

    fun setIsAgree(isAgree: Boolean) {
        this.isAgree = isAgree
        if (isAgree) {
            imgCheck?.setBackgroundResource(R.drawable.check_yes)
        } else {
            imgCheck?.setBackgroundResource(R.drawable.check_not)
        }
    }

    fun getIsAgree(): Boolean {
        if (!isAgree) {
            Toaster.show(context.getString(R.string.agreement_tip))
        }
        return isAgree
    }

    fun getAgreementUrl(keyList: MutableList<String>,startText:String): AgreementView {
        getAction(keyList,startText)
        return this
    }

    @SuppressLint("SetTextI18n")
    fun getAgreementUrl(keyList: MutableList<String>): AgreementView {
        val startText = " ${context.getString(R.string.have_read_agree)}"
        getAction(keyList,startText)
        return this
    }

    @SuppressLint("SetTextI18n")
    private fun getAction(keyList: MutableList<String>, startText:String){
        privacyBeanList.clear()
//        flowLayout?.removeAllViews()

//        var tag = 0
//
//        val textViewFirst = TextView(context)
//        textViewFirst.textSize = textSize
//        textViewFirst.text = "      ${startText}:"
//        textViewFirst.setTextColor(ContextCompat.getColor(context, R.color.color_888888))
//        textViewFirst.setOnClickListener {
//            clickCheck()
//        }
//        flowLayout?.addView(textViewFirst)
//
//        for (index in keyList.indices) {
//            val key = keyList[index]
//            if (!TextUtils.isEmpty(key)) {
//                CoroutineScope(Dispatchers.IO).launch {
//                    delay(100)
//                    RetrofitBuilder.getInstance().start(PrivacyPolicyBean::class.java, {
//                        onSuccess {
//                            tag++
//                            it?.let { privacyBean ->
//                                privacyBeanList.add(privacyBean)
//                            }
//                            if(tag>=keyList.size){
//                                CommonData.privacyPolicyList = privacyBeanList
//                            }
//                        }
//                        onFailure { _, _ ->
//                            tag++
//                            if(tag>=keyList.size){
//                                CommonData.privacyPolicyList = privacyBeanList
//                            }
//                        }
//                    }) {
//                        RetrofitBuilder.getInstance()
//                            .createApi<MainApiService>(context)
//                            .userProtocolInfos(key)
//                    }
//                }
//
//                val textView = TextView(context)
//                textView.textSize = textSize
//                var textValue = ""
//                when (key) {
//                    CommonConstant.PRIVACY_POLICY_YSZC -> {
//                        textValue = context.getString(R.string.privacy_policy_yszc)
//                    }
//
//                    CommonConstant.PRIVACY_POLICY_ZCXY -> {
//                        textValue = context.getString(R.string.privacy_policy_zcxy)
//                    }
//                }
//
//                textView.setTextColor(ContextCompat.getColor(context, R.color.color_config_main))
//                textView.text = "《${textValue}》"
//                textView.setOnClickListener {
//                    context.startActivity(WebPrivacyActivity.callIntent(context as Activity?,index))
//                }
//                flowLayout?.addView(textView)
//            }
//        }
    }

    fun createApi(context: Context): MainApiService {
        return Retrofit.Builder()
            .baseUrl(if(BuildConfig.DEBUG) AppUrlConfig.API_URL_DEBUG else AppUrlConfig.API_URL_RELEASE)
            .client(getRetrofitClient(context, mutableListOf()))
            .addConverterFactory(ScalarsConverterFactory.create())
            .build()
            .create(MainApiService::class.java)
    }

    private fun getRetrofitClient(
        context: Context,
        interceptors: MutableList<Interceptor>
    ): OkHttpClient {
        val builder = OkHttpClient().newBuilder()

        val logger = HttpLoggingInterceptor()
        logger.level = HttpLoggingInterceptor.Level.BODY

        if (LogUtil.isLog()) {
            builder.addInterceptor(logger)
        }

        try {
            if (SharePreferenceData.getIsRSAOpen() == true) {
                interceptors.add(
                    CommonRequestHeaderInterceptor(
                        context,
                        CommonConstant.HEADER_TYPE_RSA
                    )
                )
            } else {
                interceptors.add(
                    CommonRequestHeaderInterceptor(
                        context,
                        CommonConstant.HEADER_TYPE_NORMAL
                    )
                )
            }
        } catch (exception: Exception) {
            exception.printStackTrace()
            interceptors.add(
                CommonRequestHeaderInterceptor(
                    context,
                    CommonConstant.HEADER_TYPE_NORMAL
                )
            )
        }

        for (item in interceptors) {
            builder.addInterceptor(item)

            if (item is RetryInterceptor) {
                builder.retryOnConnectionFailure(true)
            }
        }

        return builder.connectTimeout(RetrofitBuilder.MAX_TIME, TimeUnit.SECONDS)
            .readTimeout(RetrofitBuilder.MAX_TIME, TimeUnit.SECONDS)
            .writeTimeout(RetrofitBuilder.MAX_TIME, TimeUnit.SECONDS)
            .dispatcher(Dispatcher())
            .connectionPool(ConnectionPool())
            .build()
    }
}