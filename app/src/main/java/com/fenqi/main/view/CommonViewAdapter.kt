package com.fenqi.main.view

import android.graphics.BitmapFactory
import android.text.TextUtils
import android.widget.ImageView
import androidx.databinding.BindingAdapter
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.fenqi.main.util.RotateTransformation

object CommonViewAdapter {

    @BindingAdapter("set_imageview_source")
    @JvmStatic
    fun setImageViewSource(imageView: ImageView, url: String?) {
        if(!TextUtils.isEmpty(url)){
            val bitmap = BitmapFactory.decodeFile(url)
            val width = bitmap.width
            val height = bitmap.height
            if (height > width) {
                Glide.with(imageView.context).load(url)
                    .apply(RequestOptions.bitmapTransform(RotateTransformation(270F)))
                    .skipMemoryCache(true).into(imageView)
            } else {
                Glide.with(imageView.context).load(url).skipMemoryCache(true).into(imageView)
            }
        }
    }
}