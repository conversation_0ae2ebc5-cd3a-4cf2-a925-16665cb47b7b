package com.fenqi.main.view

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.fenqi.main.R
import com.fenqi.main.page.login.LoginActivity
import com.fenqi.main.page.main.MainActivity
import com.fenqi.main.sharedata.SharePreferenceData

class MainTabView(context: Context?, attrs: AttributeSet?) : LinearLayout(context, attrs){

  var textViewHome:TextView? = null
  var textViewMine:TextView?=null
  var textViewRepayment:TextView?=null

  var imageIconHome:ImageView?=null
  var imageIconMine:ImageView?=null
  var imageIconRepayment:ImageView?=null

  private var currentCheckPosition = MainActivity.FRAGMENT_HOME

  init {
    LayoutInflater.from(context).inflate(R.layout.layout_main_tab,this)

    textViewHome = findViewById(R.id.text_main_tab_trip)
    textViewMine = findViewById(R.id.text_main_tab_mine)
    textViewRepayment = findViewById(R.id.text_main_tab_repayment)

    imageIconHome = findViewById(R.id.img_main_tab_home)
    imageIconMine = findViewById(R.id.img_main_tab_mine)
    imageIconRepayment = findViewById(R.id.img_main_tab_repayment)

    val relatHome:RelativeLayout = findViewById(R.id.view_main_tab_home)
    val relatMine:RelativeLayout = findViewById(R.id.view_main_tab_mine)
    val relatRepayment:RelativeLayout = findViewById(R.id.relat_main_tab_repayment)

    relatHome.setOnClickListener {
      setTargetIndex(MainActivity.FRAGMENT_HOME)
    }

    relatRepayment.setOnClickListener {
      setTargetIndex(MainActivity.FRAGMENT_REPAYMENT)
    }

    relatMine.setOnClickListener {
      setTargetIndex(MainActivity.FRAGMENT_MINE)
    }
  }

  fun setTargetIndex(index:Int){
    when(index){
      MainActivity.FRAGMENT_HOME->{
        if(currentCheckPosition != MainActivity.FRAGMENT_HOME){
          mainTabClickCallBack?.onMainTabClick(MainActivity.FRAGMENT_HOME)
          currentCheckPosition = MainActivity.FRAGMENT_HOME

          textViewHome?.setTextColor(ContextCompat.getColor(context!!,R.color.color_black))
          textViewMine?.setTextColor(ContextCompat.getColor(context!!,R.color.color_888888))
          textViewRepayment?.setTextColor(ContextCompat.getColor(context!!,R.color.color_888888))

          textViewMine?.setBackgroundResource(0)
          textViewRepayment?.setBackgroundResource(0)

          imageIconHome?.setBackgroundResource(R.drawable.home_light)
          imageIconMine?.setBackgroundResource(R.drawable.mine_grey)
          imageIconRepayment?.setBackgroundResource(R.drawable.repayment_grey)
        }
      }
      MainActivity.FRAGMENT_REPAYMENT->{
        if(currentCheckPosition != MainActivity.FRAGMENT_REPAYMENT){
          if (TextUtils.isEmpty(SharePreferenceData.getToken())) {
            context?.startActivity(LoginActivity.callIntent(context))
          } else {
            mainTabClickCallBack?.onMainTabClick(MainActivity.FRAGMENT_REPAYMENT)
            currentCheckPosition = MainActivity.FRAGMENT_REPAYMENT

            textViewHome?.setTextColor(ContextCompat.getColor(context!!,R.color.base_library_text_important))
            textViewRepayment?.setTextColor(ContextCompat.getColor(context!!,R.color.color_black))
            textViewMine?.setTextColor(ContextCompat.getColor(context!!,R.color.base_library_text_important))

            textViewHome?.setBackgroundResource(0)
            textViewMine?.setBackgroundResource(0)

            imageIconHome?.setBackgroundResource(R.drawable.home_grey)
            imageIconRepayment?.setBackgroundResource(R.drawable.repayment_light)
            imageIconMine?.setBackgroundResource(R.drawable.mine_grey)
          }
        }
      }
      MainActivity.FRAGMENT_MINE->{
        if(currentCheckPosition != MainActivity.FRAGMENT_MINE){
          if (TextUtils.isEmpty(SharePreferenceData.getToken())) {
            context?.startActivity(LoginActivity.callIntent(context))
          } else {
            mainTabClickCallBack?.onMainTabClick(MainActivity.FRAGMENT_MINE)
            currentCheckPosition = MainActivity.FRAGMENT_MINE

            textViewHome?.setTextColor(ContextCompat.getColor(context!!,R.color.base_library_text_important))
            textViewRepayment?.setTextColor(ContextCompat.getColor(context!!,R.color.base_library_text_important))
            textViewMine?.setTextColor(ContextCompat.getColor(context!!,R.color.color_black))

            textViewHome?.setBackgroundResource(0)
            textViewRepayment?.setBackgroundResource(0)

            imageIconHome?.setBackgroundResource(R.drawable.home_grey)
            imageIconRepayment?.setBackgroundResource(R.drawable.repayment_grey)
            imageIconMine?.setBackgroundResource(R.drawable.mine_light)
          }
        }
      }
    }
  }

  interface MainTabClickCallBack{
    fun onMainTabClick(position:Int)
  }

  private var mainTabClickCallBack:MainTabClickCallBack? = null

  fun setMainTabClickCallBack(mainTabClickCallBack: MainTabClickCallBack){
    this.mainTabClickCallBack = mainTabClickCallBack
  }

}