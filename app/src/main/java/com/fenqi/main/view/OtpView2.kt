package com.fenqi.main.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.fenqi.main.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.*

@SuppressLint("CustomViewStyleable")
class OtpView2(context: Context?, attrs: AttributeSet?) : RelativeLayout(context, attrs) {

    companion object{
        const val CODE_DEFAULT_TIME = 60
    }

    private var tvAction:TextView
    private var timer: Timer?=null
    private var number = 60

    private var timeColor = 0
    private var textColor = 0

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_get_otp2,this)
        tvAction = findViewById(R.id.tv_get_otp_view_action)

        val typedArray = context!!.obtainStyledAttributes(attrs, R.styleable.OtpView2)
        timeColor = typedArray.getColor(R.styleable.OtpView2_ov_time_color_2, ContextCompat.getColor(context, R.color.color_999999))
        textColor = typedArray.getColor(R.styleable.OtpView2_ov_text_color_2, ContextCompat.getColor(context, R.color.color_config_text_main))

        tvAction.setTextColor(textColor)
        typedArray.recycle()
    }

    fun startTimer(seconds:Int){
        number = seconds
        if(timer == null){
            timer = Timer()
        }
        timer?.schedule(object :TimerTask(){
            @SuppressLint("SetTextI18n")
            override fun run() {
                number--
                if(number<1){
                    stopTimer()
                    CoroutineScope(Dispatchers.Main).launch {
                        tvAction.text = context.getString(R.string.text_get_otp)
                        tvAction.setTextColor(textColor)
                        if(finishedCallBack!=null){
                            finishedCallBack.invoke()
                        }
                    }
                } else {
                    CoroutineScope(Dispatchers.Main).launch {
                        tvAction.text = "${number}${context.getString(R.string.second_resend_code)}"
                        tvAction.setTextColor(timeColor)
                        tvAction.textSize = 14F
                    }
                }
            }
        },0,1000)
    }

    fun stopTimer(){
        timer?.apply {
            cancel()
        }
        timer = null
    }

    private lateinit var finishedCallBack: () -> Unit

    fun setFinishedCallBack(finishedCallBack: () -> Unit): OtpView2 {
        this.finishedCallBack = finishedCallBack
        return this
    }
}