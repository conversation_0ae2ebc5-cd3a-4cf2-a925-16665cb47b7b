package com.fenqi.main.view.banner

import android.annotation.SuppressLint
import android.content.Context
import android.text.TextUtils
import android.view.ViewGroup
import android.widget.ImageView
import com.fenqi.main.bean.SlideshowVOBean
import com.bumptech.glide.Glide
import com.youth.banner.adapter.BannerAdapter

class ImageAdapter(datas: MutableList<SlideshowVOBean>?, var context: Context) :
    BannerAdapter<SlideshowVOBean, ImageHolder>(
        datas
    ) {
    override fun onCreateHolder(parent: ViewGroup?, viewType: Int): ImageHolder {
        val imageView = ImageView(parent!!.context)
        //注意，必须设置为match_parent，这个是viewpager2强制要求的
        val params = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        imageView.layoutParams = params
        imageView.scaleType = ImageView.ScaleType.CENTER_CROP
        return ImageHolder(imageView)
    }

    override fun onBindView(
        holder: ImageHolder?,
        data: SlideshowVOBean?,
        position: Int,
        size: Int
    ) {
        if (!TextUtils.isEmpty(data?.imgUrl)) {
            holder?.imageView?.let { Glide.with(context).load(data?.imgUrl).into(it) }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateData(data: List<SlideshowVOBean?>?) {
        mDatas.clear()
        mDatas.addAll(data!!)
        notifyDataSetChanged()
    }

}