package com.fenqi.main.view.dialog

import android.app.Activity
import com.fenqi.main.R
import com.fenqi.main.databinding.DialogAuthBackTipBinding
import com.fenqi.platformtools.customerui.dialog.BaseBottomDialog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

public class AuthBackTipConfirmOrderDialog(activity: Activity) :
    BaseBottomDialog<DialogAuthBackTipBinding>(activity) {

    override fun attachDialogLayout(): Int {
        return R.layout.dialog_auth_back_tip
    }

    override fun initView() {
        setCancelAble(false)

        databinding?.buttonDialogCancel?.setOnClickListener {
            clickConfirm()
        }

        databinding?.buttonDialogConfirm?.setOnClickListener {
            clickCancelCallback()
        }
//
//        val str="<font color='#000000'>只需</font><font color='#E73940'>2分钟</font><font color='#000000'>，即可提交资料马上领钱，真的要放弃吗？</font>"
        databinding?.tvDialogBackTip?.text = activity.getString(R.string.apply_confirm_back_tip)
    }

    private fun clickConfirm(){
        confirmLogic.invoke()
    }
    private fun clickCancelCallback(){
        dismissDialog()
        CoroutineScope(Dispatchers.Main).launch {
            delay(200)
            dismissDialog()
        }
    }

    private lateinit var confirmLogic:()->Unit
    fun setConfirmLogic(confirmLogic:()->Unit){
        this.confirmLogic = confirmLogic
    }
}