package com.fenqi.main.view.dialog

import android.app.Activity
import android.text.Html
import android.text.Html.TO_HTML_PARAGRAPH_LINES_CONSECUTIVE
import com.fenqi.main.R
import com.fenqi.main.databinding.DialogAuthBackTipBinding
import com.fenqi.platformtools.customerui.dialog.BaseBottomDialog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

public class AuthBackTipDialog(activity: Activity) :
    BaseBottomDialog<DialogAuthBackTipBinding>(activity) {

    override fun attachDialogLayout(): Int {
        return R.layout.dialog_auth_back_tip
    }

    override fun initView() {
        databinding?.click = this

        setCancelAble(false)

        databinding?.buttonDialogCancel?.setOnClickListener {
            clickConfirm()
        }

        databinding?.buttonDialogConfirm?.setOnClickListener {
            clickCancelCallback()
        }

        val str="<font color='#000000'>已完成</font><font color='#E73940'>98%</font><font color='#000000'>,仅需</font><font color='#E73940'>10秒</font><font color='#000000'>即可完成，现在放弃就太可惜了！</font>"
        databinding?.tvDialogBackTip?.text = Html.fromHtml(str,TO_HTML_PARAGRAPH_LINES_CONSECUTIVE);
    }

    private fun clickConfirm(){
        confirmLogic.invoke()
    }
    private fun clickCancelCallback(){
        dismissDialog()
        CoroutineScope(Dispatchers.Main).launch {
            delay(200)
            dismissDialog()
        }
    }

    private lateinit var confirmLogic:()->Unit
    fun setConfirmLogic(confirmLogic:()->Unit){
        this.confirmLogic = confirmLogic
    }
}