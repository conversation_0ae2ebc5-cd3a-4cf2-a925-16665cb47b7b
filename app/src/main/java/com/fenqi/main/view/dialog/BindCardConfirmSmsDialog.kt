package com.fenqi.main.view.dialog

import android.app.Activity
import com.fenqi.main.R
import com.fenqi.main.databinding.DialogBindCardConfirmSmsBinding
import com.fenqi.main.view.OtpView
import com.fenqi.main.view.autoinput.OnInputListener
import com.fenqi.platformtools.customerui.dialog.BaseDialog
import com.hjq.toast.Toaster

class BindCardConfirmSmsDialog(activity: Activity) : BaseDialog<DialogBindCardConfirmSmsBinding>(
    activity
) {
    override fun attachDialogLayout(): Int {
        return R.layout.dialog_bind_card_confirm_sms
    }

    private var smsCode:String = ""

    override fun initView() {
        databinding?.click = this

        databinding?.otpviewDialogSmsConfirm?.setFinishedCallBack {
            databinding?.otpviewDialogSmsConfirm?.isClickable = true
        }
        databinding?.otpviewDialogSmsConfirm?.setOnClickListener {
            clickSendCode()
        }
        databinding?.buttonDialogSubmit?.setOnClickListener {
            clickSubmit()
        }
        databinding?.viewDialogCancel?.setOnClickListener {
            clickDismiss()
        }
        databinding?.spliteditConfirmSms?.requestFocus()
        databinding?.spliteditConfirmSms?.setOnInputListener(object : OnInputListener() {
            override fun onInputFinished(content: String?) {
                smsCode = content.toString()
            }
        })
    }

    private fun clickSendCode(){
        resendCallBack.invoke()
    }

    fun clickSubmit(){
        if(smsCode.length<6){
            Toaster.show(activity.getString(R.string.common_input_tip))
            return
        }
        clickSubmitCallBack.invoke()
    }

    fun clickDismiss(){
        dismissDialog()
    }

    fun stopTimer(){
        databinding?.otpviewDialogSmsConfirm?.stopTimer()
    }

    fun startTimer(){
        databinding?.otpviewDialogSmsConfirm?.isClickable = false
        databinding?.otpviewDialogSmsConfirm?.stopTimer()
        databinding?.otpviewDialogSmsConfirm?.startTimer(OtpView.CODE_DEFAULT_TIME)
    }

    fun getSmsCode():String{
        return smsCode
    }

    private lateinit var clickSubmitCallBack:()->Unit
    fun setClickSubmitCallBack(clickSubmitCallBack:()->Unit):BindCardConfirmSmsDialog{
        this.clickSubmitCallBack = clickSubmitCallBack
        return this
    }

    private lateinit var resendCallBack:()->Unit
    fun setResendCallBack(resendCallBack:()->Unit):BindCardConfirmSmsDialog{
        this.resendCallBack = resendCallBack
        return this
    }
}