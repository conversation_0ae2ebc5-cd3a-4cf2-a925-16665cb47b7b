package com.fenqi.main.view.dialog

import android.app.Activity
import android.view.View
import com.fenqi.platformtools.customerui.dialog.BaseDialog
import com.fenqi.main.R
import com.fenqi.main.databinding.DialogCommonMessageBinding

public class CommonMessageDialog(activity: Activity) :
    BaseDialog<DialogCommonMessageBinding>(activity) {

    override fun attachDialogLayout(): Int {
        return R.layout.dialog_common_message
    }

    fun setMessage(value: String){
        databinding?.tvDialogCommonMessage?.text = value
    }

    public interface CommonMessageDialogCallBack{
        fun confirmClick()
    }

    private var commonMessageDialogCallBack: CommonMessageDialogCallBack? = null

    fun setCommonMessageDialogCallBack(commonMessageDialogCallBack: CommonMessageDialogCallBack){
        this.commonMessageDialogCallBack = commonMessageDialogCallBack
    }

    override fun initView() {
        databinding?.tvDialogCommonConfirm?.setOnClickListener {
            dismissDialog()
            commonMessageDialogCallBack?.confirmClick()
        }
        databinding?.tvDialogCommonCancel?.setOnClickListener {
            dismissDialog()
        }
    }

    fun setSingle(){
        databinding?.tvDialogCommonConfirm?.text = activity.getString(R.string.i_see)
        databinding?.tvDialogCommonCancel?.visibility = View.GONE
        databinding?.viewCommonMessageDialogLine?.visibility = View.GONE

        databinding?.tvDialogCommonConfirm?.setBackgroundResource(R.drawable.select_10r_grey_bottom)
    }
}