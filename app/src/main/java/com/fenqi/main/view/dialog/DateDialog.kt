package com.fenqi.main.view.dialog

import android.app.Activity
import androidx.core.content.ContextCompat
import com.fenqi.main.R
import com.fenqi.main.util.LogUtil
import com.loper7.date_time_picker.DateTimeConfig
import com.loper7.date_time_picker.dialog.CardDatePickerDialog
import java.util.Calendar
import java.util.Date

class DateDialog(var activity: Activity) {

    var dialog: CardDatePickerDialog? = null

    init {

        val selectedDateValue = Calendar.getInstance()
        val startDateValue = Calendar.getInstance()
        val endDateValue = Calendar.getInstance()
        val yearValue = selectedDateValue[Calendar.YEAR]
        val monthValue = selectedDateValue[Calendar.MONTH]
        val dayValue = selectedDateValue[Calendar.DAY_OF_MONTH]
        startDateValue.set(1940, 0, 1)
        endDateValue.set(yearValue, monthValue, dayValue)
        selectedDateValue.set(yearValue, monthValue, dayValue)

        dialog = CardDatePickerDialog.builder(activity)
            .setTitle(activity.getString(R.string.pls_date))
            .setMaxTime(endDateValue.timeInMillis)
            .setThemeColor(ContextCompat.getColor(activity, R.color.color_config_main))
            .showBackNow(false)
            .setDisplayType(
                mutableListOf(
                    DateTimeConfig.YEAR,
                    DateTimeConfig.MONTH,
                    DateTimeConfig.DAY
                )
            )
            .setLabelText("年", "月", "日")
            .setOnChoose("选择") { millisecond ->
                LogUtil.log("millisecond-->${millisecond}")
                val date = Date()
                date.time = millisecond
                dateCallBack.invoke(date)
            }
            .setOnCancel("关闭") {}
            .build()
        initView()
    }

    fun initView() {

    }

    fun showDialog() {
        if (activity.isFinishing || activity.isDestroyed) {
            return
        }
        dialog?.apply {
            dismiss()
            show()
        }
    }

    fun dismissDialog() {
        if (activity.isFinishing || activity.isDestroyed) {
            return
        }
        dialog?.dismiss()
    }

    private lateinit var dateCallBack: (date: Date) -> Unit

    fun setDateCallBack( dateCallBack: (date: Date) -> Unit) {
        this.dateCallBack = dateCallBack
    }
}