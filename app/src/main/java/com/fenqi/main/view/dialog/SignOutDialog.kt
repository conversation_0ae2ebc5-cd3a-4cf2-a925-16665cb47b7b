package com.fenqi.main.view.dialog

import android.app.Activity
import com.fenqi.platformtools.customerui.dialog.BaseDialog
import com.fenqi.main.R
import com.fenqi.main.databinding.DialogCommonTwoButtonTipBinding

class SignOutDialog(activity: Activity) : BaseDialog<DialogCommonTwoButtonTipBinding>(activity) {

    override fun attachDialogLayout(): Int {
        return R.layout.dialog_common_two_button_tip
    }

    override fun initView() {
        databinding?.click = this

        databinding?.buttonDialogCancel?.setOnClickListener {
            clickCancelCallback()
        }
        databinding?.buttonDialogConfirm?.setOnClickListener {
            clickSubmit()
        }
    }

    private fun clickCancelCallback(){
        dismissDialog()
    }

    fun clickSubmit(){
        dismissDialog()
        clickSubmit.invoke()
    }

    fun showSignOutDialog(content:String){
        databinding?.tvContentTwoButtonDialog?.text = content
        showDialog()
    }

    private lateinit var clickSubmit:()->Unit
    fun setClickSubmit(clickSubmit:()->Unit){
        this.clickSubmit = clickSubmit
    }
}