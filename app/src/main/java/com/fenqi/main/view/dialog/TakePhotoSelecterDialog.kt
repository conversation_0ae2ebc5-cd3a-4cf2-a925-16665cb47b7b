package com.fenqi.main.view.dialog

import android.app.Activity
import com.fenqi.main.R
import com.fenqi.main.databinding.DialogTakePhotoBinding
import com.fenqi.platformtools.customerui.dialog.BaseBottomDialog

class TakePhotoSelecterDialog(activity: Activity) : BaseBottomDialog<DialogTakePhotoBinding>(activity) {

    override fun attachDialogLayout(): Int {
        return R.layout.dialog_take_photo
    }

    override fun initView() {
        databinding?.click = this

        dialog?.setOnDismissListener {

        }

        databinding?.buttonDialogCancel?.setOnClickListener {
            clickCancelCallback()
        }

        databinding?.buttonDialogTakePhoto?.setOnClickListener {
            clickTakePicture()
        }
        databinding?.buttonDialogSelectImage?.setOnClickListener {
            clickAlbum()
        }
        databinding?.viewDialogContainer?.setOnClickListener {
            clickContainer()
        }
    }

    private fun clickTakePicture(){
        dismissDialog()
        takePhotoListener.clickTakePicture()
    }

    private fun clickAlbum(){
        dismissDialog()
        takePhotoListener.clickAlbum()
    }

    private fun clickCancelCallback(){
        dismissDialog()
        takePhotoListener.clickCancelCallback()
    }

    private fun clickContainer(){
        dismissDialog()
    }

    public interface TakePhotoListener{
        fun clickTakePicture()
        fun clickAlbum()
        fun clickCancelCallback()
    }

    private lateinit var takePhotoListener: TakePhotoListener
    private lateinit var takePhotoDismissListener: ()->Unit

    public fun setTakePhotoListener(takePhotoListener: TakePhotoListener):TakePhotoSelecterDialog{
        this.takePhotoListener = takePhotoListener
        return this
    }

    public fun setTakePhotoDismissListener(takePhotoDismissListener:()->Unit){
        this.takePhotoDismissListener = takePhotoDismissListener
    }
}