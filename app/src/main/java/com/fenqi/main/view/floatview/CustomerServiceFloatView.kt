package com.fenqi.main.view.floatview

import android.content.Context
import android.view.View
import android.view.ViewTreeObserver
import com.google.android.material.imageview.ShapeableImageView

class CustomerServiceFloatView(context: Context) : BaseFloatView(context) {

    private var mAdsorbType = ADSORB_HORIZONTAL

    override fun getChildView(): View {
        val imageView = ShapeableImageView(context)
        imageView.layoutParams.width = 50
        imageView.layoutParams.height = 50
        imageView.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                imageView.viewTreeObserver.removeOnGlobalLayoutListener(this)
            }
        })
        return imageView
    }

    override fun getIsCanDrag(): Boolean {
        return true
    }

    override fun getAdsorbType(): Int {
        return mAdsorbType
    }

    override fun getAdsorbTime(): Long {
        return 3000
    }

    fun setAdsorbType(type: Int) {
        mAdsorbType = type
    }


}