package com.fenqi.main.view.pickerview

import android.annotation.SuppressLint
import android.content.Context
import android.view.View
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter
import com.fenqi.main.R
import com.fenqi.main.bean.SelectVoEntity
import com.fenqi.main.databinding.AdapterPickerviewBinding

class PickerViewAdapter(
    datas: MutableList<SelectVoEntity>,
    context: Context,
    baseRecyclerViewCallBack: BaseRecyclerViewCallBack?
) : BaseRecyclerViewAdapter<SelectVoEntity, AdapterPickerviewBinding>(
    datas,
    context,
    baseRecyclerViewCallBack
) {
    @SuppressLint("SetTextI18n")
    override fun onBaseBindViewHolder(
        holder: BaseRecyclerViewHolder<AdapterPickerviewBinding>,
        position: Int
    ) {
        val selectVo = datas[position]
        val dataBinding = holder.itemDataBinding
        dataBinding.tvAdapterPickerviewContent.text = selectVo.name

        dataBinding.viewAdapterPickerview.setOnClickListener {
            baseRecyclerViewCallBack?.onClickListener(position)
        }

        if(position == datas.size-1){
            dataBinding.lineAdapterPickerview.visibility = View.GONE
        } else {
            dataBinding.lineAdapterPickerview.visibility = View.VISIBLE
        }
    }

    override fun attachLayout(): Int {
        return R.layout.adapter_pickerview
    }

}