package com.fenqi.main.view.pickerview

import android.app.Activity
import android.widget.LinearLayout
import androidx.appcompat.app.ActionBar.LayoutParams
import androidx.recyclerview.widget.LinearLayoutManager
import com.fenqi.platformtools.customerui.dialog.BaseDialog
import com.fenqi.main.R
import com.fenqi.main.bean.SelectVoEntity
import com.fenqi.main.databinding.DialogPickerViewBinding
import com.fenqi.main.util.BaseUtil
import com.fenqi.platformtools.adapter.BaseRecyclerViewAdapter

public class PickerViewDialog(activity: Activity) :
    BaseDialog<DialogPickerViewBinding>(activity) {

    override fun attachDialogLayout(): Int {
        return R.layout.dialog_picker_view
    }

    private var adapterPickerView:PickerViewAdapter? = null
    private var selectVo:MutableList<SelectVoEntity> = mutableListOf()

    override fun initView() {
        adapterPickerView = PickerViewAdapter(mutableListOf(),activity,object :BaseRecyclerViewAdapter.BaseRecyclerViewCallBack{
            override fun onClickListener(position: Int) {
                selectCallBack.invoke(selectVo[position].name.toString())
                dismissDialog()
            }
        })
        databinding?.recyclerviewAdapterPickerview?.apply {
            layoutManager = LinearLayoutManager(activity)
            setHasFixedSize(true)
            adapter = adapterPickerView
        }
        databinding?.viewDialogPickerviewClose?.setOnClickListener {
            dismissDialog()
        }
    }

    fun showPickerView(selectVo:MutableList<SelectVoEntity>){
        this.selectVo = selectVo
        if(selectVo.size>7){
            val layoutParams = LinearLayout.LayoutParams(LayoutParams.MATCH_PARENT,LayoutParams.WRAP_CONTENT)
            layoutParams.height = BaseUtil.dpToPx(activity,340F)
            databinding?.recyclerviewAdapterPickerview?.layoutParams = layoutParams
        } else {
            val layoutParams = LinearLayout.LayoutParams(LayoutParams.MATCH_PARENT,LayoutParams.WRAP_CONTENT)
            databinding?.recyclerviewAdapterPickerview?.layoutParams = layoutParams
        }

        adapterPickerView?.addNewList(selectVo)
        showDialog()
    }


    private lateinit var selectCallBack: (value:String) -> Unit

    fun setSelectCallBack(selectCallBack: (value:String) -> Unit) {
        this.selectCallBack = selectCallBack
    }

}