package com.fenqi.main.view.timer;

import android.annotation.SuppressLint;
import android.os.CountDownTimer;
import android.widget.TextView;

import com.fenqi.main.util.BaseUtil;

public class LoanCountDownTimer {

    private final static int COUNT_DOWN_TIMER_SECOND = 1000;

    private CountDownTimer countDownTimerValue;

    public LoanCountDownTimer() {

    }

    public void countDownStart(final TextView tvValue, int timeValueValue) {
        countDownTimerValue = new CountDownTimer(
                timeValueValue * COUNT_DOWN_TIMER_SECOND,
                COUNT_DOWN_TIMER_SECOND) {
            @SuppressLint("SetTextI18n")
            @Override
            public void onTick(long millisUntilFinishedValue) {
                if (tvValue != null) {
                    tvValue.setText(BaseUtil.secondTimeChangeToHDS(
                            (int) millisUntilFinishedValue / COUNT_DOWN_TIMER_SECOND) + "");
                }
            }

            @Override
            public void onFinish() {
                tvValue.setText(BaseUtil.secondTimeChangeToHDS(
                        0));
                AppLoanCountDownTimerCallBack.countTimerFinish();
                countDownFinish();
            }
        };
        countDownTimerValue.start();
    }

    private void countDownFinish() {
        if (countDownTimerValue != null) {
            countDownTimerValue.cancel();
        }
    }

    public interface LoanCountDownTimerCallBack {
        void countTimerFinish();
    }

    private LoanCountDownTimerCallBack AppLoanCountDownTimerCallBack;

    public void setLoanCountDownTimerCallBack(
            LoanCountDownTimerCallBack AppLoanCountDownTimerCallBack) {
        this.AppLoanCountDownTimerCallBack = AppLoanCountDownTimerCallBack;
    }
}
