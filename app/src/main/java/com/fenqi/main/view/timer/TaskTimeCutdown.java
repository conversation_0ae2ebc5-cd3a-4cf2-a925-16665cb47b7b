package com.fenqi.main.view.timer;

import android.os.Handler;
import android.os.Message;

import androidx.annotation.NonNull;

import java.lang.ref.WeakReference;

public class TaskTimeCutdown {

    private int anCurrentIntValue;
    private int targetAintValue;
    private boolean hasLoadingValue = false;

    private final TaskHandler taskHandler;

    private static class TaskHandler extends Handler{

        private final WeakReference<TimeTaskCallBack> mWeakReference;

        public TaskHandler(TimeTaskCallBack TimeTaskCallBack){
            mWeakReference = new WeakReference<>(TimeTaskCallBack);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (mWeakReference.get() != null) {
                mWeakReference.get().currentDataValue(msg.arg1);
            }
        }
    }

    public TaskTimeCutdown(TimeTaskCallBack TimeTaskCallBack) {
        this.TimeTaskCallBack = TimeTaskCallBack;
        taskHandler = new TaskHandler(TimeTaskCallBack);
    }

    public void runTaskPolling(int timeIntValue, int cIntValue) {
        this.targetAintValue = timeIntValue;
        this.anCurrentIntValue = cIntValue;
        hasLoadingValue = false;
        new Thread(taskRunnableValue).start();
    }

    private final Runnable taskRunnableValue = new Runnable() {

        @Override
        public void run() {
            while (!hasLoadingValue) {
                if (anCurrentIntValue < targetAintValue) {
                    anCurrentIntValue++;
                    Message obtainMessageValue = taskHandler.obtainMessage();
                    obtainMessageValue.arg1 = anCurrentIntValue;
                    taskHandler.sendMessage(obtainMessageValue);
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException exValue) {
                        exValue.printStackTrace();
                    }
                } else {
                    hasLoadingValue = true;
                }
            }
        }
    };

    public interface TimeTaskCallBack {
        void currentDataValue(int iValue);
    }

    private TimeTaskCallBack TimeTaskCallBack;

    public void setTimeTaskCallBack(
            TimeTaskCallBack TimeTaskCallBack) {
        this.TimeTaskCallBack = TimeTaskCallBack;
    }
}
