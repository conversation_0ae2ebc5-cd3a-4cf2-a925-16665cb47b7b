package com.fenqi.platformtools.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.RecyclerView

abstract class BaseRecyclerViewAdapter<Bean,DB:ViewDataBinding>(
  open var datas:MutableList<Bean>,
  open val context: Context,open val baseRecyclerViewCallBack: BaseRecyclerViewCallBack?): RecyclerView.Adapter<BaseRecyclerViewAdapter.BaseRecyclerViewHolder<DB>>() {

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseRecyclerViewHolder<DB> {
    val itemDataBinding:DB = DataBindingUtil.inflate(LayoutInflater.from(parent.context), attachLayout(),parent,false)
    return BaseRecyclerViewHolder(itemDataBinding.root,itemDataBinding)
  }

  override fun onBindViewHolder(holder: BaseRecyclerViewHolder<DB>, position: Int) {
    return onBaseBindViewHolder(holder,position)
  }

  override fun getItemCount(): Int {
    return datas.size
  }

  @SuppressLint("NotifyDataSetChanged")
  fun addNewList(list:MutableList<Bean>){
    datas.clear()
    datas.addAll(list)
    notifyDataSetChanged()
  }

  @SuppressLint("NotifyDataSetChanged")
  fun addAll(list: MutableList<Bean>){
    datas.addAll(list)
    notifyDataSetChanged()
  }

  interface BaseRecyclerViewCallBack{
    fun onClickListener(position:Int)
  }

  class BaseRecyclerViewHolder<DB:ViewDataBinding>(val itemView: View,val itemDataBinding: DB) : RecyclerView.ViewHolder(itemView) {

  }

  abstract fun onBaseBindViewHolder(holder:BaseRecyclerViewHolder<DB>,position: Int)
  abstract fun attachLayout():Int
}