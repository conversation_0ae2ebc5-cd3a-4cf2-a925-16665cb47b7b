package com.fenqi.platformtools.customerui

import android.app.Activity
import android.app.Dialog
import android.view.Gravity
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.view.animation.LinearInterpolator
import android.widget.ImageView
import android.widget.TextView
import com.fenqi.main.R

class LoadingView(val activity: Activity?) {
    var imageView: ImageView? = null
    var textView: TextView? = null
    var dialog: Dialog? = null
    var animation: Animation? = null

    fun initDialog() {
        dialog = activity?.let { Dialog(it, R.style.CommonLoadDialog) }
        dialog?.apply {
            setContentView(R.layout.base_loadingview)
            window?.attributes?.gravity = Gravity.CENTER
            if (activity != null) {
                setOwnerActivity(activity)
            }
            setCanceledOnTouchOutside(false)
            setCancelable(true)
        }
        animation = AnimationUtils.loadAnimation(activity, R.anim.anim_loadingview)
        animation?.interpolator = LinearInterpolator()
        imageView = dialog?.findViewById(R.id.img_base_load_view)
        textView = dialog?.findViewById(R.id.tv_base_load_view)
    }

    fun showDialog(message: String, cancel: Boolean) {
        message.let {
            textView?.visibility = VISIBLE
            textView?.text = message
            attachAnimation()
            dialog?.apply {
                dismiss()
                setCancelable(cancel)
                show()
            }
        }
    }

    fun showDialog(message: String) {
        message.let {
            textView?.visibility = VISIBLE
            textView?.text = message
            attachAnimation()
            dialog?.apply {
                dismiss()
                show()
            }
        }
    }

    fun showDialog(cancel: Boolean) {
        textView?.visibility = GONE
        attachAnimation()
        dialog?.apply {
            dismiss()
            setCancelable(cancel)
            show()
        }
    }

    fun showDialog() {
        textView?.visibility = GONE
        attachAnimation()
        dialog?.apply {
            dismiss()
            show()
        }
    }

    fun dismissDialog() {
        activity?.let {
            if (activity.isFinishing || activity.isDestroyed) {
                return
            }
            dialog?.dismiss()
            imageView?.clearAnimation()
        }
    }

    private fun attachAnimation() {
        imageView?.animation = animation
        imageView?.startAnimation(animation)
    }

}