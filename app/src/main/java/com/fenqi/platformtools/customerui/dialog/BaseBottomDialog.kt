package com.fenqi.platformtools.customerui.dialog

import android.app.Activity
import android.view.*
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.afollestad.materialdialogs.LayoutMode
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.bottomsheets.BottomSheet

abstract class BaseBottomDialog<DB:ViewDataBinding>(open var activity: Activity) {

  protected var dialog:MaterialDialog?=null
  protected var databinding:DB?=null

  init {
      initDialog()
  }

  fun initDialog(){
    dialog = MaterialDialog(activity,BottomSheet(LayoutMode.WRAP_CONTENT))

    val dialogView:View = LayoutInflater.from(activity).inflate(attachDialogLayout(),null,false)
    databinding = DataBindingUtil.bind(dialogView)

    dialog?.setContentView(dialogView)
    dialog?.cancelable(true)

    initView()
  }

  fun setCancelAble(cancelable:Boolean){
    dialog?.cancelable(cancelable)
  }

  abstract fun attachDialogLayout():Int

  abstract fun initView()

  fun showDialog(){
    dialog?.apply {
      show()
    }
  }

  fun dismissDialog(){
    dialog?.dismiss()
  }

}