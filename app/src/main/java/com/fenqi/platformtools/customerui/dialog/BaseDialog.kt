package com.fenqi.platformtools.customerui.dialog

import android.app.Activity
import android.view.*
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.afollestad.materialdialogs.MaterialDialog

abstract class BaseDialog<DB:ViewDataBinding>(open var activity: Activity) {

  protected var dialog:MaterialDialog?=null
  protected var databinding:DB?=null

  init {
      initDialog()
  }

  fun initDialog(){
    dialog = MaterialDialog(activity)

    val dialogView:View = LayoutInflater.from(activity).inflate(attachDialogLayout(),null,false)
    databinding = DataBindingUtil.bind(dialogView)

    dialog?.setContentView(dialogView)

    initView()
  }

  fun setCancelAble(cancelable:Boolean){
    dialog?.cancelable(cancelable)
  }

  abstract fun attachDialogLayout():Int

  abstract fun initView()

  fun showDialog(){
    if(activity.isFinishing || activity.isDestroyed){
      return
    }
    dialog?.apply {
      dismiss()
      show()
    }
  }

  fun dismissDialog(){
    if(activity.isFinishing || activity.isDestroyed){
      return
    }
    dialog?.dismiss()
  }

}