package com.fenqi.platformtools.utils;

import android.app.Activity;

import java.util.Stack;

public class ActivityManager {

  private Stack<Activity> activities = new Stack<>();
  private static volatile ActivityManager activityManager;

  public static synchronized ActivityManager getActivityManager() {
    if (activityManager == null) {
      activityManager = new ActivityManager();
    }
    return activityManager;
  }

  public void addActivity(Activity actValue) {
    if (activities == null) {
      activities = new Stack<>();
    }
    activities.add(actValue);
  }

  public void removeTargetActivity(Activity actValue) {
    if (activities != null) {
      activities.remove(actValue);
    }
  }

  private void destroyTargetActivity(Activity activityValue) {
    if (activityValue != null) {
      if (activities.contains(activityValue)) {
        activities.remove(activityValue);
      }
      activityValue.finish();
    }
  }

  public void destroyTargetActivity(Class<?> aClassValue) {
    if (activities.size() <= 0) {
      return;
    }
    for (int i = 0; i< activities.size(); i++) {
      if (activities.get(i).getClass().equals(aClassValue)) {
        destroyTargetActivity(activities.get(i));
      }
    }
  }

  public void destroyAllActivity() {
    for (int i = 0, size = activities.size(); i < size; i++) {
      if (null != activities.get(i)) {
        activities.get(i).finish();
      }
    }
    activities.clear();
  }
}