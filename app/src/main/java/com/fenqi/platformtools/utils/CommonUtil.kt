package com.fenqi.platformtools.utils

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Build
import androidx.recyclerview.widget.RecyclerView
import java.io.ByteArrayOutputStream
import java.lang.Exception
import java.lang.StringBuilder
import java.util.*
import java.util.zip.GZIPOutputStream

class CommonUtil {

    companion object {
        fun getInstance(): CommonUtil {
            return CommonUtilBuilderHelper.instance
        }
    }

    private object CommonUtilBuilderHelper {
        val instance = CommonUtil();
    }


    fun copy(context: Context?,data:String) {
        val clipboardManagerYYCashValue = context?.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clipDataYYCashValue = ClipData.newPlainText("Label", data)
        clipboardManagerYYCashValue.setPrimaryClip(clipDataYYCashValue)
    }

    fun isSlideBottom(recyclerView:RecyclerView):Boolean{
        return recyclerView.computeVerticalScrollExtent() + recyclerView.computeVerticalScrollOffset() >= recyclerView.computeVerticalScrollRange()
    }

    fun getAcceptLanguage(context: Context): String {
        try {
            var locale: Locale? = null
            locale = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                context.resources.configuration.locales.get(0)
            } else {
                context.resources.configuration.locale
            }
            val sb = StringBuilder()
            if (locale.country.isNotBlank()) {
                sb.append(locale.language).append("-").append(locale.country).append(",")
            }
            sb.append(locale.language).append(";q=0.8")
            return sb.toString()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return "en-US,en;q=0.8"
    }

    fun secondTimeChangeToHDS(secondDataValue: Int): String? {
        var hValueValue = 0
        var dValueValue = 0
        var sValueValue = 0
        val tempValueValue = secondDataValue % 3600
        if (secondDataValue > 3600) {
            hValueValue = secondDataValue / 3600
            if (tempValueValue != 0) {
                if (tempValueValue > 60) {
                    dValueValue = tempValueValue / 60
                    if (tempValueValue % 60 != 0) {
                        sValueValue = tempValueValue % 60
                    }
                } else {
                    sValueValue = tempValueValue
                }
            }
        } else {
            dValueValue = secondDataValue / 60
            if (secondDataValue % 60 != 0) {
                sValueValue = secondDataValue % 60
            }
        }
        val sRealValueValue: String = if (sValueValue < 10) {
            "0$sValueValue"
        } else {
            sValueValue.toString()
        }
        val hRealValueValue: String = if (hValueValue < 10) {
            "0$hValueValue"
        } else {
            hValueValue.toString()
        }
        val dRealValueValue: String = if (dValueValue < 10) {
            "0$dValueValue"
        } else {
            dValueValue.toString()
        }
        return if (hValueValue == 0) {
            "$dRealValueValue:$sRealValueValue"
        } else {
            "$hRealValueValue:$dRealValueValue:$sRealValueValue"
        }
    }

    fun bytesToString(bytes: ByteArray): String {
        if (bytes.isEmpty()) {
            return ""
        }
        val buf = StringBuilder()
        for (b in bytes) {
            buf.append(String.format("%02X:", b))
        }
        if (buf.isNotEmpty()) {
            buf.deleteCharAt(buf.length - 1)
        }
        return buf.toString()
    }

    fun compressStringValue(value: String?): String? {
        if (value == null || value.isEmpty()) {
            return value
        }
        val byteArrayOutputStream = ByteArrayOutputStream()
        val gzipOutputStream = GZIPOutputStream(byteArrayOutputStream)
        gzipOutputStream.write(value.toByteArray(charset("UTF-8")))
        gzipOutputStream.close()
        return byteArrayOutputStream.toString("ISO-8859-1")
    }
}