package com.fenqi.platformtools.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.AsyncTask;
import android.text.TextUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;

public class DownLoadImgTaskService extends AsyncTask<String, Void, String> {

    @SuppressLint("StaticFieldLeak")
    private Context context;

    public DownLoadImgTaskService(Context context) {
        this.context = context;
    }

    @Override
    protected String doInBackground(String... valuesValue) {
        String filePathValueValue = null;
        try {
            URL urlApiValue = new URL(valuesValue[0]);
            URLConnection urlConnectionValue = urlApiValue.openConnection();
            urlConnectionValue.setConnectTimeout(10 * 1000);
            InputStream inputStreamValue = urlConnectionValue.getInputStream();
            byte[] bytesValue = new byte[1024];
            int lengthValue;
            File sFileValue = new File(context.getExternalCacheDir().getPath());
            if (!sFileValue.exists()) {
                sFileValue.mkdirs();
            }
            filePathValueValue = sFileValue.getPath() + "/" + valuesValue[1] + ".jpg";
            OutputStream fileOutputStreamValue = new FileOutputStream(filePathValueValue);
            while ((lengthValue = inputStreamValue.read(bytesValue)) != -1) {
                fileOutputStreamValue.write(bytesValue, 0, lengthValue);
            }
            fileOutputStreamValue.close();
            inputStreamValue.close();
        } catch (Exception exValue) {
            exValue.printStackTrace();
        }
        return filePathValueValue;
    }

    @Override
    protected void onPostExecute(String valueValue) {
        super.onPostExecute(valueValue);
        if (TextUtils.isEmpty(valueValue)) {
            return;
        }
        if (nextMoneyDownLoadImageTaskCallBack != null) {
            nextMoneyDownLoadImageTaskCallBack.downLoadImageSuccess(valueValue);
        }
    }

    public interface DownLoadImageTaskCallBack {
        void downLoadImageSuccess(String filePathValue);
    }

    private DownLoadImageTaskCallBack nextMoneyDownLoadImageTaskCallBack;

    public DownLoadImgTaskService setDownLoadImageTaskCallBack(
            DownLoadImageTaskCallBack nextMoneyDownLoadImageTaskCallBack) {
        this.nextMoneyDownLoadImageTaskCallBack = nextMoneyDownLoadImageTaskCallBack;
        return this;
    }
}
