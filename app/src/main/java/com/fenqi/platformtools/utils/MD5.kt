package com.fenqi.platformtools.utils

import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

object MD5 {

    fun md5(plainText: String): String {
        return try {
            val md = MessageDigest.getInstance("MD5")
            md.update(plainText.toByteArray())
            val b = md.digest()
            val buf = StringBuffer("")
            for (offset in b.indices) {
                var i = b[offset].toInt()
                if (i < 0) {
                    i += 256
                }
                if (i < 16) {
                    buf.append("0")
                }
                buf.append(Integer.toHexString(i))
            }
            buf.toString()
        } catch (exception: NoSuchAlgorithmException) {
            exception.printStackTrace()
            ""
        }
    }
}