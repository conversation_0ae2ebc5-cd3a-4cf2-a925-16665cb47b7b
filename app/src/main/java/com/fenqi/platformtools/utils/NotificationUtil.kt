package com.fenqi.platformtools.utils

import android.app.AppOpsManager
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import com.fenqi.main.BuildConfig
import java.lang.reflect.InvocationTargetException

object NotificationUtil {
    fun setNotification(context: Context) {
        val enabled = isNotificationEnabled(context)
        if (!enabled) {
            val localIntent = Intent()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                localIntent.action = Settings.ACTION_APP_NOTIFICATION_SETTINGS
                localIntent.putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
                localIntent.putExtra("app_uid", context.applicationInfo.uid)
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                localIntent.action = "android.settings.APP_NOTIFICATION_SETTINGS"
                localIntent.putExtra("app_package", context.packageName)
                localIntent.putExtra("app_uid", context.applicationInfo.uid)
            } else if (Build.VERSION.SDK_INT == Build.VERSION_CODES.KITKAT) {
                localIntent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                localIntent.addCategory(Intent.CATEGORY_DEFAULT)
                localIntent.data = Uri.parse("package:" + context.packageName)
            } else
                localIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                localIntent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                localIntent.data = Uri.fromParts("package", context.packageName, null)
            context.startActivity(localIntent)
        }
    }

    fun isNotificationEnabled(mContext: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val mNotificationManager = mContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            var channel = mNotificationManager.getNotificationChannel(BuildConfig.APPLICATION_ID)
            !(!mNotificationManager.areNotificationsEnabled() || channel.importance == NotificationManager.IMPORTANCE_NONE)
        } else if (Build.VERSION.SDK_INT >= 24) {
            val mNotificationManager =
                mContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            mNotificationManager.areNotificationsEnabled()
        } else {
            val CHECK_OP_NO_THROW = "checkOpNoThrow"
            val OP_POST_NOTIFICATION = "OP_POST_NOTIFICATION"
            val appOps = mContext.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
            val appInfo = mContext.applicationInfo
            val pkg = mContext.applicationContext.packageName
            val uid = appInfo.uid
            try {
                val appOpsClass = Class.forName(AppOpsManager::class.java.name)
                val checkOpNoThrowMethod = appOpsClass.getMethod(
                    CHECK_OP_NO_THROW, Integer.TYPE,
                    Integer.TYPE, String::class.java
                )
                val opPostNotificationValue = appOpsClass.getDeclaredField(OP_POST_NOTIFICATION)
                val value = opPostNotificationValue[Int::class.java] as Int
                (checkOpNoThrowMethod.invoke(appOps, value, uid, pkg) as Int
                        == AppOpsManager.MODE_ALLOWED)
            } catch (e: ClassNotFoundException) {
                true
            } catch (e: NoSuchMethodException) {
                true
            } catch (e: NoSuchFieldException) {
                true
            } catch (e: InvocationTargetException) {
                true
            } catch (e: IllegalAccessException) {
                true
            } catch (e: RuntimeException) {
                true
            }
        }
    }
}