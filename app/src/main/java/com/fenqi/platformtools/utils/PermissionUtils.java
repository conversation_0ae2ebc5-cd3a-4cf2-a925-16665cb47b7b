package com.fenqi.platformtools.utils;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.core.content.PermissionChecker;

public class PermissionUtils {

  public static boolean hasPermission(@NonNull Context context,
                                                     @NonNull String value) {
    return PermissionChecker.checkSelfPermission(context, value)
        == PermissionChecker.PERMISSION_GRANTED;
  }

  public static boolean hasTargetPermission(@NonNull Context contMetaCashValue,
                                                     @NonNull String... strings) {
    for (String sMetaCashValue : strings) {
      if (!hasPermission(contMetaCashValue, sMetaCashValue)) {
        return false;
      }
    }
    return true;
  }
}