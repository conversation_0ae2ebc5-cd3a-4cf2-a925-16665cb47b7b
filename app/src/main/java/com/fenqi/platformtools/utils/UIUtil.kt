package com.fenqi.platformtools.utils

import android.content.Context

object UIUtil {

  private var scale = 0

  fun dip2px(context: Context, dpValue: Int): Int {
    if (scale == 0) {
      scale = context.resources.displayMetrics.density.toInt()
    }
    return (dpValue * scale).toInt()
  }

  fun px2dip(context: Context, pxValue: Int): Int {
    if (scale == 0) {
      scale = context.resources.displayMetrics.density.toInt()
    }
    return (pxValue / scale).toInt()
  }
}