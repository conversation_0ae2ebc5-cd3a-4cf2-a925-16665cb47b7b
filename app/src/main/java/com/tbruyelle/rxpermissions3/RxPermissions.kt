package com.tbruyelle.rxpermissions3

import android.app.Activity
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.subjects.PublishSubject
import java.util.concurrent.ConcurrentHashMap

/**
 * 兼容原RxPermissions的API，使用Android原生权限请求
 * 保持业务逻辑完全不变
 */
class RxPermissions(private val activity: Activity) {
    
    companion object {
        private val permissionSubjects = ConcurrentHashMap<Int, PublishSubject<Boolean>>()
        private var requestCode = 1000
        
        fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
            val subject = permissionSubjects.remove(requestCode)
            if (subject != null) {
                val allGranted = grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }
                subject.onNext(allGranted)
                subject.onComplete()
            }
        }
    }
    
    fun request(vararg permissions: String): Observable<Boolean> {
        // 检查是否已经有权限
        val hasAllPermissions = permissions.all { permission ->
            ContextCompat.checkSelfPermission(activity, permission) == PackageManager.PERMISSION_GRANTED
        }
        
        if (hasAllPermissions) {
            return Observable.just(true)
        }
        
        // 创建Subject用于异步返回结果
        val subject = PublishSubject.create<Boolean>()
        val currentRequestCode = requestCode++
        permissionSubjects[currentRequestCode] = subject
        
        // 请求权限
        ActivityCompat.requestPermissions(activity, permissions, currentRequestCode)
        
        return subject
    }
}
