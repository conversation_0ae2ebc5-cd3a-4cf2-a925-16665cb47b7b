<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="com.fenqi.main.page.apply.ApplyNowViewModel" />

        <variable
            name="click"
            type="com.fenqi.main.page.apply.ApplyNowActivity" />

        <import type="com.fenqi.main.R" />

        <import type="android.view.View" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/view_title_apply_now"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/color_white"
            android:orientation="vertical">

            <View
                android:id="@+id/view_sbh"
                android:layout_width="match_parent"
                android:layout_height="30dp" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="40dp"
                    android:ellipsize="end"
                    android:text="@string/title_apply_now"
                    android:gravity="center"
                    android:lines="1"
                    android:textColor="@color/color_black"
                    android:textSize="16sp" />

                <RelativeLayout
                    android:id="@+id/view_return_back_an"
                    android:layout_width="50dp"
                    android:layout_height="match_parent">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_centerInParent="true"
                        android:background="@drawable/icon_arrow_start_black" />
                </RelativeLayout>
            </RelativeLayout>
        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/base_bg_deep"
            android:layout_weight="1">

            <androidx.core.widget.NestedScrollView
                android:id="@+id/scrollview_an"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/base_bg_deep"/>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recyclerview_ant"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:paddingHorizontal="15dp"
                            android:paddingTop="10dp"
                            tools:visibility="visible"
                            android:visibility="gone"
                            tools:itemCount="2"
                            tools:listitem="@layout/item_common_two_text2" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_marginHorizontal="15dp"
                            android:layout_height="1dp"
                            android:background="@color/base_bg_deep"/>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recyclerview_anm"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/white"
                            android:paddingHorizontal="15dp"
                            android:paddingTop="10dp"
                            tools:visibility="visible"
                            android:visibility="gone"
                            tools:itemCount="2"
                            tools:listitem="@layout/item_common_two_text2" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_marginHorizontal="15dp"
                            android:layout_height="1dp"
                            android:background="@color/base_bg_deep"/>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recyclerview_anb"
                            android:layout_width="match_parent"
                            android:background="@color/white"
                            android:layout_height="wrap_content"
                            android:paddingHorizontal="15dp"
                            android:paddingTop="10dp"
                            tools:visibility="visible"
                            android:visibility="gone"
                            tools:itemCount="2"
                            tools:listitem="@layout/item_common_two_text2" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingHorizontal="15dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="@dimen/size_mini"
                                android:textColor="@color/color_999999"
                                android:layout_marginTop="10dp"
                                android:text="@string/confirm_apply_tip"/>

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recyclerview_anf"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:background="@drawable/shape_10r_white"
                                tools:visibility="visible"
                                android:visibility="gone"
                                tools:itemCount="2"
                                tools:listitem="@layout/adapter_confirm_apply" />

                            <TextView
                                android:id="@+id/view_button_os"
                                android:layout_width="match_parent"
                                android:layout_height="50dp"
                                android:layout_marginTop="20dp"
                                android:background="@drawable/select_button_main"
                                android:gravity="center"
                                android:onClick="@{()->click.clickOrderSubmit()}"
                                android:text="@string/next"
                                android:layout_marginBottom="50dp"
                                android:textColor="@color/color_config_text_main"
                                android:textSize="@dimen/size_body" />
                        </LinearLayout>

                    </LinearLayout>
                </LinearLayout>
            </androidx.core.widget.NestedScrollView>
        </RelativeLayout>


    </LinearLayout>

</layout>