<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="com.fenqi.main.page.authinfo.bindbank.cardlist.BankCardListViewModel" />
        <variable
            name="click"
            type="com.fenqi.main.page.authinfo.bindbank.cardlist.BankCardListActivity" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:background="@color/base_bg"
        android:orientation="vertical"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_height="0dp"
            android:layout_weight="1">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerview_bcl"
                android:layout_width="match_parent"
                android:layout_marginTop="10dp"
                android:layout_marginHorizontal="10dp"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </LinearLayout>

</layout>