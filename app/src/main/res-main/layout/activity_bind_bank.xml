<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="viewModel"
            type="com.fenqi.main.page.authinfo.bindbank.BindBankViewModel" />
        <variable
            name="click"
            type="com.fenqi.main.page.authinfo.bindbank.BindBankActivity" />
    </data>

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:background="@color/color_white"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_weight="1"
            android:layout_height="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingHorizontal="20dp"
                android:orientation="vertical">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview_bb"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"/>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/color_white"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/base_bg"/>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginHorizontal="20dp"
                android:layout_marginBottom="20dp"
                android:textSize="@dimen/size_body"
                android:textColor="@color/text_config_color"
                android:text="@string/next"
                android:gravity="center"
                android:onClick="@{()->click.clickSubmit()}"
                android:background="@drawable/select_button_main"
                android:layout_gravity="center_horizontal"/>
        </LinearLayout>

    </LinearLayout>
</layout>