<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="viewModel"
            type="com.fenqi.main.page.authinfo.bindbank.bindnew.BindBankNewViewModel" />
        <variable
            name="click"
            type="com.fenqi.main.page.authinfo.bindbank.bindnew.BindBankNewActivity" />
        <import type="android.view.View"/>
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:background="@color/base_bg"
        android:layout_height="match_parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:paddingHorizontal="15dp"
            android:background="@color/color_auth_bg"
            android:layout_height="@dimen/auth_title_height">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="@dimen/size_mini"
                android:layout_centerVertical="true"
                android:text="@string/auth_title_warning"
                android:textColor="@color/color_auth_title"/>

            <ImageView
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_marginTop="12dp"
                android:background="@drawable/gantanhao_warning"/>
        </RelativeLayout>

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/auth_title_height"
            android:layout_height="match_parent">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_weight="1"
                android:layout_height="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="15dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingVertical="15dp"
                            android:background="@drawable/shape_10r_white"
                            android:paddingHorizontal="15dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="100dp"
                                android:layout_height="wrap_content"
                                android:background="@null"
                                android:text="@string/bank_number_add"
                                android:textSize="@dimen/size_body"
                                android:textColor="@color/color_black"/>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:layout_marginTop="5dp"
                                android:gravity="center_vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:background="@null"
                                    android:text="@string/bank_number_add_tip"
                                    android:textSize="@dimen/size_mini"
                                    android:textColor="@color/color_999999"/>

                                <ImageView
                                    android:layout_width="20dp"
                                    android:layout_height="20dp"
                                    android:background="@drawable/gantanhao"/>
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="15dp"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="100dp"
                                        android:layout_height="wrap_content"
                                        android:background="@null"
                                        android:text="@string/bank_number"
                                        android:textSize="@dimen/size_sm"
                                        android:textColor="@color/color_black"
                                        android:textColorHint="@color/color_999999"/>

                                    <RelativeLayout
                                        android:layout_width="0dp"
                                        android:layout_weight="1"
                                        android:layout_height="55dp"
                                        tools:ignore="RtlSymmetry">

                                        <EditText
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:background="@null"
                                            android:inputType="number"
                                            android:text="@={viewModel.bankNumber}"
                                            android:hint="@string/pls_bank_number"
                                            android:gravity="center_vertical"
                                            android:maxLength="30"
                                            android:textSize="@dimen/size_sm"
                                            android:textColor="@color/color_black"
                                            android:textColorHint="@color/color_999999" />

                                        <RelativeLayout
                                            android:layout_width="50dp"
                                            android:layout_alignParentEnd="true"
                                            android:onClick="@{()->click.clickCloseInput()}"
                                            android:visibility="@{viewModel.showClose?View.VISIBLE:View.GONE}"
                                            android:layout_height="match_parent">

                                            <ImageView
                                                android:layout_width="20dp"
                                                android:layout_height="20dp"
                                                android:layout_centerVertical="true"
                                                android:layout_alignParentEnd="true"
                                                android:background="@drawable/close_input"/>
                                        </RelativeLayout>
                                    </RelativeLayout>
                                </LinearLayout>

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="1dp"
                                    android:background="@color/base_bg"/>
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:onClick="@{()->click.clickGetCardBin()}"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="100dp"
                                        android:layout_height="wrap_content"
                                        android:background="@null"
                                        android:text="@string/bank_name"
                                        android:textSize="@dimen/size_sm"
                                        android:textColor="@color/color_black"
                                        android:textColorHint="@color/color_999999"/>

                                    <RelativeLayout
                                        android:layout_width="0dp"
                                        android:layout_weight="1"
                                        android:layout_height="55dp"
                                        tools:ignore="RtlSymmetry">

                                        <TextView
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:background="@null"
                                            android:text="@={viewModel.bankName}"
                                            android:hint="@string/pls_bank_name"
                                            android:gravity="center_vertical"
                                            android:layout_marginEnd="20dp"
                                            android:textSize="@dimen/size_sm"
                                            android:textColor="@color/color_black"
                                            android:textColorHint="@color/color_999999"/>

                                    </RelativeLayout>

                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:orientation="horizontal"
                                        android:gravity="center_vertical">

                                        <ImageView
                                            android:layout_width="30dp"
                                            android:layout_height="30dp"
                                            app:set_bank_logo="@{viewModel.bankLogo}"/>

                                        <ImageView
                                            android:layout_width="16dp"
                                            android:layout_height="16dp"
                                            android:layout_gravity="center_vertical"
                                            android:background="@drawable/arrow_right_grey"/>
                                    </LinearLayout>

                                </LinearLayout>

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="1dp"
                                    android:background="@color/base_bg"/>
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="100dp"
                                        android:layout_height="wrap_content"
                                        android:background="@null"
                                        android:text="@string/bank_phone_number"
                                        android:textSize="@dimen/size_sm"
                                        android:textColor="@color/color_black"
                                        android:textColorHint="@color/color_999999"/>

                                    <RelativeLayout
                                        android:layout_width="0dp"
                                        android:layout_weight="1"
                                        android:layout_height="55dp"
                                        tools:ignore="RtlSymmetry">

                                        <EditText
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:inputType="phone"
                                            android:background="@null"
                                            android:text="@={viewModel.bankPhoneNumber}"
                                            android:hint="@string/pls_bank_phone_number"
                                            android:maxLength="11"
                                            android:gravity="center_vertical"
                                            android:textSize="@dimen/size_sm"
                                            android:textColor="@color/color_black"
                                            android:textColorHint="@color/color_999999" />

                                    </RelativeLayout>
                                </LinearLayout>

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="1dp"
                                    android:background="@color/base_bg"/>
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:layout_width="100dp"
                                        android:layout_height="wrap_content"
                                        android:background="@null"
                                        android:text="@string/bank_verify_code"
                                        android:textSize="@dimen/size_sm"
                                        android:textColor="@color/color_black"
                                        android:textColorHint="@color/color_999999"/>

                                    <RelativeLayout
                                        android:layout_width="0dp"
                                        android:layout_weight="1"
                                        android:layout_height="55dp"
                                        tools:ignore="RtlSymmetry">

                                        <EditText
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:background="@null"
                                            android:maxLength="6"
                                            android:inputType="number"
                                            android:text="@={viewModel.bankVerifyCode}"
                                            android:gravity="center_vertical"
                                            android:textSize="@dimen/size_sm"
                                            android:hint="@string/pls_bank_verify_code"
                                            android:textColor="@color/color_black"
                                            android:textColorHint="@color/color_999999" />

                                    </RelativeLayout>

                                    <RelativeLayout
                                        android:id="@+id/view_bbov_container"
                                        android:layout_width="70dp"
                                        android:layout_height="35dp"
                                        android:paddingHorizontal="15dp"
                                        android:layout_gravity="center_vertical"
                                        android:onClick="@{()->click.clickSendCode()}">

                                        <com.fenqi.main.view.OtpView3
                                            android:id="@+id/otpview_bbn"
                                            android:layout_width="wrap_content"
                                            android:layout_centerVertical="true"
                                            android:layout_height="wrap_content"
                                            android:layout_alignParentEnd="true"
                                            app:ov_default_text_size="12sp"
                                            app:ov_text_color="@color/color_white"
                                            app:ov_time_color="@color/color_grey_bcbdbf" />
                                    </RelativeLayout>
                                </LinearLayout>

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="1dp"
                                    android:background="@color/base_bg"/>
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="@color/base_bg"/>

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="50dp"
                                android:layout_marginHorizontal="20dp"
                                android:layout_marginBottom="20dp"
                                android:layout_marginTop="20dp"
                                android:textSize="@dimen/size_body"
                                android:textColor="@color/color_config_text_main"
                                android:text="@string/next"
                                android:gravity="center"
                                android:onClick="@{()->click.clickSubmit()}"
                                android:background="@drawable/select_button_main"
                                android:layout_gravity="center_horizontal"/>
                        </LinearLayout>
                    </LinearLayout>


                </LinearLayout>

            </androidx.core.widget.NestedScrollView>



        </LinearLayout>
    </RelativeLayout>


</layout>