<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="viewModel"
            type="com.fenqi.main.page.authinfo.emergencycontact.EmergencyContactViewModel" />
        <variable
            name="click"
            type="com.fenqi.main.page.authinfo.emergencycontact.EmergencyContactActivity" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:background="@color/base_bg"
        android:layout_height="match_parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:paddingHorizontal="15dp"
            android:background="@color/color_auth_bg"
            android:layout_height="@dimen/auth_title_height">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="@dimen/size_mini"
                android:layout_centerVertical="true"
                android:text="@string/auth_title_warning"
                android:textColor="@color/color_auth_title"/>

            <ImageView
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_marginTop="12dp"
                android:background="@drawable/gantanhao_warning"/>
        </RelativeLayout>

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/auth_title_height"
            android:layout_height="match_parent">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_weight="1"
                android:layout_height="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingHorizontal="10dp"
                    android:orientation="vertical">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerview_ec"
                        android:layout_width="match_parent"
                        android:layout_marginTop="10dp"
                        android:layout_height="match_parent"/>

                </LinearLayout>
            </androidx.core.widget.NestedScrollView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginHorizontal="20dp"
                    android:layout_marginBottom="20dp"
                    android:textSize="@dimen/size_body"
                    android:textColor="@color/color_config_text_main"
                    android:text="@string/next"
                    android:gravity="center"
                    android:onClick="@{()->click.clickSubmit()}"
                    android:background="@drawable/select_button_main"
                    android:layout_gravity="center_horizontal"/>
            </LinearLayout>

        </LinearLayout>
    </RelativeLayout>


</layout>