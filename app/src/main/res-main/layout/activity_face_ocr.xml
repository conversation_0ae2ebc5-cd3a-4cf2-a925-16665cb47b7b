<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="viewModel"
            type="com.fenqi.main.page.authinfo.faceocr.FaceOcrViewModel" />
        <variable
            name="click"
            type="com.fenqi.main.page.authinfo.faceocr.FaceOcrActivity" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:paddingHorizontal="15dp"
            android:background="@color/color_auth_bg"
            android:layout_height="@dimen/auth_title_height">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="@dimen/size_mini"
                android:layout_centerVertical="true"
                android:text="@string/auth_title_warning"
                android:textColor="@color/color_auth_title"/>

            <ImageView
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_marginTop="12dp"
                android:background="@drawable/gantanhao_warning"/>
        </RelativeLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:background="@color/base_bg"
            android:layout_marginTop="@dimen/auth_title_height"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@color/color_white"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/view_fco"
                    android:layout_width="match_parent"
                    android:layout_marginTop="5dp"
                    android:orientation="vertical"
                    android:layout_height="match_parent">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="150dp"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/i_top_bg"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal">

                            <RelativeLayout
                                android:layout_width="0dp"
                                android:layout_weight="1"
                                android:onClick="@{()->click.clickFront()}"
                                android:layout_height="match_parent">

                                <LinearLayout
                                    android:id="@+id/view_fbc"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_centerInParent="true"
                                    android:orientation="vertical"
                                    android:gravity="center_horizontal">

                                    <ImageView
                                        android:layout_width="100dp"
                                        android:layout_height="70dp"
                                        android:background="@drawable/identiy_front"/>

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textSize="@dimen/size_sm"
                                        android:textColor="@color/color_black"
                                        android:layout_marginTop="10dp"
                                        android:text="@string/face_card_no_front_tip"/>
                                </LinearLayout>

                                <ImageView
                                    android:id="@+id/img_fs"
                                    android:layout_width="match_parent"
                                    android:layout_marginEnd="15dp"
                                    android:layout_marginVertical="20dp"
                                    android:layout_marginStart="20dp"
                                    android:layout_height="match_parent"/>
                            </RelativeLayout>

                            <RelativeLayout
                                android:layout_width="0dp"
                                android:onClick="@{()->click.clickBack()}"
                                android:layout_weight="1"
                                android:layout_height="match_parent">

                                <LinearLayout
                                    android:id="@+id/view_bbc"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:layout_centerInParent="true"
                                    android:gravity="center_horizontal">

                                    <ImageView
                                        android:layout_width="100dp"
                                        android:layout_height="70dp"
                                        android:background="@drawable/identiy_back"/>

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:textSize="@dimen/size_sm"
                                        android:textColor="@color/color_black"
                                        android:layout_marginTop="10dp"
                                        android:text="@string/face_card_no_back_tip"/>
                                </LinearLayout>


                                <ImageView
                                    android:id="@+id/img_bs"
                                    android:layout_width="match_parent"
                                    android:layout_marginEnd="15dp"
                                    android:layout_marginVertical="20dp"
                                    android:layout_marginStart="20dp"
                                    android:layout_height="match_parent"/>
                            </RelativeLayout>
                        </LinearLayout>
                    </RelativeLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="20dp"
                        android:textColor="@color/color_444444"
                        android:textSize="14sp"
                        android:text="@string/identity_card_desc"/>

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="70dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="15dp"
                        android:background="@drawable/i_center_tip"/>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:paddingHorizontal="17dp"
                        android:layout_height="wrap_content"
                        tools:visibility="visible"
                        android:layout_marginTop="30dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textStyle="bold"
                            android:textColor="@color/color_black"
                            android:textSize="16sp"
                            android:text="@string/identity_info"/>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/color_error"
                            android:textSize="@dimen/size_mini"
                            android:text="@string/identity_info_tip"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="50dp"
                            android:orientation="horizontal"
                            android:layout_marginTop="15dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/color_444444"
                                android:textSize="14sp"
                                android:text="@string/name"/>

                            <EditText
                                android:layout_width="0dp"
                                android:layout_weight="1"
                                android:layout_height="match_parent"
                                android:background="@null"
                                android:gravity="end|center_vertical"
                                android:text="@={viewModel.realName}"
                                android:paddingHorizontal="10dp"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:hint="@string/face_card_no_real_name_tip"
                                android:textColor="@color/color_config_main"
                                android:textColorHint="@color/color_d2d2d2" />
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/base_bg_deep"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="50dp"
                            android:layout_marginTop="5dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/color_444444"
                                android:textSize="14sp"
                                android:text="@string/id_card_number"/>

                            <EditText
                                android:layout_width="0dp"
                                android:layout_weight="1"
                                android:layout_height="match_parent"
                                android:background="@null"
                                android:gravity="end|center_vertical"
                                android:paddingHorizontal="10dp"
                                android:text="@={viewModel.idCardNumber}"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:hint="@string/face_card_no_id_card_number_tip"
                                android:textColor="@color/color_config_main"
                                android:textColorHint="@color/color_d2d2d2" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="20dp">

            <TextView
                android:id="@+id/button_fos"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginHorizontal="20dp"
                android:textSize="@dimen/size_body"
                android:enabled="false"
                android:textColor="@color/color_white"
                android:onClick="@{()->click.clickNext()}"
                android:text="@string/next"
                android:gravity="center"
                android:background="@drawable/shape_5r_grey"
                android:layout_gravity="center_horizontal"/>
        </LinearLayout>


    </RelativeLayout>
</layout>