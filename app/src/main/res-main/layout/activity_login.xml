<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="loginModel"
            type="com.fenqi.main.page.login.LoginViewModel" />
        <variable
            name="loginClick"
            type="com.fenqi.main.page.login.LoginActivity" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="20dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/button_login_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="25sp"
                android:textStyle="bold"
                tools:text="@string/app_name"
                android:layout_marginTop="100dp"
                android:textColor="@color/color_black"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="13sp"
                android:layout_marginTop="45dp"
                android:textColor="@color/color_444444"
                android:text="@string/phone_number"/>

            <LinearLayout
                android:id="@+id/view_login_phone"
                android:layout_width="match_parent"
                android:layout_height="@dimen/common_input_height"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/input_login_phone"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@null"
                    android:cursorVisible="true"
                    android:hint="@string/input_phone"
                    android:inputType="phone"
                    android:maxLength="11"
                    android:paddingEnd="10dp"
                    android:text="@={loginModel.phoneNumber}"
                    android:textColor="@color/color_333333"
                    android:textColorHint="@color/color_hint"
                    android:textCursorDrawable="@drawable/input_hint_color"
                    android:textSize="22sp"
                    tools:ignore="RtlSymmetry" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/color_d2d2d2_second"/>

            <TextView
                android:id="@+id/button_submit"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="30dp"
                android:background="@drawable/shape_5r_grey"
                android:gravity="center"
                android:enabled="false"
                android:onClick="@{()->loginClick.login()}"
                android:text="@string/get_otp"
                android:textColor="@color/color_white"
                android:textSize="@dimen/size_body" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_marginTop="10dp"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/button_agreement_login"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    tools:text="         温馨提示"
                    android:textColor="@color/color_999999"
                    android:textSize="@dimen/size_mini"/>

                <RelativeLayout
                    android:id="@+id/view_agreement_cc"
                    android:layout_width="30dp"
                    android:layout_height="30dp">

                    <ImageView
                        android:id="@+id/img_agreement_check"
                        android:layout_width="15dp"
                        android:layout_marginTop="4dp"
                        android:background="@drawable/check_yes"
                        android:layout_height="15dp"/>

                </RelativeLayout>
            </RelativeLayout>
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/view_return_back"
            android:layout_width="40dp"
            android:onClick="@{()->loginClick.clickGoBack()}"
            android:layout_marginStart="20dp"
            android:layout_marginTop="50dp"
            android:layout_height="40dp">
            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:background="@drawable/close_grey_2"/>
        </RelativeLayout>
    </RelativeLayout>

</layout>