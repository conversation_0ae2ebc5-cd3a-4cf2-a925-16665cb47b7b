<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="click"
            type="com.fenqi.main.page.main.MainActivity" />
        <variable
            name="mainModel"
            type="com.fenqi.main.page.main.MainViewModel" />
    </data>

    <RelativeLayout
        android:id="@+id/view_main_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_white">

        <FrameLayout
            android:id="@+id/view_main_fragment_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="50dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:orientation="vertical">
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/base_grey_line"/>

            <com.fenqi.main.view.MainTabView
                android:id="@+id/tab_view_main"
                android:layout_width="match_parent"
                android:layout_height="50dp"/>
        </LinearLayout>


    </RelativeLayout>
</layout>