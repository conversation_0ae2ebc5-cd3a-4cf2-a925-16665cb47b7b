<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:banner="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="com.fenqi.main.page.mine.MineActivityViewModel" />

        <variable
            name="click"
            type="com.fenqi.main.page.mine.MineFragment" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/mine_bg"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="20dp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="50dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <androidx.cardview.widget.CardView
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        app:cardBackgroundColor="@color/color_config_main"
                        app:cardCornerRadius="40dp"
                        app:cardElevation="0dp">

                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/avatar" />
                    </androidx.cardview.widget.CardView>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{viewModel.userPersonalResponseEntity.mobile}"
                            android:textColor="@color/color_black"
                            android:textSize="@dimen/size_title"
                            android:textStyle="bold"
                            tools:text="00000000" />
                    </LinearLayout>

                    <RelativeLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1">

                        <RelativeLayout
                            android:layout_width="50dp"
                            android:onClick="@{()->click.clickCustomerService()}"
                            android:layout_alignParentEnd="true"
                            android:layout_height="50dp">
                            <ImageView
                                android:layout_width="25dp"
                                android:layout_height="25dp"
                                android:layout_marginEnd="5dp"
                                android:layout_centerVertical="true"
                                android:layout_alignParentEnd="true"
                                android:background="@drawable/customer_service"/>
                        </RelativeLayout>
                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="15dp"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/shape_10r_mine_apply_now">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="#8B8C9D"
                        android:textSize="@dimen/size_mini"
                        android:text="@string/estimated_borrow"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/color_white"
                        android:textStyle="bold"
                        android:text="@{viewModel.maxAmount}"
                        android:layout_marginVertical="5dp"
                        android:textSize="25sp"
                        tools:text="80000"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="#8B8C9D"
                        android:textSize="@dimen/size_mini"
                        android:text="@string/estimated_borrow_tip"/>
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/color_black"
                    android:layout_alignParentEnd="true"
                    android:onClick="@{()->click.clickApplyNow()}"
                    android:layout_centerVertical="true"
                    android:text="@{viewModel.buttonText}"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="10dp"
                    android:background="@drawable/shape_mine_apply_now_button"
                    android:textSize="@dimen/size_sm"/>
            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingVertical="15dp"
                android:layout_marginTop="15dp"
                android:layout_marginHorizontal="20dp"
                android:background="@drawable/shape_10r_white"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:onClick="@{()->click.clickLoanRecord()}"
                    android:gravity="center_horizontal"
                    android:layout_weight="1">

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:background="@drawable/mine_wait_repay"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/color_black"
                        android:layout_marginTop="5dp"
                        android:textSize="@dimen/size_mini_l"
                        android:text="@string/wait_repay_bill"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:onClick="@{()->click.clickPaidList()}"
                    android:gravity="center_horizontal"
                    android:layout_weight="1">

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:background="@drawable/mine_repayed"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/color_black"
                        android:layout_marginTop="5dp"
                        android:textSize="@dimen/size_mini_l"
                        android:text="@string/repayment_finished_bill"/>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:onClick="@{()->click.clickMyBankCard()}"
                    android:gravity="center_horizontal"
                    android:layout_weight="1">

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:background="@drawable/mine_bank_card"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/color_black"
                        android:layout_marginTop="5dp"
                        android:textSize="@dimen/size_mini_l"
                        android:text="@string/bank_card"/>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:onClick="@{()->click.clickSetting()}"
                    android:gravity="center_horizontal"
                    android:layout_weight="1">

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:background="@drawable/mine_setting"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/color_black"
                        android:layout_marginTop="5dp"
                        android:textSize="@dimen/size_mini_l"
                        android:text="@string/title_setting"/>
                </LinearLayout>
            </LinearLayout>

            <RelativeLayout
                android:id="@+id/view_mine_banner_container"
                android:layout_width="match_parent"
                android:padding="2dp"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="15dp"
                android:background="@drawable/shape_10r_white"
                android:layout_height="80dp">

                <com.youth.banner.Banner
                    android:id="@+id/banner_mine"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    banner:banner_indicator_normal_color="@color/color_d2d2d2"
                    banner:banner_indicator_selected_color="@color/color_config_main"
                    banner:banner_radius="8dp"/>
            </RelativeLayout>
        </LinearLayout>
    </RelativeLayout>
</layout>