<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="com.fenqi.main.page.paidlist.PaidListViewModel" />

        <variable
            name="click"
            type="com.fenqi.main.page.paidlist.PaidListActivity" />

        <import type="com.fenqi.main.R"/>
        <import type="androidx.core.content.ContextCompat"/>
        <import type="android.view.View"/>
    </data>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/refreshview_repayment"
        android:background="@color/base_bg_deep"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>


        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerview_repayment"
                android:layout_width="match_parent"
                android:visibility="@{viewModel.showNoDataPaid?View.GONE:View.VISIBLE}"
                android:layout_margin="10dp"
                tools:itemCount="2"
                tools:listitem="@layout/layout_item_repayment"
                android:layout_height="match_parent"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:visibility="@{viewModel.showNoDataPaid?View.VISIBLE:View.GONE}"
                android:layout_marginTop="50dp"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="150dp"
                    android:layout_height="150dp"
                    android:background="@drawable/empty"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/size_mini"
                    android:textColor="@color/color_999999"
                    android:text="@string/no_data_repayment"/>
            </LinearLayout>
        </LinearLayout>
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</layout>