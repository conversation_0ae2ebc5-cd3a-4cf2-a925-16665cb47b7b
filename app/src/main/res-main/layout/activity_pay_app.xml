<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="click"
            type="com.fenqi.main.page.payapp.PayAppActivity" />
        <variable
            name="viewModel"
            type="com.fenqi.main.page.payapp.PayAppViewModel" />
        <import type="android.view.View"/>
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg_deep"
        android:orientation="vertical">

        <View
            android:id="@+id/view_pay_app_bar"
            android:layout_width="match_parent"
            android:layout_height="1dp"/>

        <RelativeLayout
            android:layout_width="match_parent"
            android:onClick="@{()->click.clickGoBack()}"
            android:layout_height="50dp">

            <RelativeLayout
                android:layout_width="50dp"
                android:layout_height="match_parent">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="20dp"
                    android:background="@drawable/icon_arrow_start_black" />
            </RelativeLayout>
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/view_pay_container1"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            tools:visibility="gone"
            android:paddingHorizontal="15dp"
            android:layout_weight="1">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/size_mini_l"
                android:layout_marginTop="20dp"
                android:layout_gravity="center_horizontal"
                android:textColor="@color/color_666666"
                android:text="@string/pay_amount"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:textSize="40sp"
                android:textStyle="bold"
                android:text="@{viewModel.amount}"
                android:textColor="@color/black"
                tools:text="000"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/size_mini_l"
                android:layout_marginTop="30dp"
                android:textColor="@color/color_666666"
                android:text="@string/pay_way"/>


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerview_pay_app"
                android:layout_width="match_parent"
                android:paddingVertical="10dp"
                tools:itemCount="3"
                android:layout_marginTop="10dp"
                android:background="@drawable/shape_10r_white"
                android:layout_height="wrap_content"
                tools:listitem="@layout/adapter_pay_bank_list"/>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_marginTop="20dp"
                android:textStyle="bold"
                android:onClick="@{()->click.pay()}"
                android:textColor="@color/color_config_home_text"
                android:textSize="@dimen/size_body"
                android:gravity="center"
                android:text="@string/confirm_pay"
                android:background="@drawable/select_button_main"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/view_pay_container2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:visibility="gone"
            android:visibility="gone"
            android:orientation="vertical"
            android:gravity="center_horizontal">

            <ImageView
                android:id="@+id/img_pay_ing"
                android:layout_width="50dp"
                android:layout_marginTop="30dp"
                android:layout_height="50dp"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:textSize="@dimen/size_sm"
                android:text="@string/pay_get_status"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/view_pay_container3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:visibility="visible"
            android:visibility="gone"
            android:orientation="vertical"
            android:gravity="center_horizontal">

            <ImageView
                android:id="@+id/img_pay_finished_status"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:layout_marginTop="30dp"
                android:background="@drawable/success"/>

            <TextView
                android:id="@+id/text_pay_finished_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:text="@{viewModel.payFinishedContent}"
                android:textColor="@color/color_success"
                android:textSize="@dimen/size_sm"
                tools:text="@string/pay_success"/>

            <TextView
                android:id="@+id/text_return_back"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_marginTop="20dp"
                android:layout_marginHorizontal="50dp"
                android:textStyle="bold"
                android:onClick="@{()->click.clickGoBack()}"
                android:textColor="@color/color_config_home_text"
                android:textSize="@dimen/size_body"
                android:gravity="center"
                android:text="@string/back"
                android:background="@drawable/select_button_main"/>
        </LinearLayout>
    </LinearLayout>
</layout>