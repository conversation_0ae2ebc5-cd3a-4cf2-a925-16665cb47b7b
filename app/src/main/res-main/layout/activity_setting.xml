<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="com.fenqi.main.page.setting.SettingViewModel" />
        <variable
            name="click"
            type="com.fenqi.main.page.setting.SettingActivity" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <LinearLayout
            android:orientation="vertical"
            android:padding="10dp"
            android:background="@color/base_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="20dp"
                android:background="@drawable/shape_10r_white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingVertical="3dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:onClick="@{()->click.clickRegisterPrivacy()}"
                        android:layout_height="50dp">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:gravity="center_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/user_register_privacy"
                                android:textSize="14sp"
                                android:textColor="@color/color_black"/>
                        </LinearLayout>

                        <ImageView
                            android:layout_width="15dp"
                            android:layout_height="15dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:background="@drawable/arrow_grey_end" />
                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/base_grey_line" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:onClick="@{()->click.clickPrivacyPolicy()}"
                        android:layout_height="50dp">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:gravity="center_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/user_register_privacy_policy_user"
                                android:textSize="14sp"
                                android:textColor="@color/color_black"/>
                        </LinearLayout>

                        <ImageView
                            android:layout_width="15dp"
                            android:layout_height="15dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:background="@drawable/arrow_grey_end" />
                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/base_grey_line" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:onClick="@{()->click.clickAboutUs()}"
                        android:layout_height="50dp">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:gravity="center_vertical">

                            <TextView
                                android:id="@+id/button_about_us_title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/setting_about_us"
                                android:textSize="14sp"
                                android:textColor="@color/color_black"/>
                        </LinearLayout>

                        <ImageView
                            android:layout_width="15dp"
                            android:layout_height="15dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:background="@drawable/arrow_grey_end" />
                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/base_grey_line" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:onClick="@{()->click.clickClearCache()}"
                        android:layout_height="50dp">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:gravity="center_vertical">

                            <TextView
                                android:id="@+id/button_setting_app_version"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                tools:text="版本"
                                android:textSize="14sp"
                                android:textColor="@color/color_black"/>
                        </LinearLayout>

                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/base_grey_line" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:onClick="@{()->click.clickClearCache()}"
                        android:layout_height="50dp">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:gravity="center_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/app_clear_cache"
                                android:textSize="14sp"
                                android:textColor="@color/color_black"/>
                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:text="@{viewModel.appCache}"
                            android:layout_alignParentEnd="true"
                            tools:text="0kb"
                            android:textSize="14sp"
                            android:textColor="@color/color_999999"/>
                    </RelativeLayout>

                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/select_button_main"
            android:gravity="center"
            android:textStyle="bold"
            android:layout_alignParentBottom="true"
            android:layout_marginHorizontal="30dp"
            android:onClick="@{()->click.clickSignOut()}"
            android:text="@string/sign_out"
            android:layout_marginBottom="50dp"
            android:textColor="@color/color_black"
            android:textSize="@dimen/size_body" />
    </RelativeLayout>
</layout>