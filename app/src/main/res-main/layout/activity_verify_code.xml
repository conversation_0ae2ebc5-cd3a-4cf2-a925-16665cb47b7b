<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="click"
            type="com.fenqi.main.page.verifycode.VerifyCodeActivity" />
        <variable
            name="viewModel"
            type="com.fenqi.main.page.verifycode.VerifyCodeViewModel" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:background="@color/color_white"
            android:paddingHorizontal="30dp"
            android:onClick="@{()->click.clickContainer()}"
            android:orientation="vertical"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/button_login_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="25sp"
                android:textStyle="bold"
                android:text="@string/hint_input_otp"
                android:layout_marginTop="90dp"
                android:textColor="@color/color_black"/>

            <TextView
                android:id="@+id/button_verify_code_phone_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="13sp"
                android:textColor="@color/color_999999"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginTop="20dp"
                android:onClick="@{()->click.clickInputContainer()}"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content">

                    <RelativeLayout
                        android:layout_width="50dp"
                        android:background="@drawable/shape_ring_5r_d2"
                        android:layout_height="50dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="28sp"
                            android:layout_centerInParent="true"
                            android:text="@{viewModel.otp1}"
                            android:textColor="@color/color_black"/>
                    </RelativeLayout>
                </RelativeLayout>
                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content">

                    <RelativeLayout
                        android:layout_width="50dp"
                        android:background="@drawable/shape_ring_5r_d2"
                        android:layout_height="50dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="28sp"
                            android:layout_centerInParent="true"
                            android:text="@{viewModel.otp2}"
                            android:textColor="@color/color_black"/>
                    </RelativeLayout>
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content">

                    <RelativeLayout
                        android:layout_width="50dp"
                        android:background="@drawable/shape_ring_5r_d2"
                        android:layout_height="50dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="28sp"
                            android:layout_centerInParent="true"
                            android:text="@{viewModel.otp3}"
                            android:textColor="@color/color_black"/>

                    </RelativeLayout>
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content">

                    <RelativeLayout
                        android:layout_width="50dp"
                        android:background="@drawable/shape_ring_5r_d2"
                        android:layout_height="50dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="28sp"
                            android:layout_centerInParent="true"
                            android:text="@{viewModel.otp4}"
                            android:textColor="@color/color_black"/>
                    </RelativeLayout>
                </RelativeLayout>
            </LinearLayout>

            <com.fenqi.main.view.OtpView2
                android:id="@+id/otpview_verify_code"
                android:layout_width="match_parent"
                android:gravity="center"
                android:layout_marginTop="20dp"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/button_verify_login_submit"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/shape_5r_grey"
                android:gravity="center"
                android:enabled="false"
                android:onClick="@{()->click.clickConfirm()}"
                android:text="@string/confirm"
                android:textColor="@color/color_white"
                android:textSize="@dimen/size_body" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/view_code_key_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="30dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    android:background="@drawable/select_common_white_press"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textSize="25sp"
                    android:onClick="@{()->click.clickKeyboard(1)}"
                    android:textColor="@color/color_black"
                    android:text="1"
                   />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    android:layout_weight="1"
                    android:background="@drawable/select_common_white_press"
                    android:gravity="center"
                    android:onClick="@{()->click.clickKeyboard(2)}"
                    android:textSize="25sp"
                    android:textColor="@color/color_black"
                    android:text="2"
                   />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    android:background="@drawable/select_common_white_press"
                    android:layout_weight="1"
                    android:onClick="@{()->click.clickKeyboard(3)}"
                    android:gravity="center"
                    android:textSize="25sp"
                    android:textColor="@color/color_black"
                    android:text="3"
                   />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    android:background="@drawable/select_common_white_press"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:onClick="@{()->click.clickKeyboard(4)}"
                    android:textSize="25sp"
                    android:textColor="@color/color_black"
                    android:text="4"
                   />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    android:layout_weight="1"
                    android:background="@drawable/select_common_white_press"
                    android:gravity="center"
                    android:textSize="25sp"
                    android:onClick="@{()->click.clickKeyboard(5)}"
                    android:textColor="@color/color_black"
                    android:text="5"
                   />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    android:background="@drawable/select_common_white_press"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:onClick="@{()->click.clickKeyboard(6)}"
                    android:textSize="25sp"
                    android:textColor="@color/color_black"
                    android:text="6"
                   />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    android:background="@drawable/select_common_white_press"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:onClick="@{()->click.clickKeyboard(7)}"
                    android:textSize="25sp"
                    android:textColor="@color/color_black"
                    android:text="7"
                   />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    android:background="@drawable/select_common_white_press"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:onClick="@{()->click.clickKeyboard(8)}"
                    android:textSize="25sp"
                    android:textColor="@color/color_black"
                    android:text="8"
                   />
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    android:background="@drawable/select_common_white_press"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:onClick="@{()->click.clickKeyboard(9)}"
                    android:textSize="25sp"
                    android:textColor="@color/color_black"
                    android:text="9"
                   />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    android:background="@drawable/select_common_white_press"
                    android:onClick="@{()->click.clickDownKey()}"
                    android:layout_weight="1">

                    <ImageView
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_centerInParent="true"
                        android:background="@drawable/key_down"/>
                </RelativeLayout>
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    android:layout_weight="1"
                    android:background="@drawable/select_common_white_press"
                    android:gravity="center"
                    android:onClick="@{()->click.clickKeyboard(0)}"
                    android:textSize="25sp"
                    android:textColor="@color/color_black"
                    android:text="0"
                   />

                <RelativeLayout
                    android:layout_width="0dp"
                    android:background="@drawable/select_common_white_press"
                    android:layout_height="70dp"
                    android:onClick="@{()->click.clickDelete()}"
                    android:layout_weight="1">

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="20dp"
                        android:layout_centerInParent="true"
                        android:background="@drawable/key_delete"/>
                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/view_return_back"
            android:layout_width="40dp"
            android:onClick="@{()->click.clickGoBack()}"
            android:layout_marginStart="20dp"
            android:layout_marginTop="50dp"
            android:layout_height="40dp">
            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:background="@drawable/close_grey_2"/>
        </RelativeLayout>

    </RelativeLayout>

</layout>