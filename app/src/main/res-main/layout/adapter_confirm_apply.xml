<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:id="@+id/view_adapter_confirm_apply_container"
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:paddingHorizontal="15dp"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:layout_width="match_parent"
            android:paddingVertical="15dp"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/img_adapter_confirm_apply"
                    android:layout_width="30dp"
                    android:layout_height="30dp"/>

                <TextView
                    android:id="@+id/button_adapter_confirm_apply"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/size_sm"
                    android:layout_marginStart="6dp"
                    android:textColor="@color/color_black"
                    tools:text="实名信息"/>
            </LinearLayout>
            
            <TextView
                android:id="@+id/button_adapter_confirm_apply_submit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/size_sm"
                android:layout_centerVertical="true"
                android:paddingVertical="5dp"
                android:paddingHorizontal="20dp"
                android:background="@drawable/shape_confirm_apply_button"
                android:layout_alignParentEnd="true"
                tools:text="去完成"
                android:textColor="@color/color_confirm_button"/>
        </RelativeLayout>
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/base_bg_deep"/>
    </LinearLayout>
</layout>