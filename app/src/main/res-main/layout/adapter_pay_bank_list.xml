<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="60dp">

        <RelativeLayout
            android:id="@+id/view_adapter_pay_app_bank_list"
            android:layout_width="match_parent"
            android:background="@drawable/select_common_white_press"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:paddingHorizontal="15dp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_centerVertical="true">

                <ImageView
                    android:id="@+id/image_adapter_bank_logo"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    tools:background="@drawable/avatar"/>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/txt_adapter_bank_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/size_sm"
                        android:textColor="@color/black"
                        tools:text="招商银行"/>
                    <TextView
                        android:id="@+id/txt_adapter_bank_no"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/size_mini"
                        android:textColor="@color/color_444444"
                        tools:text="6092 **** **** 7287"/>
                </LinearLayout>
            </LinearLayout>

            <ImageView
                android:id="@+id/image_adapter_check"
                android:layout_width="17dp"
                android:layout_height="17dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"/>
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginEnd="15dp"
            android:layout_marginStart="65dp"
            android:background="@color/base_grey_line"/>
    </LinearLayout>
</layout>