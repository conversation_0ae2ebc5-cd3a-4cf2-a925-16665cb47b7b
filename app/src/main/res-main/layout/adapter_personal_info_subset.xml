<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_10r_white"
        android:layout_marginBottom="10dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:paddingHorizontal="10dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/button_adapter_personal_info_subset_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@null"
                tools:text="生日"
                android:textSize="@dimen/size_sm"
                android:textColor="@color/color_black"
                android:textColorHint="@color/color_999999"/>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_weight="1"
                android:paddingStart="10dp"
                android:layout_height="55dp"
                tools:ignore="RtlSymmetry">

                <EditText
                    android:id="@+id/input_adapter_personal_info_subset"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@null"
                    android:paddingEnd="15dp"
                    android:gravity="center_vertical|end"
                    android:textSize="@dimen/size_sm"
                    android:textColor="@color/color_black"
                    android:textColorHint="@color/color_999999" />

                <TextView
                    android:id="@+id/button_adapter_personal_info_subset"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@null"
                    android:gravity="center_vertical|end"
                    android:layout_marginEnd="15dp"
                    android:textSize="@dimen/size_sm"
                    android:textColor="@color/color_black"
                    android:textColorHint="@color/color_999999"/>

                <ImageView
                    android:id="@+id/img_adapter_personal_info_subset_arrow"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_centerVertical="true"
                    android:visibility="gone"
                    android:layout_alignParentEnd="true"
                    android:background="@drawable/arrow_right_grey"/>

            </RelativeLayout>
        </LinearLayout>
    </LinearLayout>

</layout>