<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data
        android:layout_width="12dp"
        android:layout_height="12dp">

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="45dp">

        <RelativeLayout
            android:id="@+id/view_adapter_pickerview"
            android:layout_width="match_parent"
            android:background="@drawable/select_common_white_press"
            android:layout_weight="1"
            android:layout_height="0dp">

            <TextView
                android:id="@+id/tv_adapter_pickerview_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/size_body"
                android:layout_centerInParent="true"
                tools:text="未婚"
                android:textColor="@color/black"/>
        </RelativeLayout>

        <View
            android:id="@+id/line_adapter_pickerview"
            android:layout_width="match_parent"
            android:visibility="gone"
            android:background="@color/base_bg"
            android:layout_height="1dp"/>
    </LinearLayout>
</layout>