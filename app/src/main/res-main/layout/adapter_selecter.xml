<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:id="@+id/view_adapter_card_list_container"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:background="@drawable/select_f1_bg_press"
        android:layout_height="50dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_weight="1"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            android:layout_height="0dp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:orientation="horizontal"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:layout_height="wrap_content">

                <ImageView
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:id="@+id/image_adapter_card_list_logo"/>

                <TextView
                    android:id="@+id/tv_adapter_selecter"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:gravity="center_vertical"
                    android:textColor="@color/color_black"/>
            </LinearLayout>

            <ImageView
                android:layout_width="17dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_height="17dp"
                android:background="@drawable/arrow_right_black"/>

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginEnd="15dp"
            android:layout_marginStart="45dp"
            android:background="@color/base_grey_line"/>

    </LinearLayout>
</layout>