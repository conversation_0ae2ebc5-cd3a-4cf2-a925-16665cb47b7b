<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:orientation="vertical"
                android:padding="7dp">

                <RelativeLayout
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_gravity="center_horizontal">

                    <ImageView
                        android:id="@+id/img_base_load_view"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerHorizontal="true"
                        android:layout_centerVertical="true"
                        android:background="@drawable/base_loading"
                        android:scaleType="fitXY" />

                </RelativeLayout>

                <TextView
                    android:id="@+id/tv_base_load_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="5dp"
                    android:gravity="center"
                    android:textColor="@color/base_library_white"
                    android:textSize="14sp"
                    android:visibility="gone" />
            </LinearLayout>

        </RelativeLayout>


    </RelativeLayout>
</layout>