<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="click"
            type="com.fenqi.main.view.dialog.AuthBackTipDialog" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_alignParentBottom="true"
            android:background="@drawable/return_back_bg">

            <TextView
                android:id="@+id/tv_dialog_back_tip"
                android:layout_width="match_parent"
                android:layout_marginTop="40dp"
                android:textStyle="bold"
                android:layout_marginStart="20dp"
                tools:text="xxxxx"
                android:textSize="@dimen/size_body"
                android:layout_height="wrap_content"
                />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="20dp"
                android:layout_marginBottom="40dp"
                android:layout_alignParentBottom="true"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/button_dialog_cancel"
                    android:layout_width="wrap_content"
                    android:layout_height="45dp"
                    android:background="@drawable/shape_ring_5r_d2"
                    android:gravity="center"
                    android:paddingHorizontal="20dp"
                    android:text="@string/waiver_of_quota"
                    android:textColor="@color/color_999999"
                    android:textSize="@dimen/size_body" />

                <TextView
                    android:id="@+id/button_dialog_confirm"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_marginStart="10dp"
                    android:layout_height="45dp"
                    android:background="@drawable/select_button_main"
                    android:gravity="center"
                    android:text="@string/continue_input"
                    android:textColor="@color/color_config_text_main"
                    android:textSize="@dimen/size_body" />
            </LinearLayout>

        </RelativeLayout>
    </RelativeLayout>
</layout>