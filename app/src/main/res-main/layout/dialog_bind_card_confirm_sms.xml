<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="click"
            type="com.fenqi.main.view.dialog.BindCardConfirmSmsDialog" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_10r_white"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="45dp">

            <TextView
                android:id="@+id/tv_dialog_bind_card_confirm_title"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:textSize="16sp"
                android:textStyle="bold"
                android:gravity="center"
                android:text="@string/bind_card_confirm_sms_title"
                android:textColor="@color/color_black"/>

            <RelativeLayout
                android:id="@+id/view_dialog_cancel"
                android:layout_width="45dp"
                android:layout_alignParentEnd="true"
                android:layout_height="match_parent">

                <ImageView
                    android:layout_width="17dp"
                    android:layout_height="17dp"
                    android:layout_centerInParent="true"
                    android:background="@drawable/close_grey_2"/>
            </RelativeLayout>
        </RelativeLayout>

        <com.fenqi.main.view.autoinput.SplitEditTextView
            android:id="@+id/splitedit_confirm_sms"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginHorizontal="20dp"
            android:inputType="number"
            android:textColor="@color/color_black"
            android:textSize="25sp"
            app:borderColor="@color/color_d2d2d2"
            app:borderSize="1dp"
            app:contentNumber="6"
            app:contentShowMode="text"
            app:cornerSize="10dp"
            app:cursorColor="@color/color_d2d2d2"
            app:cursorWidth="2dp"
            app:inputBoxStyle="singleBox"
            app:spaceSize="10dp" />

        <com.fenqi.main.view.OtpView2
            android:id="@+id/otpview_dialog_sms_confirm"
            android:layout_width="match_parent"
            android:gravity="center"
            android:layout_marginTop="20dp"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/button_dialog_submit"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="30dp"
            android:textSize="@dimen/size_body"
            android:textColor="@color/text_config_color"
            android:text="@string/text_submit"
            android:gravity="center"
            android:background="@drawable/select_button_main"
            android:layout_gravity="center_horizontal"/>
    </LinearLayout>
</layout>