<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="click"
            type="com.fenqi.main.view.dialog.CommonMessageDialog" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_10r_white"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/size_body"
            android:layout_marginStart="15dp"
            android:textStyle="bold"
            android:layout_marginTop="15dp"
            android:text="@string/tips"
            android:textColor="@color/color_111111"/>


        <TextView
            android:id="@+id/tv_dialog_common_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:layout_marginTop="15dp"
            android:textSize="13sp"
            android:textColor="@color/color_444444"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="15dp"
            android:background="@color/base_bg"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_dialog_common_cancel"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:background="@drawable/select_10r_left_button"
                android:layout_height="match_parent"
                android:textSize="@dimen/size_body"
                android:textColor="@color/color_666666"
                android:text="@string/cancel"/>

            <View
                android:id="@+id/view_common_message_dialog_line"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/base_bg"/>

            <TextView
                android:id="@+id/tv_dialog_common_confirm"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:background="@drawable/select_10r_right"
                android:layout_height="match_parent"
                android:textSize="@dimen/size_body"
                android:textColor="@color/color_black"
                android:text="@string/confirm"/>
        </LinearLayout>

    </LinearLayout>
</layout>