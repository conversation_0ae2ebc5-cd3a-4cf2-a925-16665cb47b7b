<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="click"
            type="com.fenqi.main.view.dialog.SignOutDialog" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_10r_white"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginTop="15dp"
            android:text="@string/dialog_title_tips"
            android:textColor="@color/color_black"
            android:textSize="@dimen/size_body" />

        <TextView
            android:id="@+id/tv_content_two_button_dialog"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:layout_marginHorizontal="15dp"
            android:gravity="center_horizontal"
            android:textColor="@color/color_black"
            android:textSize="@dimen/size_sm" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="20dp"
            android:background="@color/base_grey_line" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp">

            <TextView
                android:id="@+id/button_dialog_cancel"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/shape_10r_select_log_out_cancel_left"
                android:gravity="center"
                android:text="@string/cancel"
                android:textColor="@color/color_666666"
                android:textSize="@dimen/size_sm" />

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/base_grey_line" />

            <TextView
                android:id="@+id/button_dialog_confirm"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/shape_10r_select_log_out_cancel_right"
                android:gravity="center"
                android:text="@string/confirm"
                android:textColor="@color/color_black"
                android:textSize="@dimen/size_sm" />

        </LinearLayout>
    </LinearLayout>
</layout>