<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="click"
            type="com.fenqi.main.page.main.view.AuthTipDialog" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_alignParentBottom="true"
            android:layout_height="wrap_content">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="40dp"
                    android:background="@drawable/shape_20r_top_white"
                    android:orientation="vertical">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="70dp"
                        android:background="@drawable/shape_grante_home_auth_tip"/>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="vertical"
                    android:layout_marginTop="30dp"
                    android:padding="30dp"
                    android:background="@drawable/shape_20r_top_white"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tv_dialog_home_auth_tip_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/size_body"
                        tools:text="欢迎来到"/>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="50dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="@dimen/size_sm"
                            android:text="@string/estimated_borrow2"
                            android:textColor="@color/color_999999"/>

                        <ImageView
                            android:layout_width="17dp"
                            android:layout_marginTop="2dp"
                            android:layout_marginStart="5dp"
                            android:layout_gravity="center_vertical"
                            android:layout_height="17dp"
                            android:background="@drawable/gantanhao_grey_small"/>
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_main_home_auth_tip_max_amount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textStyle="bold"
                        android:textColor="@color/color_config_home_text"
                        android:textSize="45sp"
                        tools:text="80,000" />

                    <TextView
                        android:id="@+id/tv_home_auth_tip_submit_button"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_marginTop="60dp"
                        android:textSize="@dimen/size_body"
                        android:textColor="@color/color_config_text_main"
                        android:gravity="center"
                        android:background="@drawable/select_button_main"
                        android:layout_gravity="center_horizontal"/>

                    <TextView
                        android:id="@+id/tv_home_auth_tip_cant_borrow_now"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingVertical="15dp"
                        android:gravity="center"
                        android:textSize="@dimen/size_mini_l"
                        android:textColor="@color/color_999999"
                        android:text="@string/now_cant_borrow"/>
                </LinearLayout>

                <ImageView
                    android:layout_width="130dp"
                    android:layout_height="130dp"
                    android:layout_marginEnd="20dp"
                    android:layout_alignParentEnd="true"
                    android:background="@drawable/paobu"/>
            </RelativeLayout>
        </LinearLayout>
    </RelativeLayout>


</layout>