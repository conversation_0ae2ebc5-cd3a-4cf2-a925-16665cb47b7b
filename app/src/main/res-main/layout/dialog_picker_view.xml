<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="click"
            type="com.fenqi.main.view.dialog.CommonMessageDialog" />
    </data>

    <LinearLayout
        android:id="@+id/view_dialog_pickerview_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="10dp"
        android:background="@drawable/shape_10r_white"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="50dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/size_body"
                android:textColor="@color/black"
                android:layout_centerInParent="true"
                android:text="@string/pls_select"/>

            <RelativeLayout
                android:id="@+id/view_dialog_pickerview_close"
                android:layout_width="50dp"
                android:layout_alignParentEnd="true"
                android:layout_height="50dp">

                <ImageView
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_centerInParent="true"
                    android:background="@drawable/close_grey_2"/>
            </RelativeLayout>
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/base_bg"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerview_adapter_pickerview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

    </LinearLayout>
</layout>