<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="click"
            type="com.fenqi.main.view.dialog.TakePhotoSelecterDialog" />
    </data>

    <RelativeLayout
        android:id="@+id/view_dialog_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="20dp"
            android:paddingHorizontal="10dp"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/button_dialog_take_photo"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:background="@drawable/select_take_photo"
                android:paddingHorizontal="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:layout_gravity="center_horizontal"
                    android:text="@string/avatar_take_pictures"
                    android:textAllCaps="false"
                    android:textColor="@color/color_black"
                    android:textSize="@dimen/size_sm" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/button_dialog_select_image"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="5dp"
                android:background="@drawable/select_take_photo"
                android:paddingHorizontal="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_horizontal"
                    android:gravity="center"
                    android:text="@string/avatar_album"
                    android:textAllCaps="false"
                    android:textColor="@color/color_black"
                    android:textSize="@dimen/size_sm" />
            </LinearLayout>

            <TextView
                android:id="@+id/button_dialog_cancel"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="10dp"
                android:background="@drawable/select_take_photo"
                android:gravity="center"
                android:text="@string/all_cancel"
                android:textAllCaps="false"
                android:textColor="@color/color_777777"
                android:textSize="@dimen/size_sm" />
        </LinearLayout>
    </RelativeLayout>
</layout>