<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:banner="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="click"
            type="com.fenqi.main.page.main.fragment.home.HomeFragment" />

        <variable
            name="viewModel"
            type="com.fenqi.main.page.main.fragment.home.HomeFragmentViewModel" />

        <import type="android.view.View" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/base_bg">

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/home_main_bg" />

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshview_main_home"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.scwang.smart.refresh.header.ClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    tools:ignore="ScrollViewSize">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="50dp"
                                android:layout_marginHorizontal="15dp"
                                android:layout_marginTop="35dp"
                                android:orientation="horizontal">

                                <RelativeLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content">

                                    <TextView
                                        android:id="@+id/tv_main_home_app_name"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentBottom="true"
                                        android:text="@string/app_name"
                                        android:textColor="@color/color_config_home_title_text"
                                        android:textSize="25sp" />
                                </RelativeLayout>

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="bottom"
                                    android:layout_marginStart="5dp"
                                    android:layout_marginBottom="2dp"
                                    android:text="@string/home_title_sub"
                                    android:textColor="@color/color_config_home_title_text"
                                    android:textSize="12sp" />

                                <RelativeLayout
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1">

                                    <RelativeLayout
                                        android:layout_width="50dp"
                                        android:layout_height="match_parent"
                                        android:layout_alignParentEnd="true"
                                        android:onClick="@{()->click.clickCustomerService()}">

                                        <ImageView
                                            android:layout_width="25dp"
                                            android:layout_height="25dp"
                                            android:layout_alignParentEnd="true"
                                            android:layout_alignParentBottom="true"
                                            android:layout_marginBottom="5dp"
                                            android:background="@drawable/customer_service_home" />
                                    </RelativeLayout>
                                </RelativeLayout>
                            </LinearLayout>

                            <androidx.cardview.widget.CardView
                                android:id="@+id/view_home_marqueeview_container"
                                android:layout_width="match_parent"
                                android:layout_height="45dp"
                                android:layout_marginHorizontal="15dp"
                                android:layout_marginTop="20dp"
                                app:cardCornerRadius="10dp"
                                app:cardElevation="0dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@drawable/shape_home_status_container"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal"
                                    android:paddingHorizontal="10dp"
                                    android:paddingVertical="10dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:background="@drawable/shape_home_message_warning"
                                        android:paddingHorizontal="5dp"
                                        android:text="@string/announcement"
                                        android:textColor="@color/white"
                                        android:textSize="@dimen/size_mini" />

                                    <LinearLayout
                                        android:layout_width="0dp"
                                        android:layout_height="match_parent"
                                        android:layout_weight="1"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal">

                                        <com.fenqi.main.view.marqueeview.MarqueeView
                                            android:id="@+id/marqueeview_home"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:layout_marginStart="7dp"
                                            app:mvAnimDuration="1000"
                                            app:mvDirection="bottom_to_top"
                                            app:mvInterval="3000"
                                            app:mvSingleLine="true"
                                            app:mvTextColor="@color/black"
                                            app:mvTextSize="12sp" />

                                    </LinearLayout>
                                </LinearLayout>
                            </androidx.cardview.widget.CardView>

                            <LinearLayout
                                android:id="@+id/linear_main_home_container"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_margin="15dp"
                                android:orientation="vertical" />


                            <LinearLayout
                                android:id="@+id/view_refuse_borrow_other"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:visibility="gone"
                                android:layout_marginBottom="10dp"
                                tools:visibility="visible"
                                android:orientation="vertical">

                                <RelativeLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:layout_marginHorizontal="15dp"
                                    android:background="@drawable/shape_10r_white">

                                    <androidx.cardview.widget.CardView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        app:cardElevation="0dp"
                                        app:cardBackgroundColor="@color/white"
                                        app:cardCornerRadius="8dp">

                                        <ImageView
                                            android:id="@+id/image_refuse_borrow_other"
                                            android:layout_width="match_parent"
                                            android:adjustViewBounds="true"
                                            android:scaleType="fitXY"
                                            android:layout_height="wrap_content"/>
                                    </androidx.cardview.widget.CardView>
                                </RelativeLayout>

                            </LinearLayout>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/platform_advantage"
                                android:layout_gravity="center_horizontal"
                                android:textColor="@color/color_black"
                                android:textSize="@dimen/size_sm"
                                android:textStyle="bold" />

                            <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="125dp"
                                android:layout_marginHorizontal="15dp"
                                android:layout_marginTop="5dp"
                                android:background="@drawable/home_bottom_tip" />

                            <RelativeLayout
                                android:id="@+id/view_home_banner_container"
                                android:layout_width="match_parent"
                                android:padding="2dp"
                                android:layout_marginHorizontal="15dp"
                                android:layout_marginTop="20dp"
                                android:background="@drawable/shape_10r_white"
                                android:layout_height="80dp">

                                <com.youth.banner.Banner
                                    android:id="@+id/banner_home"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    banner:banner_indicator_normal_color="@color/color_d2d2d2"
                                    banner:banner_indicator_selected_color="@color/color_config_main"
                                    banner:banner_radius="8dp"/>
                            </RelativeLayout>


                            <View
                                android:layout_width="match_parent"
                                android:layout_height="30dp" />
                        </LinearLayout>
                    </RelativeLayout>
                </LinearLayout>
            </ScrollView>
        </com.scwang.smart.refresh.layout.SmartRefreshLayout>
    </RelativeLayout>
</layout>