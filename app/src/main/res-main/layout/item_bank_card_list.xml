<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_marginBottom="10dp"
        android:padding="15dp"
        android:orientation="horizontal"
        android:background="@drawable/shape_grante_card_list"
        android:layout_height="wrap_content">

        <androidx.cardview.widget.CardView
            android:layout_width="40dp"
            app:cardCornerRadius="30dp"
            android:background="@drawable/shape_bigr_white"
            android:layout_height="40dp">

            <ImageView
                android:id="@+id/image_adapter_bank_logo"
                android:layout_width="40dp"
                android:layout_centerInParent="true"
                android:layout_height="40dp"/>
        </androidx.cardview.widget.CardView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_arbcl_card_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="银行"
                android:textSize="@dimen/size_title"
                android:textStyle="bold"
                android:textColor="@color/color_white"/>

            <TextView
                android:id="@+id/tv_arbcl_card_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="@string/next"
                android:textSize="@dimen/size_mini"
                android:textColor="@color/color_white"/>

            <RelativeLayout
                android:layout_width="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="15dp"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_arbcl_card_no"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"

                    tools:text="0192729172272928"
                    android:layout_centerVertical="true"
                    android:textSize="20sp"
                    android:textColor="@color/color_white"/>
            </RelativeLayout>

        </LinearLayout>

    </LinearLayout>
</layout>