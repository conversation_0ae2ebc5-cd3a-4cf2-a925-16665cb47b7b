<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:id="@+id/view_item_common_two_text_container"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_marginBottom="15dp"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_common_space_two_start"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                tools:text="start"
                android:textColor="@color/color_999999"
                android:textSize="@dimen/size_sm" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_common_space_two_end"
                    android:layout_width="wrap_content"
                    tools:text="end"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/size_sm"
                    android:textColor="@color/black" />

                <ImageView
                    android:id="@+id/image_arrow_item_common_two_text"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:visibility="gone"
                    android:layout_marginStart="5dp"
                    android:background="@drawable/arrow_right_black"/>
            </LinearLayout>

        </RelativeLayout>
    </LinearLayout>
</layout>