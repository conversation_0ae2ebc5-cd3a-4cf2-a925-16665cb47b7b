<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="match_parent"
    android:layout_height="50dp"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/text_base_title"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="40dp"
        android:lines="1"
        android:ellipsize="end"
        tools:text="AndroidStart"
        android:textColor="@color/base_title_color"
        android:textSize="16sp"
        android:gravity="center" />

    <RelativeLayout
        android:id="@+id/view_base_title_return_back"
        android:layout_width="50dp"
        android:layout_height="match_parent">

      <ImageView
          android:layout_width="20dp"
          android:layout_height="20dp"
          android:layout_marginStart="20dp"
          android:layout_centerVertical="true"
          android:background="@drawable/icon_arrow_start_black"/>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/view_base_title_end_container"
        android:layout_width="wrap_content"
        android:layout_alignParentEnd="true"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_marginEnd="20dp"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/text_base_title_end_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/size_sm"
            android:paddingHorizontal="15dp"
            android:paddingVertical="3dp"
            android:layout_centerVertical="true"
            android:background="@drawable/shape_4r_main_ring"
            android:textColor="@color/color_config_main"
            tools:text="重新操作"/>
    </RelativeLayout>
</RelativeLayout>