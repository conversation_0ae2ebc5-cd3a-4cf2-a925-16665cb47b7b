<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginTop="10dp"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_common_space_two_start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        tools:text="start"
        android:textColor="@color/color_black"
        android:textSize="@dimen/size_sm" />

    <TextView
        android:id="@+id/tv_common_space_two_end"
        android:layout_width="wrap_content"
        android:layout_alignParentEnd="true"
        tools:text="end"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:textSize="@dimen/size_sm"
        android:textColor="@color/color_999999"/>
</RelativeLayout>