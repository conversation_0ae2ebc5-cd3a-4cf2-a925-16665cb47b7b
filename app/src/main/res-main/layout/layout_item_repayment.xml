<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp">

            <LinearLayout
                android:id="@+id/view_item_repayment_container"
                android:layout_width="match_parent"
                android:orientation="vertical"
                android:padding="10dp"
                android:background="@drawable/shape_10r_white_shadow"
                android:layout_height="wrap_content">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_centerVertical="true"
                        android:gravity="center_vertical">

                        <ImageView
                            android:id="@+id/image_ar_repayment_icon"
                            android:layout_width="25dp"
                            android:background="@drawable/repay_bill"
                            android:layout_height="25dp"/>

                        <TextView
                            android:id="@+id/tv_ar_wait_for_pay_amount_repayment"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textStyle="bold"
                            android:layout_marginStart="5dp"
                            tools:text="0000000"
                            android:textSize="@dimen/size_sm"
                            android:textColor="@color/color_black"/>
                    </LinearLayout>
                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_marginHorizontal="9dp"
                        android:background="@drawable/shape_dot_1"/>

                    <TextView
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/size_mini"
                        android:layout_marginStart="5dp"
                        android:textColor="@color/color_black"
                        android:text="@string/bill_terms"/>

                    <TextView
                        android:id="@+id/tv_ar_order_issue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="第一期"
                        android:textSize="@dimen/size_mini"
                        android:textColor="@color/color_999999"/>


                    <RelativeLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1">

                        <TextView
                            android:id="@+id/tv_ar_start_time"
                            android:layout_width="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_height="wrap_content"
                            tools:text="第一期"
                            android:textSize="@dimen/size_mini"
                            android:textColor="@color/color_black"/>
                    </RelativeLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_marginHorizontal="9dp"
                        android:background="@drawable/shape_dot_2"/>

                    <TextView
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/size_mini"
                        android:layout_marginStart="5dp"
                        android:textColor="@color/color_black"
                        android:text="@string/bill_cycle"/>

                    <TextView
                        android:id="@+id/tv_ar_bill_cycle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="2020-20-20-2024-20-20"
                        android:textSize="@dimen/size_mini"
                        android:textColor="@color/color_999999"/>

                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_marginHorizontal="9dp"
                        android:background="@drawable/shape_dot_3"/>

                    <TextView
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/size_mini"
                        android:layout_marginStart="5dp"
                        android:textColor="@color/color_black"
                        android:text="@string/bill_repayment_amount"/>

                    <TextView
                        android:id="@+id/tv_ar_bill_repayment_amount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="2020-20-20-2024-20-20"
                        android:textSize="@dimen/size_mini"
                        android:textColor="@color/color_999999"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_marginHorizontal="9dp"
                        android:background="@drawable/shape_dot_4"/>

                    <TextView
                        android:id="@+id/tv_adapter_repayment_expiration_title"
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/size_mini"
                        android:layout_marginStart="5dp"
                        android:textColor="@color/color_black"
                        tools:text="@string/bill_repayment_amount"/>

                    <TextView
                        android:id="@+id/tv_adapter_repayment_expiration_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="2020-20-20-2024-20-20"
                        android:textSize="@dimen/size_mini"
                        android:textColor="@color/color_999999"/>
                </LinearLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_marginTop="5dp"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tv_ar_button_repayment_status"
                        android:layout_width="wrap_content"
                        android:minWidth="90dp"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:layout_alignParentEnd="true"
                        android:paddingVertical="3dp"
                        android:textColor="@color/color_config_main"
                        tools:text="@string/paid_finished"
                        android:background="@drawable/shape_ring_bigr_main"/>
                </RelativeLayout>
            </LinearLayout>

            <TextView
                android:id="@+id/tv_ar_order_status"
                android:layout_width="wrap_content"
                android:layout_alignParentEnd="true"
                android:paddingHorizontal="20dp"
                android:paddingVertical="2dp"
                android:background="@drawable/shape_repayment_status_light_blue"
                android:layout_height="wrap_content"
                tools:text="待还款"
                android:textSize="@dimen/size_mini_l"
                android:textColor="@color/color_white"/>
        </RelativeLayout>


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_marginBottom="10dp"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/view_item_repayment_container_pledge"
                android:layout_width="match_parent"
                android:orientation="vertical"
                android:padding="10dp"
                android:background="@drawable/shape_10r_white_shadow"
                android:layout_height="wrap_content">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:id="@+id/view_ar_order_no_pledge"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_centerVertical="true"
                        android:gravity="center_vertical">

                        <ImageView
                            android:id="@+id/image_ar_repayment_icon_pledge"
                            android:layout_width="25dp"
                            android:background="@drawable/repay_db"
                            android:layout_height="25dp"/>

                        <TextView
                            android:id="@+id/tv_ar_wait_for_pay_amount_repayment_pledge"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textStyle="bold"
                            android:layout_marginStart="5dp"
                            tools:text="0000000"
                            android:textSize="@dimen/size_sm"
                            android:textColor="@color/color_black"/>
                    </LinearLayout>

                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_marginHorizontal="9dp"
                        android:background="@drawable/shape_dot_1"/>

                    <TextView
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/size_mini"
                        android:layout_marginStart="5dp"
                        android:textColor="@color/color_black"
                        android:text="@string/bill_terms"/>

                    <TextView
                        android:id="@+id/tv_ar_order_issue_pledge"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="第一期"
                        android:textSize="@dimen/size_mini"
                        android:textColor="@color/color_999999"/>


                    <RelativeLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1">

                        <TextView
                            android:id="@+id/tv_ar_start_time_pledge"
                            android:layout_width="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_height="wrap_content"
                            tools:text="第一期"
                            android:textSize="@dimen/size_mini"
                            android:textColor="@color/color_black"/>
                    </RelativeLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_marginHorizontal="9dp"
                        android:background="@drawable/shape_dot_2"/>

                    <TextView
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/size_mini"
                        android:layout_marginStart="5dp"
                        android:textColor="@color/color_black"
                        android:text="@string/bill_cycle"/>

                    <TextView
                        android:id="@+id/tv_ar_bill_cycle_pledge"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="2020-20-20-2024-20-20"
                        android:textSize="@dimen/size_mini"
                        android:textColor="@color/color_999999"/>

                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_marginHorizontal="9dp"
                        android:background="@drawable/shape_dot_3"/>

                    <TextView
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/size_mini"
                        android:layout_marginStart="5dp"
                        android:textColor="@color/color_black"
                        android:text="@string/bill_repayment_amount"/>

                    <TextView
                        android:id="@+id/tv_ar_bill_repayment_amount_pledge"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="2020-20-20-2024-20-20"
                        android:textSize="@dimen/size_mini"
                        android:textColor="@color/color_999999"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_marginHorizontal="9dp"
                        android:background="@drawable/shape_dot_4"/>

                    <TextView
                        android:id="@+id/tv_adapter_pledge_expiration_title"
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/size_mini"
                        android:layout_marginStart="5dp"
                        android:textColor="@color/color_black"
                        tools:text="@string/bill_repayment_amount"/>

                    <TextView
                        android:id="@+id/tv_adapter_pledge_expiration_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="2020-20-20-2024-20-20"
                        android:textSize="@dimen/size_mini"
                        android:textColor="@color/color_999999"/>
                </LinearLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_marginTop="5dp"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tv_ar_button_repayment_status_pledge"
                        android:layout_width="wrap_content"
                        android:minWidth="90dp"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:layout_alignParentEnd="true"
                        android:paddingVertical="3dp"
                        android:textColor="@color/color_config_main"
                        tools:text="@string/paid_finished"
                        android:background="@drawable/shape_ring_bigr_main"/>
                </RelativeLayout>
            </LinearLayout>

            <TextView
                android:id="@+id/tv_ar_order_status_pledge"
                android:layout_width="wrap_content"
                android:layout_alignParentEnd="true"
                android:paddingHorizontal="20dp"
                android:paddingVertical="2dp"
                android:background="@drawable/shape_repayment_status_light_blue"
                android:layout_height="wrap_content"
                tools:text="待还款"
                android:textSize="@dimen/size_mini_l"
                android:textColor="@color/white"/>
        </RelativeLayout>




    </LinearLayout>

</layout>