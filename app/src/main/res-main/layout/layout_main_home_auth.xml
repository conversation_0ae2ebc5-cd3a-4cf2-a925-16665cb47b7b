<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:background="@drawable/shape_home_status_container"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:text="@string/borrow_credit_amount"
            android:textColor="@color/color_config_home_text"
            android:textSize="@dimen/size_sm" />

        <TextView
            android:id="@+id/tv_main_home_max_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:textColor="@color/color_config_home_text"
            android:textSize="45sp"
            tools:text="80,000" />

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:layout_marginBottom="20dp"
            android:layout_marginTop="20dp"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_layout_main_auth_sub_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:gravity="center_horizontal"
                android:textStyle="bold"
                android:textColor="@color/color_config_home_text"
                android:textSize="@dimen/size_body_l"/>

            <TextView
                android:id="@+id/tv_layout_main_auth_title_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="40dp"
                android:layout_marginTop="10dp"
                android:gravity="center_horizontal"
                android:textColor="@color/color_config_home_text"
                android:textSize="@dimen/size_mini_l"/>


            <com.fenqi.main.page.main.view.BorrowStepsView
                android:id="@+id/borrow_steps_view"
                android:layout_marginTop="20dp"
                android:layout_width="match_parent"
                android:paddingHorizontal="20dp"
                android:layout_height="wrap_content"/>

        </LinearLayout>

    </LinearLayout>


</layout>