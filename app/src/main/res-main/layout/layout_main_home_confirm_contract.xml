<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="click"
            type="com.fenqi.main.page.main.view.MainHomeConfirmContractView" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:background="@drawable/shape_home_status_container"
        android:orientation="vertical">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="70dp"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/auth_wait_for_pay_bg"/>

       <LinearLayout
           android:layout_width="match_parent"
           android:orientation="vertical"
           android:gravity="center_horizontal"
           android:layout_marginBottom="20dp"
           android:layout_marginTop="20dp"
           android:layout_height="wrap_content">

           <TextView
               android:id="@+id/tv_layout_main_auth_sub_tip"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:layout_marginHorizontal="20dp"
               android:gravity="center_horizontal"
               android:textSize="@dimen/size_mini_l"
               android:textColor="@color/color_config_home_text"
               />

           <TextView
               android:id="@+id/tv_layout_main_auth_title_tip"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:textStyle="bold"
               android:layout_marginTop="10dp"
               android:gravity="center_horizontal"
               android:textSize="@dimen/size_body_l"
               android:textColor="@color/color_config_home_text"
               />

           <TextView
               android:id="@+id/button_main_home_auth"
               android:layout_width="match_parent"
               android:layout_height="50dp"
               android:layout_marginTop="15dp"
               android:background="@drawable/shape_main_home_button"
               android:gravity="center"
               android:layout_marginHorizontal="20dp"
               android:onClick="@{()->click.clickSubmit()}"
               tools:text="@string/apply_now"
               android:textColor="@color/color_config_home_text"
               android:textSize="@dimen/size_body" />

           <com.fenqi.main.page.main.view.BorrowStepsView
               android:id="@+id/borrow_steps_view"
               android:layout_marginTop="20dp"
               android:layout_width="match_parent"
               android:paddingHorizontal="20dp"
               android:layout_height="wrap_content"/>

       </LinearLayout>

    </LinearLayout>


</layout>