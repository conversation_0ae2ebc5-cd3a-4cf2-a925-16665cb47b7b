<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="com.fenqi.main.page.main.view.MainHomeWaitForPayView" />

        <variable
            name="click"
            type="com.fenqi.main.page.main.view.MainHomeWaitForPayView" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_home_status_container"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_layout_main_auth_sub_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:text="@string/text_main_loan_max_amount"
            android:textColor="@color/color_config_home_text"
            android:textSize="@dimen/size_mini_l"/>

        <TextView
            android:id="@+id/tv_layout_main_auth_title_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:textStyle="bold"
            android:textColor="@color/color_config_home_text"
            android:textSize="@dimen/size_body_l"
            tools:text="额度" />

        <TextView
            android:id="@+id/tv_main_home_bottom_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:layout_marginTop="20dp"
            android:text="@string/text_main_amount_tips"
            android:textColor="#B6AC90"
            android:textSize="10sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:layout_marginTop="20dp"
            android:layout_marginHorizontal="30dp"
            android:background="@drawable/shape_dot_line_home"/>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:layout_weight="1">

                <ImageView
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:background="@drawable/home_tip1"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textSize="@dimen/size_mini"
                    android:text="@string/home_tip1"
                    android:textColor="@color/color_config_home_text"/>

            </LinearLayout>
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:layout_weight="1">

                <ImageView
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:background="@drawable/home_tip2"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textSize="@dimen/size_mini"
                    android:text="@string/home_tip2"
                    android:textColor="@color/color_config_home_text"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:layout_weight="1">

                <ImageView
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:background="@drawable/home_tip3"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:textSize="@dimen/size_mini"
                    android:text="@string/home_tip3"
                    android:textColor="@color/color_config_home_text"/>

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>


</layout>