<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:background="@color/base_library_white">

  <RelativeLayout
      android:id="@+id/view_main_tab_home"
      android:layout_width="0dp"
      android:layout_weight="1"
      android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:gravity="center_horizontal"
        android:layout_centerHorizontal="true"
        android:orientation="vertical">

      <ImageView
          android:id="@+id/img_main_tab_home"
          android:layout_width="25dp"
          android:layout_height="25dp"
          android:background="@drawable/home_light" />

      <TextView
          android:id="@+id/text_main_tab_trip"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:textColor="@color/color_black"
          android:textSize="@dimen/size_mini"
          android:text="@string/title_home" />
    </LinearLayout>
  </RelativeLayout>

  <RelativeLayout
      android:id="@+id/relat_main_tab_repayment"
      android:layout_width="0dp"
      android:layout_weight="1"
      android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:gravity="center_horizontal"
        android:layout_centerHorizontal="true"
        android:orientation="vertical">

      <ImageView
          android:id="@+id/img_main_tab_repayment"
          android:layout_width="25dp"
          android:layout_height="25dp"
          android:background="@drawable/repayment_grey" />

      <TextView
          android:id="@+id/text_main_tab_repayment"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:textColor="@color/base_library_text_important"
          android:textSize="@dimen/size_mini"
          android:text="@string/repayment" />
    </LinearLayout>
  </RelativeLayout>

  <RelativeLayout
      android:id="@+id/view_main_tab_mine"
      android:layout_width="0dp"
      android:layout_weight="1"
      android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:gravity="center_horizontal"
        android:layout_centerHorizontal="true"
        android:orientation="vertical">

      <ImageView
          android:id="@+id/img_main_tab_mine"
          android:layout_width="25dp"
          android:layout_height="25dp"
          android:background="@drawable/mine_grey"/>

      <TextView
          android:id="@+id/text_main_tab_mine"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:textColor="@color/base_library_text_important"
          android:textSize="@dimen/size_mini"
          android:text="@string/main_mine_tab" />
    </LinearLayout>

  </RelativeLayout>
</LinearLayout>