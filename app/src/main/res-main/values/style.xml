<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="CommonLoadDialog" parent="@style/CommonDialog">
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:background">#00FFFFFF</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <style name="CommonDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.7</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
    </style>
</resources>