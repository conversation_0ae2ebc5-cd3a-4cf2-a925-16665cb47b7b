<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="MarqueeViewStyle">
        <attr format="integer|reference" name="mvInterval"/>
        <attr format="integer|reference" name="mvAnimDuration"/>
        <attr format="dimension|reference" name="mvTextSize"/>
        <attr format="color|reference" name="mvTextColor"/>
        <attr format="boolean" name="mvSingleLine"/>
        <attr format="reference" name="mvFont"/>
        <attr name="mvGravity">
            <enum name="left" value="0"/>
            <enum name="center" value="1"/>
            <enum name="right" value="2"/>
        </attr>
        <attr name="mvDirection">
            <enum name="bottom_to_top" value="0"/>
            <enum name="top_to_bottom" value="1"/>
            <enum name="right_to_left" value="2"/>
            <enum name="left_to_right" value="3"/>
        </attr>
    </declare-styleable>
</resources>