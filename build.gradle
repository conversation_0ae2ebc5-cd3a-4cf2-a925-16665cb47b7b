buildscript {
    ext.kotlin_version = '1.8.21'
    repositories {
        google()
        mavenCentral()
        maven { url 'https://maven.google.com' }
        maven {
            url 'https://maven.accuauth.com/repository/maven-releases/'
            // If your gradle version is greater than 7.0, you need to add the following configuration to allow pulling aar via http
            allowInsecureProtocol = true
        }

        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/'}
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url "https://maven.aliyun.com/repository/jcenter" }

        //banner
        maven { url "https://s01.oss.sonatype.org/content/groups/public" }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "org.jetbrains.kotlin:kotlin-android-extensions:$kotlin_version"
        classpath 'com.github.kezong:fat-aar:1.3.8'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
        maven { url 'https://maven.google.com' }
        maven {
            url 'https://maven.accuauth.com/repository/maven-releases/'
            // If your gradle version is greater than 7.0, you need to add the following configuration to allow pulling aar via http
            allowInsecureProtocol = true
        }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/'}
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url "https://maven.aliyun.com/repository/jcenter" }

        //banner
        maven { url "https://s01.oss.sonatype.org/content/groups/public" }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}