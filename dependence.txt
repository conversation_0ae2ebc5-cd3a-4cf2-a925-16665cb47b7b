> Task :buildSrc:compileKotlin UP-TO-DATE
> Task :buildSrc:compileJava NO-SOURCE
> Task :buildSrc:compileGroovy NO-SOURCE
> Task :buildSrc:pluginDescriptors UP-TO-DATE
> Task :buildSrc:processResources NO-SOURCE
> Task :buildSrc:classes UP-TO-DATE
> Task :buildSrc:inspectClassesForKotlinIC UP-TO-DATE
> Task :buildSrc:jar UP-TO-DATE
> Task :buildSrc:assemble UP-TO-DATE
> Task :buildSrc:compileTestKotlin NO-SOURCE
> Task :buildSrc:pluginUnderTestMetadata UP-TO-DATE
> Task :buildSrc:compileTestJava NO-SOURCE
> Task :buildSrc:compileTestGroovy NO-SOURCE
> Task :buildSrc:processTestResources NO-SOURCE
> Task :buildSrc:testClasses UP-TO-DATE
> Task :buildSrc:test NO-SOURCE
> Task :buildSrc:validatePlugins UP-TO-DATE
> Task :buildSrc:check UP-TO-DATE
> Task :buildSrc:build UP-TO-DATE
Configuration on demand is an incubating feature.

> Configure project :
Build was configured to prefer settings repositories over project repositories but repository 'Google' was added by build file 'build.gradle'
Build was configured to prefer settings repositories over project repositories but repository 'MavenRepo' was added by build file 'build.gradle'
Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by build file 'build.gradle'
Build was configured to prefer settings repositories over project repositories but repository 'maven2' was added by build file 'build.gradle'
Build was configured to prefer settings repositories over project repositories but repository 'maven3' was added by build file 'build.gradle'
Build was configured to prefer settings repositories over project repositories but repository 'maven4' was added by build file 'build.gradle'
Build was configured to prefer settings repositories over project repositories but repository 'maven5' was added by build file 'build.gradle'
Build was configured to prefer settings repositories over project repositories but repository 'maven6' was added by build file 'build.gradle'
Build was configured to prefer settings repositories over project repositories but repository 'maven7' was added by build file 'build.gradle'

> Configure project :app
WARNING:: DSL element 'annotationProcessorOptions.includeCompileClasspath' is obsolete.
It will be removed in version 7.0 of the Android Gradle plugin.
It does not do anything and AGP no longer includes annotation processors added on your project's compile classpath
Build was configured to prefer settings repositories over project repositories but repository 'MavenRepo2' was added by build file 'app/build.gradle'

> Task :app:dependencies

------------------------------------------------------------
Project ':app'
------------------------------------------------------------

androidApis - Configuration providing various types of Android JAR file
No dependencies

androidTestAnnotationProcessor - Classpath for the annotation processor for 'androidTest'. (n)
No dependencies

androidTestApi - API dependencies for 'androidTest' sources. (n)
No dependencies

androidTestApiDependenciesMetadata
No dependencies

androidTestApk - Apk dependencies for 'androidTest' sources (deprecated: use 'androidTestRuntimeOnly' instead). (n)
No dependencies

androidTestAtestAnnotationProcessor - Classpath for the annotation processor for 'androidTestAtest'. (n)
No dependencies

androidTestAtestApi - API dependencies for 'androidTestAtest' sources. (n)
No dependencies

androidTestAtestApiDependenciesMetadata
No dependencies

androidTestAtestApk - Apk dependencies for 'androidTestAtest' sources (deprecated: use 'androidTestAtestRuntimeOnly' instead). (n)
No dependencies

androidTestAtestCompile - Compile dependencies for 'androidTestAtest' sources (deprecated: use 'androidTestAtestImplementation' instead). (n)
No dependencies

androidTestAtestCompileOnly - Compile only dependencies for 'androidTestAtest' sources. (n)
No dependencies

androidTestAtestCompileOnlyDependenciesMetadata
No dependencies

androidTestAtestDebugAnnotationProcessor - Classpath for the annotation processor for 'androidTestAtestDebug'. (n)
No dependencies

androidTestAtestDebugApi - API dependencies for 'androidTestAtestDebug' sources. (n)
No dependencies

androidTestAtestDebugApiDependenciesMetadata
No dependencies

androidTestAtestDebugApk - Apk dependencies for 'androidTestAtestDebug' sources (deprecated: use 'androidTestAtestDebugRuntimeOnly' instead). (n)
No dependencies

androidTestAtestDebugCompile - Compile dependencies for 'androidTestAtestDebug' sources (deprecated: use 'androidTestAtestDebugImplementation' instead). (n)
No dependencies

androidTestAtestDebugCompileOnly - Compile only dependencies for 'androidTestAtestDebug' sources. (n)
No dependencies

androidTestAtestDebugCompileOnlyDependenciesMetadata
No dependencies

androidTestAtestDebugImplementation - Implementation only dependencies for 'androidTestAtestDebug' sources. (n)
No dependencies

androidTestAtestDebugImplementationDependenciesMetadata
No dependencies

androidTestAtestDebugIntransitiveDependenciesMetadata
No dependencies

androidTestAtestDebugProvided - Provided dependencies for 'androidTestAtestDebug' sources (deprecated: use 'androidTestAtestDebugCompileOnly' instead). (n)
No dependencies

androidTestAtestDebugRuntimeOnly - Runtime only dependencies for 'androidTestAtestDebug' sources. (n)
No dependencies

androidTestAtestDebugWearApp - Link to a wear app to embed for object 'androidTestAtestDebug'. (n)
No dependencies

androidTestAtestImplementation - Implementation only dependencies for 'androidTestAtest' sources. (n)
No dependencies

androidTestAtestImplementationDependenciesMetadata
No dependencies

androidTestAtestIntransitiveDependenciesMetadata
No dependencies

androidTestAtestProvided - Provided dependencies for 'androidTestAtest' sources (deprecated: use 'androidTestAtestCompileOnly' instead). (n)
No dependencies

androidTestAtestRuntimeOnly - Runtime only dependencies for 'androidTestAtest' sources. (n)
No dependencies

androidTestAtestWearApp - Link to a wear app to embed for object 'androidTestAtest'. (n)
No dependencies

androidTestCompile - Compile dependencies for 'androidTest' sources (deprecated: use 'androidTestImplementation' instead). (n)
No dependencies

androidTestCompileOnly - Compile only dependencies for 'androidTest' sources. (n)
No dependencies

androidTestCompileOnlyDependenciesMetadata
No dependencies

androidTestDebugAnnotationProcessor - Classpath for the annotation processor for 'androidTestDebug'. (n)
No dependencies

androidTestDebugApi - API dependencies for 'androidTestDebug' sources. (n)
No dependencies

androidTestDebugApiDependenciesMetadata
No dependencies

androidTestDebugApk - Apk dependencies for 'androidTestDebug' sources (deprecated: use 'androidTestDebugRuntimeOnly' instead). (n)
No dependencies

androidTestDebugCompile - Compile dependencies for 'androidTestDebug' sources (deprecated: use 'androidTestDebugImplementation' instead). (n)
No dependencies

androidTestDebugCompileOnly - Compile only dependencies for 'androidTestDebug' sources. (n)
No dependencies

androidTestDebugCompileOnlyDependenciesMetadata
No dependencies

androidTestDebugImplementation - Implementation only dependencies for 'androidTestDebug' sources. (n)
No dependencies

androidTestDebugImplementationDependenciesMetadata
No dependencies

androidTestDebugIntransitiveDependenciesMetadata
No dependencies

androidTestDebugProvided - Provided dependencies for 'androidTestDebug' sources (deprecated: use 'androidTestDebugCompileOnly' instead). (n)
No dependencies

androidTestDebugRuntimeOnly - Runtime only dependencies for 'androidTestDebug' sources. (n)
No dependencies

androidTestDebugWearApp - Link to a wear app to embed for object 'androidTestDebug'. (n)
No dependencies

androidTestImplementation - Implementation only dependencies for 'androidTest' sources. (n)
No dependencies

androidTestImplementationDependenciesMetadata
No dependencies

androidTestIntransitiveDependenciesMetadata
No dependencies

androidTestProvided - Provided dependencies for 'androidTest' sources (deprecated: use 'androidTestCompileOnly' instead). (n)
No dependencies

androidTestReleaseAnnotationProcessor - Classpath for the annotation processor for 'androidTestRelease'. (n)
No dependencies

androidTestReleaseApi - API dependencies for 'androidTestRelease' sources. (n)
No dependencies

androidTestReleaseApiDependenciesMetadata
No dependencies

androidTestReleaseApk - Apk dependencies for 'androidTestRelease' sources (deprecated: use 'androidTestReleaseRuntimeOnly' instead). (n)
No dependencies

androidTestReleaseCompile - Compile dependencies for 'androidTestRelease' sources (deprecated: use 'androidTestReleaseImplementation' instead). (n)
No dependencies

androidTestReleaseCompileOnly - Compile only dependencies for 'androidTestRelease' sources. (n)
No dependencies

androidTestReleaseCompileOnlyDependenciesMetadata
No dependencies

androidTestReleaseImplementation - Implementation only dependencies for 'androidTestRelease' sources. (n)
No dependencies

androidTestReleaseImplementationDependenciesMetadata
No dependencies

androidTestReleaseIntransitiveDependenciesMetadata
No dependencies

androidTestReleaseProvided - Provided dependencies for 'androidTestRelease' sources (deprecated: use 'androidTestReleaseCompileOnly' instead). (n)
No dependencies

androidTestReleaseRuntimeOnly - Runtime only dependencies for 'androidTestRelease' sources. (n)
No dependencies

androidTestReleaseWearApp - Link to a wear app to embed for object 'androidTestRelease'. (n)
No dependencies

androidTestRuntimeOnly - Runtime only dependencies for 'androidTest' sources. (n)
No dependencies

androidTestUtil - Additional APKs used during instrumentation testing.
No dependencies

androidTestWearApp - Link to a wear app to embed for object 'androidTest'. (n)
No dependencies

annotationProcessor - Classpath for the annotation processor for 'main'. (n)
\--- androidx.databinding:databinding-compiler:4.2.2 (n)

api - API dependencies for 'main' sources. (n)
+--- androidx.databinding:databinding-common:4.2.2 (n)
+--- androidx.databinding:databinding-runtime:4.2.2 (n)
\--- androidx.databinding:databinding-adapters:4.2.2 (n)

apiDependenciesMetadata
+--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-runtime:4.2.2
|    +--- androidx.databinding:viewbinding:4.2.2
|    |    \--- androidx.annotation:annotation:1.0.0
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0
|    |    +--- androidx.lifecycle:lifecycle-common:2.0.0
|    |    |    \--- androidx.annotation:annotation:1.0.0
|    |    +--- androidx.arch.core:core-common:2.0.0
|    |    |    \--- androidx.annotation:annotation:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0
|    +--- androidx.collection:collection:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0
|    \--- androidx.databinding:databinding-common:4.2.2
\--- androidx.databinding:databinding-adapters:4.2.2
     +--- androidx.databinding:databinding-common:4.2.2
     \--- androidx.databinding:databinding-runtime:4.2.2 (*)

apk - Apk dependencies for 'main' sources (deprecated: use 'runtimeOnly' instead). (n)
No dependencies

archives - Configuration for archive artifacts. (n)
No dependencies

atestAnnotationProcessor - Classpath for the annotation processor for 'atest'. (n)
No dependencies

atestApi - API dependencies for 'atest' sources. (n)
No dependencies

atestApiDependenciesMetadata
No dependencies

atestApk - Apk dependencies for 'atest' sources (deprecated: use 'atestRuntimeOnly' instead). (n)
No dependencies

atestCompile - Compile dependencies for 'atest' sources (deprecated: use 'atestImplementation' instead). (n)
No dependencies

atestCompileOnly - Compile only dependencies for 'atest' sources. (n)
No dependencies

atestCompileOnlyDependenciesMetadata
No dependencies

atestDebugAabPublication - Bundle Publication for atestDebug (n)
No dependencies

atestDebugAndroidTestAnnotationProcessorClasspath - Resolved configuration for annotation-processor for variant: atestDebugAndroidTest
No dependencies

atestDebugAndroidTestApi (n)
No dependencies

atestDebugAndroidTestApiDependenciesMetadata
No dependencies

atestDebugAndroidTestCompilationApi - API dependencies for /atestDebugAndroidTest (n)
No dependencies

atestDebugAndroidTestCompilationCompileOnly - Compile only dependencies for /atestDebugAndroidTest. (n)
No dependencies

atestDebugAndroidTestCompilationImplementation - Implementation only dependencies for /atestDebugAndroidTest. (n)
No dependencies

atestDebugAndroidTestCompilationRuntimeOnly - Runtime only dependencies for /atestDebugAndroidTest. (n)
No dependencies

atestDebugAndroidTestCompileClasspath - Compile classpath for /atestDebugAndroidTest.
+--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-runtime:4.2.2
|    +--- androidx.databinding:viewbinding:4.2.2
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1
|    |    +--- androidx.lifecycle:lifecycle-common:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.arch.core:core-common:2.1.0
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.collection:collection:1.0.0 -> 1.1.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-adapters:4.2.2
|    +--- androidx.databinding:databinding-common:4.2.2
|    \--- androidx.databinding:databinding-runtime:4.2.2 (*)
+--- project :app (*)
+--- org.jetbrains.kotlin:kotlin-parcelize-runtime:1.8.21
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.8.21
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- androidx.appcompat:appcompat:1.3.1
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.5.0 -> 1.6.0
|    |    +--- androidx.annotation:annotation:1.2.0
|    |    +--- androidx.annotation:annotation-experimental:1.1.0
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    |    \--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |         +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |         \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.activity:activity:1.2.4 -> 1.3.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.savedstate:savedstate:1.1.0
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1
|    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1
|    |         |    \--- androidx.lifecycle:lifecycle-common:2.3.1 (*)
|    |         \--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    +--- androidx.fragment:fragment:1.3.6
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.2.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    +--- androidx.viewpager:viewpager:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    \--- androidx.customview:customview:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |         \--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.loader:loader:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.3.0
|    |    |    |    +--- androidx.arch.core:core-runtime:2.1.0
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    |    |    \--- androidx.arch.core:core-common:2.1.0 (*)
|    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.activity:activity:1.2.4 -> 1.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 (*)
|    |    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.appcompat:appcompat-resources:1.3.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    \--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |         +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |         +--- androidx.interpolator:interpolator:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.collection:collection:1.1.0 (*)
|    +--- androidx.drawerlayout:drawerlayout:1.0.0
|    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    \--- androidx.customview:customview:1.0.0 (*)
|    \--- androidx.savedstate:savedstate:1.1.0 (*)
+--- com.google.android.material:material:1.4.0
|    +--- androidx.annotation:annotation:1.0.1 -> 1.2.0
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    +--- androidx.cardview:cardview:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.coordinatorlayout:coordinatorlayout:1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.customview:customview:1.0.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.constraintlayout:constraintlayout:2.0.1 -> 2.1.4
|    +--- androidx.core:core:1.5.0 -> 1.6.0 (*)
|    +--- androidx.dynamicanimation:dynamicanimation:1.0.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.legacy:legacy-support-core-utils:1.0.0
|    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |         +--- androidx.documentfile:documentfile:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.loader:loader:1.0.0 (*)
|    |         +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.print:print:1.0.0
|    |              \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.0.0 -> 1.2.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.3.2 -> 1.6.0 (*)
|    |    \--- androidx.customview:customview:1.0.0 (*)
|    +--- androidx.transition:transition:1.2.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    \--- androidx.viewpager2:viewpager2:1.0.0
|         +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|         +--- androidx.fragment:fragment:1.1.0 -> 1.3.6 (*)
|         +--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
|         +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|         \--- androidx.collection:collection:1.1.0 (*)
+--- androidx.constraintlayout:constraintlayout:2.1.2 -> 2.1.4
+--- androidx.core:core-ktx:1.6.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.5.10 -> 1.8.21 (*)
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.core:core:1.6.0 (*)
+--- com.squareup.okhttp3:okhttp:4.9.3
|    +--- com.squareup.okio:okio:2.8.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 1.8.21 (*)
+--- com.squareup.okhttp3:logging-interceptor:4.9.3
|    +--- com.squareup.okhttp3:okhttp:4.9.3 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.21
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
|         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21
|              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- com.github.tbruyelle:rxpermissions:0.12
+--- io.reactivex.rxjava3:rxjava:3.0.6
+--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    \--- androidx.interpolator:interpolator:1.0.0 (*)
+--- org.greenrobot:eventbus:3.3.1
|    \--- org.greenrobot:eventbus-java:3.3.1
+--- me.drakeet.support:toastcompat:1.1.0
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.github.bumptech.glide:glide:4.11.0
|    +--- com.github.bumptech.glide:gifdecoder:4.11.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- com.github.bumptech.glide:disklrucache:4.11.0
|    +--- com.github.bumptech.glide:annotations:4.11.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.vectordrawable:vectordrawable-animated:1.0.0 -> 1.1.0 (*)
|    \--- androidx.exifinterface:exifinterface:1.0.0 -> 1.3.3
+--- com.google.code.gson:gson:2.8.6
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1
|    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.4.1
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
+--- com.squareup.retrofit2:retrofit:2.9.0
|    \--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.3 (*)
+--- com.squareup.retrofit2:converter-gson:2.9.0
|    +--- com.squareup.retrofit2:retrofit:2.9.0 (*)
|    \--- com.google.code.gson:gson:2.8.5 -> 2.8.6
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21 (*)
+--- com.android.support:support-annotations:28.0.0 -> androidx.annotation:annotation:1.2.0
+--- cn.hutool:hutool-crypto:5.8.16
|    \--- cn.hutool:hutool-core:5.8.16
+--- com.squareup.retrofit2:converter-scalars:2.0.0
|    \--- com.squareup.retrofit2:retrofit:2.0.0 -> 2.9.0 (*)
+--- androidx.lifecycle:lifecycle-livedata-ktx:2.3.0
|    +--- androidx.lifecycle:lifecycle-livedata:2.3.0 (*)
|    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.3.0
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1 (*)
+--- io.github.lucksiege:pictureselector:v3.10.6
|    +--- androidx.appcompat:appcompat:1.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.2.1 (*)
|    +--- androidx.activity:activity:1.3.1 (*)
|    +--- androidx.fragment:fragment:1.3.1 -> 1.3.6 (*)
|    +--- androidx.exifinterface:exifinterface:1.3.3
|    +--- androidx.viewpager2:viewpager2:1.0.0 (*)
|    \--- androidx.constraintlayout:constraintlayout:2.1.4
+--- io.github.lucksiege:compress:v3.10.6
+--- com.aliyun.dpa:oss-android-sdk:2.9.19
|    \--- com.squareup.okhttp3:okhttp:3.11.0 -> 4.9.3 (*)
+--- com.afollestad.material-dialogs:core:3.3.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.3.61 -> 1.8.21 (*)
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    \--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
+--- com.afollestad.material-dialogs:bottomsheets:3.2.1
|    \--- com.afollestad.material-dialogs:core:3.2.1 -> 3.3.0 (*)
+--- com.jakewharton.rxbinding2:rxbinding:2.0.0
|    +--- io.reactivex.rxjava2:rxjava:2.0.2
|    +--- io.reactivex.rxjava2:rxandroid:2.0.0
|    |    \--- io.reactivex.rxjava2:rxjava:2.0.0 -> 2.0.2
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.amazonaws:aws-android-sdk-s3:2.73.0
|    +--- com.amazonaws:aws-android-sdk-core:2.73.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- com.amazonaws:aws-android-sdk-kms:2.73.0
|    |    \--- com.amazonaws:aws-android-sdk-core:2.73.0 (*)
|    \--- androidx.appcompat:appcompat:1.2.0 -> 1.3.1 (*)
+--- com.android.support.constraint:constraint-layout:2.0.4 -> androidx.constraintlayout:constraintlayout:2.1.4
+--- androidx.multidex:multidex:2.0.1
+--- org.jetbrains.kotlin:kotlin-parcelize-runtime:{strictly 1.8.21} -> 1.8.21 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib:{strictly 1.8.21} -> 1.8.21 (c)
+--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:{strictly 1.8.21} -> 1.8.21 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-common:{strictly 1.8.21} -> 1.8.21 (c)
\--- org.jetbrains:annotations:{strictly 13.0} -> 13.0 (c)

atestDebugAndroidTestCompileOnly (n)
No dependencies

atestDebugAndroidTestCompileOnlyDependenciesMetadata
No dependencies

atestDebugAndroidTestImplementation (n)
No dependencies

atestDebugAndroidTestImplementationDependenciesMetadata
No dependencies

atestDebugAndroidTestIntransitiveDependenciesMetadata
No dependencies

atestDebugAndroidTestRuntimeClasspath - Runtime classpath of /atestDebugAndroidTest.
+--- org.jetbrains.kotlin:kotlin-parcelize-runtime:1.8.21
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.8.21
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- org.jetbrains.kotlin:kotlin-parcelize-runtime:{strictly 1.8.21} -> 1.8.21 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib:{strictly 1.8.21} -> 1.8.21 (c)
+--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:{strictly 1.8.21} -> 1.8.21 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-common:{strictly 1.8.21} -> 1.8.21 (c)
\--- org.jetbrains:annotations:{strictly 13.0} -> 13.0 (c)

atestDebugAndroidTestRuntimeOnly (n)
No dependencies

atestDebugAnnotationProcessor - Classpath for the annotation processor for 'atestDebug'. (n)
No dependencies

atestDebugAnnotationProcessorClasspath - Resolved configuration for annotation-processor for variant: atestDebug
\--- androidx.databinding:databinding-compiler:4.2.2
     +--- androidx.databinding:databinding-compiler-common:4.2.2
     |    +--- androidx.databinding:databinding-common:4.2.2
     |    +--- com.android.databinding:baseLibrary:4.2.2
     |    +--- org.antlr:antlr4:4.5.3
     |    +--- commons-io:commons-io:2.4
     |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
     |    +--- com.google.guava:guava:28.1-jre
     |    |    +--- com.google.guava:failureaccess:1.0.1
     |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
     |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    +--- org.checkerframework:checker-qual:2.8.1
     |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
     |    |    +--- com.google.j2objc:j2objc-annotations:1.3
     |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
     |    +--- com.squareup:javapoet:1.10.0
     |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
     |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
     |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
     |    |    |    \--- org.jetbrains:annotations:13.0
     |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
     |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
     |    +--- com.google.code.gson:gson:2.8.6
     |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
     |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
     |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
     |    |    +--- org.glassfish.jaxb:txw2:2.3.2
     |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
     |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
     |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
     |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
     |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
     |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
     |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
     |    +--- com.android.tools:annotations:27.2.2
     |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
     |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
     |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
     +--- androidx.databinding:databinding-common:4.2.2
     +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
     +--- com.google.auto:auto-common:0.10
     |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
     +--- commons-io:commons-io:2.4
     +--- commons-codec:commons-codec:1.10
     +--- org.antlr:antlr4:4.5.3
     \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3

atestDebugApi - API dependencies for 'atestDebug' sources. (n)
No dependencies

atestDebugApiDependenciesMetadata
No dependencies

atestDebugApiElements - API elements for atestDebug (n)
No dependencies

atestDebugApk - Apk dependencies for 'atestDebug' sources (deprecated: use 'atestDebugRuntimeOnly' instead). (n)
No dependencies

atestDebugApkPublication - APK publication for atestDebug (n)
No dependencies

atestDebugCompilationApi - API dependencies for /atestDebug (n)
No dependencies

atestDebugCompilationCompileOnly - Compile only dependencies for /atestDebug. (n)
No dependencies

atestDebugCompilationImplementation - Implementation only dependencies for /atestDebug. (n)
No dependencies

atestDebugCompilationRuntimeOnly - Runtime only dependencies for /atestDebug. (n)
No dependencies

atestDebugCompile - Dependencies for compilation (deprecated, use 'atestDebugCompilationImplementation ' instead). (n)
No dependencies

atestDebugCompileClasspath - Compile classpath for /atestDebug.
+--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-runtime:4.2.2
|    +--- androidx.databinding:viewbinding:4.2.2
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1
|    |    +--- androidx.lifecycle:lifecycle-common:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.arch.core:core-common:2.1.0
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.collection:collection:1.0.0 -> 1.1.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-adapters:4.2.2
|    +--- androidx.databinding:databinding-common:4.2.2
|    \--- androidx.databinding:databinding-runtime:4.2.2 (*)
+--- org.jetbrains.kotlin:kotlin-parcelize-runtime:1.8.21
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.8.21
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- androidx.databinding:databinding-common:{strictly 4.2.2} -> 4.2.2 (c)
+--- androidx.databinding:databinding-runtime:{strictly 4.2.2} -> 4.2.2 (c)
+--- androidx.databinding:databinding-adapters:{strictly 4.2.2} -> 4.2.2 (c)
+--- org.jetbrains.kotlin:kotlin-parcelize-runtime:{strictly 1.8.21} -> 1.8.21 (c)
+--- androidx.appcompat:appcompat:1.3.1
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.5.0 -> 1.6.0
|    |    +--- androidx.annotation:annotation:1.2.0
|    |    +--- androidx.annotation:annotation-experimental:1.1.0
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    |    \--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |         +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |         \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.activity:activity:1.2.4 -> 1.3.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.savedstate:savedstate:1.1.0
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1
|    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1
|    |         |    \--- androidx.lifecycle:lifecycle-common:2.3.1 (*)
|    |         \--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    +--- androidx.fragment:fragment:1.3.6
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.2.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    +--- androidx.viewpager:viewpager:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    \--- androidx.customview:customview:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |         \--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.loader:loader:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.3.0
|    |    |    |    +--- androidx.arch.core:core-runtime:2.1.0
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    |    |    \--- androidx.arch.core:core-common:2.1.0 (*)
|    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.activity:activity:1.2.4 -> 1.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 (*)
|    |    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.appcompat:appcompat-resources:1.3.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    \--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |         +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |         +--- androidx.interpolator:interpolator:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.collection:collection:1.1.0 (*)
|    +--- androidx.drawerlayout:drawerlayout:1.0.0
|    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    \--- androidx.customview:customview:1.0.0 (*)
|    \--- androidx.savedstate:savedstate:1.1.0 (*)
+--- com.google.android.material:material:1.4.0
|    +--- androidx.annotation:annotation:1.0.1 -> 1.2.0
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    +--- androidx.cardview:cardview:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.coordinatorlayout:coordinatorlayout:1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.customview:customview:1.0.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.constraintlayout:constraintlayout:2.0.1 -> 2.1.4
|    +--- androidx.core:core:1.5.0 -> 1.6.0 (*)
|    +--- androidx.dynamicanimation:dynamicanimation:1.0.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.legacy:legacy-support-core-utils:1.0.0
|    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |         +--- androidx.documentfile:documentfile:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.loader:loader:1.0.0 (*)
|    |         +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.print:print:1.0.0
|    |              \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.0.0 -> 1.2.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.3.2 -> 1.6.0 (*)
|    |    \--- androidx.customview:customview:1.0.0 (*)
|    +--- androidx.transition:transition:1.2.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    \--- androidx.viewpager2:viewpager2:1.0.0
|         +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|         +--- androidx.fragment:fragment:1.1.0 -> 1.3.6 (*)
|         +--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
|         +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|         \--- androidx.collection:collection:1.1.0 (*)
+--- androidx.constraintlayout:constraintlayout:2.1.2 -> 2.1.4
+--- androidx.core:core-ktx:1.6.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.5.10 -> 1.8.21 (*)
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.core:core:1.6.0 (*)
+--- com.squareup.okhttp3:okhttp:4.9.3
|    +--- com.squareup.okio:okio:2.8.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 1.8.21 (*)
+--- com.squareup.okhttp3:logging-interceptor:4.9.3
|    +--- com.squareup.okhttp3:okhttp:4.9.3 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.21
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
|         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21
|              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- com.github.tbruyelle:rxpermissions:0.12
+--- io.reactivex.rxjava3:rxjava:3.0.6
+--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    \--- androidx.interpolator:interpolator:1.0.0 (*)
+--- org.greenrobot:eventbus:3.3.1
|    \--- org.greenrobot:eventbus-java:3.3.1
+--- me.drakeet.support:toastcompat:1.1.0
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.github.bumptech.glide:glide:4.11.0
|    +--- com.github.bumptech.glide:gifdecoder:4.11.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- com.github.bumptech.glide:disklrucache:4.11.0
|    +--- com.github.bumptech.glide:annotations:4.11.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.vectordrawable:vectordrawable-animated:1.0.0 -> 1.1.0 (*)
|    \--- androidx.exifinterface:exifinterface:1.0.0 -> 1.3.3
+--- com.google.code.gson:gson:2.8.6
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1
|    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.4.1
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
+--- com.squareup.retrofit2:retrofit:2.9.0
|    \--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.3 (*)
+--- com.squareup.retrofit2:converter-gson:2.9.0
|    +--- com.squareup.retrofit2:retrofit:2.9.0 (*)
|    \--- com.google.code.gson:gson:2.8.5 -> 2.8.6
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21 (*)
+--- com.android.support:support-annotations:28.0.0 -> androidx.annotation:annotation:1.2.0
+--- cn.hutool:hutool-crypto:5.8.16
|    \--- cn.hutool:hutool-core:5.8.16
+--- com.squareup.retrofit2:converter-scalars:2.0.0
|    \--- com.squareup.retrofit2:retrofit:2.0.0 -> 2.9.0 (*)
+--- androidx.lifecycle:lifecycle-livedata-ktx:2.3.0
|    +--- androidx.lifecycle:lifecycle-livedata:2.3.0 (*)
|    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.3.0
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1 (*)
+--- io.github.lucksiege:pictureselector:v3.10.6
|    +--- androidx.appcompat:appcompat:1.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.2.1 (*)
|    +--- androidx.activity:activity:1.3.1 (*)
|    +--- androidx.fragment:fragment:1.3.1 -> 1.3.6 (*)
|    +--- androidx.exifinterface:exifinterface:1.3.3
|    +--- androidx.viewpager2:viewpager2:1.0.0 (*)
|    \--- androidx.constraintlayout:constraintlayout:2.1.4
+--- io.github.lucksiege:compress:v3.10.6
+--- com.aliyun.dpa:oss-android-sdk:2.9.19
|    \--- com.squareup.okhttp3:okhttp:3.11.0 -> 4.9.3 (*)
+--- com.afollestad.material-dialogs:core:3.3.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.3.61 -> 1.8.21 (*)
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    \--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
+--- com.afollestad.material-dialogs:bottomsheets:3.2.1
|    \--- com.afollestad.material-dialogs:core:3.2.1 -> 3.3.0 (*)
+--- com.jakewharton.rxbinding2:rxbinding:2.0.0
|    +--- io.reactivex.rxjava2:rxjava:2.0.2
|    +--- io.reactivex.rxjava2:rxandroid:2.0.0
|    |    \--- io.reactivex.rxjava2:rxjava:2.0.0 -> 2.0.2
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.amazonaws:aws-android-sdk-s3:2.73.0
|    +--- com.amazonaws:aws-android-sdk-core:2.73.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- com.amazonaws:aws-android-sdk-kms:2.73.0
|    |    \--- com.amazonaws:aws-android-sdk-core:2.73.0 (*)
|    \--- androidx.appcompat:appcompat:1.2.0 -> 1.3.1 (*)
+--- com.android.support.constraint:constraint-layout:2.0.4 -> androidx.constraintlayout:constraintlayout:2.1.4
+--- androidx.multidex:multidex:2.0.1
+--- androidx.appcompat:appcompat:{strictly 1.3.1} -> 1.3.1 (c)
+--- com.google.android.material:material:{strictly 1.4.0} -> 1.4.0 (c)
+--- androidx.constraintlayout:constraintlayout:{strictly 2.1.4} -> 2.1.4 (c)
+--- androidx.core:core-ktx:{strictly 1.6.0} -> 1.6.0 (c)
+--- com.squareup.okhttp3:okhttp:{strictly 4.9.3} -> 4.9.3 (c)
+--- com.squareup.okhttp3:logging-interceptor:{strictly 4.9.3} -> 4.9.3 (c)
+--- com.github.tbruyelle:rxpermissions:{strictly 0.12} -> 0.12 (c)
+--- io.reactivex.rxjava3:rxjava:{strictly 3.0.6} -> 3.0.6 (c)
+--- androidx.swiperefreshlayout:swiperefreshlayout:{strictly 1.1.0} -> 1.1.0 (c)
+--- org.greenrobot:eventbus:{strictly 3.3.1} -> 3.3.1 (c)
+--- me.drakeet.support:toastcompat:{strictly 1.1.0} -> 1.1.0 (c)
+--- com.github.bumptech.glide:glide:{strictly 4.11.0} -> 4.11.0 (c)
+--- com.google.code.gson:gson:{strictly 2.8.6} -> 2.8.6 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:{strictly 1.4.1} -> 1.4.1 (c)
+--- com.squareup.retrofit2:retrofit:{strictly 2.9.0} -> 2.9.0 (c)
+--- com.squareup.retrofit2:converter-gson:{strictly 2.9.0} -> 2.9.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:{strictly 1.8.21} -> 1.8.21 (c)
+--- androidx.annotation:annotation:{strictly 1.2.0} -> 1.2.0 (c)
+--- cn.hutool:hutool-crypto:{strictly 5.8.16} -> 5.8.16 (c)
+--- com.squareup.retrofit2:converter-scalars:{strictly 2.0.0} -> 2.0.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-ktx:{strictly 2.3.0} -> 2.3.0 (c)
+--- io.github.lucksiege:pictureselector:{strictly v3.10.6} -> v3.10.6 (c)
+--- io.github.lucksiege:compress:{strictly v3.10.6} -> v3.10.6 (c)
+--- com.aliyun.dpa:oss-android-sdk:{strictly 2.9.19} -> 2.9.19 (c)
+--- com.afollestad.material-dialogs:core:{strictly 3.3.0} -> 3.3.0 (c)
+--- com.afollestad.material-dialogs:bottomsheets:{strictly 3.2.1} -> 3.2.1 (c)
+--- com.jakewharton.rxbinding2:rxbinding:{strictly 2.0.0} -> 2.0.0 (c)
+--- com.amazonaws:aws-android-sdk-s3:{strictly 2.73.0} -> 2.73.0 (c)
+--- androidx.multidex:multidex:{strictly 2.0.1} -> 2.0.1 (c)
+--- androidx.databinding:viewbinding:{strictly 4.2.2} -> 4.2.2 (c)
+--- androidx.lifecycle:lifecycle-runtime:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.collection:collection:{strictly 1.1.0} -> 1.1.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib:{strictly 1.8.21} -> 1.8.21 (c)
+--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:{strictly 1.8.21} -> 1.8.21 (c)
+--- androidx.core:core:{strictly 1.6.0} -> 1.6.0 (c)
+--- androidx.cursoradapter:cursoradapter:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.activity:activity:{strictly 1.3.1} -> 1.3.1 (c)
+--- androidx.fragment:fragment:{strictly 1.3.6} -> 1.3.6 (c)
+--- androidx.appcompat:appcompat-resources:{strictly 1.3.1} -> 1.3.1 (c)
+--- androidx.drawerlayout:drawerlayout:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.savedstate:savedstate:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.cardview:cardview:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.coordinatorlayout:coordinatorlayout:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.dynamicanimation:dynamicanimation:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.annotation:annotation-experimental:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.recyclerview:recyclerview:{strictly 1.2.1} -> 1.2.1 (c)
+--- androidx.transition:transition:{strictly 1.2.0} -> 1.2.0 (c)
+--- androidx.vectordrawable:vectordrawable:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.viewpager2:viewpager2:{strictly 1.0.0} -> 1.0.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:{strictly 1.8.21} -> 1.8.21 (c)
+--- androidx.interpolator:interpolator:{strictly 1.0.0} -> 1.0.0 (c)
+--- org.greenrobot:eventbus-java:{strictly 3.3.1} -> 3.3.1 (c)
+--- com.github.bumptech.glide:gifdecoder:{strictly 4.11.0} -> 4.11.0 (c)
+--- com.github.bumptech.glide:disklrucache:{strictly 4.11.0} -> 4.11.0 (c)
+--- com.github.bumptech.glide:annotations:{strictly 4.11.0} -> 4.11.0 (c)
+--- androidx.vectordrawable:vectordrawable-animated:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.exifinterface:exifinterface:{strictly 1.3.3} -> 1.3.3 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-core:{strictly 1.4.1} -> 1.4.1 (c)
+--- cn.hutool:hutool-core:{strictly 5.8.16} -> 5.8.16 (c)
+--- androidx.lifecycle:lifecycle-livedata:{strictly 2.3.0} -> 2.3.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-core-ktx:{strictly 2.3.0} -> 2.3.0 (c)
+--- io.reactivex.rxjava2:rxjava:{strictly 2.0.2} -> 2.0.2 (c)
+--- io.reactivex.rxjava2:rxandroid:{strictly 2.0.0} -> 2.0.0 (c)
+--- com.amazonaws:aws-android-sdk-core:{strictly 2.73.0} -> 2.73.0 (c)
+--- com.amazonaws:aws-android-sdk-kms:{strictly 2.73.0} -> 2.73.0 (c)
+--- androidx.lifecycle:lifecycle-common:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.arch.core:core-common:{strictly 2.1.0} -> 2.1.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-common:{strictly 1.8.21} -> 1.8.21 (c)
+--- org.jetbrains:annotations:{strictly 13.0} -> 13.0 (c)
+--- androidx.versionedparcelable:versionedparcelable:{strictly 1.1.1} -> 1.1.1 (c)
+--- androidx.lifecycle:lifecycle-viewmodel:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.lifecycle:lifecycle-viewmodel-savedstate:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.viewpager:viewpager:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.loader:loader:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-core:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.customview:customview:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.legacy:legacy-support-core-utils:{strictly 1.0.0} -> 1.0.0 (c)
+--- com.squareup.okio:okio:{strictly 2.8.0} -> 2.8.0 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:{strictly 1.4.1} -> 1.4.1 (c)
+--- androidx.arch.core:core-runtime:{strictly 2.1.0} -> 2.1.0 (c)
+--- androidx.documentfile:documentfile:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.localbroadcastmanager:localbroadcastmanager:{strictly 1.0.0} -> 1.0.0 (c)
\--- androidx.print:print:{strictly 1.0.0} -> 1.0.0 (c)

atestDebugCompileOnly - Compile only dependencies for 'atestDebug' sources. (n)
No dependencies

atestDebugCompileOnlyDependenciesMetadata
No dependencies

atestDebugImplementation - Implementation only dependencies for 'atestDebug' sources. (n)
No dependencies

atestDebugImplementationDependenciesMetadata
No dependencies

atestDebugIntransitiveDependenciesMetadata
No dependencies

atestDebugProvided - Provided dependencies for 'atestDebug' sources (deprecated: use 'atestDebugCompileOnly' instead). (n)
No dependencies

atestDebugReverseMetadataValues - Metadata Values dependencies for the base Split
No dependencies

atestDebugRuntimeClasspath - Runtime classpath of /atestDebug.
+--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-runtime:4.2.2
|    +--- androidx.databinding:viewbinding:4.2.2
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1
|    |    +--- androidx.arch.core:core-runtime:2.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    \--- androidx.arch.core:core-common:2.1.0
|    |    |         \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.lifecycle:lifecycle-common:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.arch.core:core-common:2.1.0 (*)
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.collection:collection:1.0.0 -> 1.1.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-adapters:4.2.2
|    +--- androidx.databinding:databinding-common:4.2.2
|    \--- androidx.databinding:databinding-runtime:4.2.2 (*)
+--- org.jetbrains.kotlin:kotlin-parcelize-runtime:1.8.21
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.8.21
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- androidx.appcompat:appcompat:1.3.1
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.5.0 -> 1.6.0
|    |    +--- androidx.annotation:annotation:1.2.0
|    |    +--- androidx.annotation:annotation-experimental:1.1.0
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.activity:activity:1.2.4 -> 1.3.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.savedstate:savedstate:1.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    +--- androidx.arch.core:core-common:2.0.1 -> 2.1.0 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-common:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1
|    |    |    |    +--- androidx.arch.core:core-common:2.1.0 (*)
|    |    |    |    +--- androidx.arch.core:core-runtime:2.1.0 (*)
|    |    |    |    \--- androidx.lifecycle:lifecycle-common:2.3.1 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.tracing:tracing:1.0.0
|    |         \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.fragment:fragment:1.3.6
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.2.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    +--- androidx.viewpager:viewpager:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    \--- androidx.customview:customview:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |         \--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.loader:loader:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.3.0
|    |    |    |    +--- androidx.arch.core:core-common:2.1.0 (*)
|    |    |    |    +--- androidx.arch.core:core-runtime:2.1.0 (*)
|    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.activity:activity:1.2.4 -> 1.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 (*)
|    |    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.appcompat:appcompat-resources:1.3.1
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    \--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |         +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |         +--- androidx.interpolator:interpolator:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.collection:collection:1.1.0 (*)
|    +--- androidx.drawerlayout:drawerlayout:1.0.0
|    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    \--- androidx.customview:customview:1.0.0 (*)
|    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.lifecycle:lifecycle-runtime:2.3.1 (*)
|    \--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
+--- com.google.android.material:material:1.4.0
|    +--- androidx.annotation:annotation:1.0.1 -> 1.2.0
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    +--- androidx.cardview:cardview:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.coordinatorlayout:coordinatorlayout:1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.customview:customview:1.0.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.constraintlayout:constraintlayout:2.0.1 -> 2.1.4
|    |    +--- androidx.appcompat:appcompat:1.2.0 -> 1.3.1 (*)
|    |    +--- androidx.core:core:1.3.2 -> 1.6.0 (*)
|    |    \--- androidx.constraintlayout:constraintlayout-core:1.0.4
|    +--- androidx.core:core:1.5.0 -> 1.6.0 (*)
|    +--- androidx.dynamicanimation:dynamicanimation:1.0.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.legacy:legacy-support-core-utils:1.0.0
|    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |         +--- androidx.documentfile:documentfile:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.loader:loader:1.0.0 (*)
|    |         +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.print:print:1.0.0
|    |              \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.0.0 -> 1.2.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.3.2 -> 1.6.0 (*)
|    |    +--- androidx.customview:customview:1.0.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.transition:transition:1.2.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    \--- androidx.viewpager2:viewpager2:1.0.0
|         +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|         +--- androidx.fragment:fragment:1.1.0 -> 1.3.6 (*)
|         +--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
|         +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|         \--- androidx.collection:collection:1.1.0 (*)
+--- androidx.constraintlayout:constraintlayout:2.1.2 -> 2.1.4 (*)
+--- androidx.core:core-ktx:1.6.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.5.10 -> 1.8.21 (*)
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.core:core:1.6.0 (*)
+--- com.squareup.okhttp3:okhttp:4.9.3
|    +--- com.squareup.okio:okio:2.8.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 1.8.21 (*)
+--- com.squareup.okhttp3:logging-interceptor:4.9.3
|    +--- com.squareup.okhttp3:okhttp:4.9.3 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.21
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
|         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21
|              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- com.github.tbruyelle:rxpermissions:0.12
|    +--- io.reactivex.rxjava3:rxjava:3.0.4 -> 3.0.6
|    +--- androidx.fragment:fragment:1.2.5 -> 1.3.6 (*)
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
+--- io.reactivex.rxjava3:rxjava:3.0.6
+--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    \--- androidx.interpolator:interpolator:1.0.0 (*)
+--- org.greenrobot:eventbus:3.3.1
|    \--- org.greenrobot:eventbus-java:3.3.1
+--- me.drakeet.support:toastcompat:1.1.0
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.github.bumptech.glide:glide:4.11.0
|    +--- com.github.bumptech.glide:gifdecoder:4.11.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- com.github.bumptech.glide:disklrucache:4.11.0
|    +--- com.github.bumptech.glide:annotations:4.11.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.vectordrawable:vectordrawable-animated:1.0.0 -> 1.1.0 (*)
|    \--- androidx.exifinterface:exifinterface:1.0.0 -> 1.3.3
|         \--- androidx.annotation:annotation:1.2.0
+--- com.google.code.gson:gson:2.8.6
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1
|    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.4.1
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
+--- com.squareup.retrofit2:retrofit:2.9.0
|    \--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.3 (*)
+--- com.squareup.retrofit2:converter-gson:2.9.0
|    +--- com.squareup.retrofit2:retrofit:2.9.0 (*)
|    \--- com.google.code.gson:gson:2.8.5 -> 2.8.6
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21 (*)
+--- com.android.support:support-annotations:28.0.0 -> androidx.annotation:annotation:1.2.0
+--- cn.hutool:hutool-crypto:5.8.16
|    \--- cn.hutool:hutool-core:5.8.16
+--- com.squareup.retrofit2:converter-scalars:2.0.0
|    \--- com.squareup.retrofit2:retrofit:2.0.0 -> 2.9.0 (*)
+--- androidx.lifecycle:lifecycle-livedata-ktx:2.3.0
|    +--- androidx.lifecycle:lifecycle-livedata:2.3.0 (*)
|    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.3.0
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1 (*)
+--- io.github.lucksiege:pictureselector:v3.10.6
|    +--- androidx.appcompat:appcompat:1.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.2.1 (*)
|    +--- androidx.activity:activity:1.3.1 (*)
|    +--- androidx.fragment:fragment:1.3.1 -> 1.3.6 (*)
|    +--- androidx.exifinterface:exifinterface:1.3.3 (*)
|    +--- androidx.viewpager2:viewpager2:1.0.0 (*)
|    \--- androidx.constraintlayout:constraintlayout:2.1.4 (*)
+--- io.github.lucksiege:compress:v3.10.6
+--- com.aliyun.dpa:oss-android-sdk:2.9.19
|    \--- com.squareup.okhttp3:okhttp:3.11.0 -> 4.9.3 (*)
+--- com.afollestad.material-dialogs:core:3.3.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.3.61 -> 1.8.21 (*)
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    \--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
+--- com.afollestad.material-dialogs:bottomsheets:3.2.1
|    +--- com.afollestad.material-dialogs:core:3.2.1 -> 3.3.0 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.3.61 -> 1.8.21 (*)
|    \--- com.google.android.material:material:1.1.0 -> 1.4.0 (*)
+--- com.jakewharton.rxbinding2:rxbinding:2.0.0
|    +--- io.reactivex.rxjava2:rxjava:2.0.2
|    +--- io.reactivex.rxjava2:rxandroid:2.0.0
|    |    \--- io.reactivex.rxjava2:rxjava:2.0.0 -> 2.0.2
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.amazonaws:aws-android-sdk-s3:2.73.0
|    +--- com.amazonaws:aws-android-sdk-core:2.73.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- com.amazonaws:aws-android-sdk-kms:2.73.0
|    |    \--- com.amazonaws:aws-android-sdk-core:2.73.0 (*)
|    \--- androidx.appcompat:appcompat:1.2.0 -> 1.3.1 (*)
+--- com.android.support.constraint:constraint-layout:2.0.4 -> androidx.constraintlayout:constraintlayout:2.1.4 (*)
\--- androidx.multidex:multidex:2.0.1

atestDebugRuntimeElements - Runtime elements for atestDebug (n)
No dependencies

atestDebugRuntimeOnly - Runtime only dependencies for 'atestDebug' sources. (n)
No dependencies

atestDebugUnitTestAnnotationProcessorClasspath - Resolved configuration for annotation-processor for variant: atestDebugUnitTest
No dependencies

atestDebugUnitTestApi (n)
No dependencies

atestDebugUnitTestApiDependenciesMetadata
No dependencies

atestDebugUnitTestCompilationApi - API dependencies for /atestDebugUnitTest (n)
No dependencies

atestDebugUnitTestCompilationCompileOnly - Compile only dependencies for /atestDebugUnitTest. (n)
No dependencies

atestDebugUnitTestCompilationImplementation - Implementation only dependencies for /atestDebugUnitTest. (n)
No dependencies

atestDebugUnitTestCompilationRuntimeOnly - Runtime only dependencies for /atestDebugUnitTest. (n)
No dependencies

atestDebugUnitTestCompileClasspath - Compile classpath for /atestDebugUnitTest.
+--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-runtime:4.2.2
|    +--- androidx.databinding:viewbinding:4.2.2
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1
|    |    +--- androidx.lifecycle:lifecycle-common:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.arch.core:core-common:2.1.0
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.collection:collection:1.0.0 -> 1.1.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-adapters:4.2.2
|    +--- androidx.databinding:databinding-common:4.2.2
|    \--- androidx.databinding:databinding-runtime:4.2.2 (*)
+--- project :app (*)
+--- org.jetbrains.kotlin:kotlin-parcelize-runtime:1.8.21
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.8.21
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- androidx.appcompat:appcompat:1.3.1
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.5.0 -> 1.6.0
|    |    +--- androidx.annotation:annotation:1.2.0
|    |    +--- androidx.annotation:annotation-experimental:1.1.0
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    |    \--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |         +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |         \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.activity:activity:1.2.4 -> 1.3.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.savedstate:savedstate:1.1.0
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1
|    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1
|    |         |    \--- androidx.lifecycle:lifecycle-common:2.3.1 (*)
|    |         \--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    +--- androidx.fragment:fragment:1.3.6
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.2.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    +--- androidx.viewpager:viewpager:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    \--- androidx.customview:customview:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |         \--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.loader:loader:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.3.0
|    |    |    |    +--- androidx.arch.core:core-runtime:2.1.0
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    |    |    \--- androidx.arch.core:core-common:2.1.0 (*)
|    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.activity:activity:1.2.4 -> 1.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 (*)
|    |    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.appcompat:appcompat-resources:1.3.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    \--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |         +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |         +--- androidx.interpolator:interpolator:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.collection:collection:1.1.0 (*)
|    +--- androidx.drawerlayout:drawerlayout:1.0.0
|    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    \--- androidx.customview:customview:1.0.0 (*)
|    \--- androidx.savedstate:savedstate:1.1.0 (*)
+--- com.google.android.material:material:1.4.0
|    +--- androidx.annotation:annotation:1.0.1 -> 1.2.0
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    +--- androidx.cardview:cardview:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.coordinatorlayout:coordinatorlayout:1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.customview:customview:1.0.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.constraintlayout:constraintlayout:2.0.1 -> 2.1.4
|    +--- androidx.core:core:1.5.0 -> 1.6.0 (*)
|    +--- androidx.dynamicanimation:dynamicanimation:1.0.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.legacy:legacy-support-core-utils:1.0.0
|    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |         +--- androidx.documentfile:documentfile:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.loader:loader:1.0.0 (*)
|    |         +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.print:print:1.0.0
|    |              \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.0.0 -> 1.2.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.3.2 -> 1.6.0 (*)
|    |    \--- androidx.customview:customview:1.0.0 (*)
|    +--- androidx.transition:transition:1.2.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    \--- androidx.viewpager2:viewpager2:1.0.0
|         +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|         +--- androidx.fragment:fragment:1.1.0 -> 1.3.6 (*)
|         +--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
|         +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|         \--- androidx.collection:collection:1.1.0 (*)
+--- androidx.constraintlayout:constraintlayout:2.1.2 -> 2.1.4
+--- androidx.core:core-ktx:1.6.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.5.10 -> 1.8.21 (*)
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.core:core:1.6.0 (*)
+--- com.squareup.okhttp3:okhttp:4.9.3
|    +--- com.squareup.okio:okio:2.8.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 1.8.21 (*)
+--- com.squareup.okhttp3:logging-interceptor:4.9.3
|    +--- com.squareup.okhttp3:okhttp:4.9.3 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.21
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
|         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21
|              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- com.github.tbruyelle:rxpermissions:0.12
+--- io.reactivex.rxjava3:rxjava:3.0.6
+--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    \--- androidx.interpolator:interpolator:1.0.0 (*)
+--- org.greenrobot:eventbus:3.3.1
|    \--- org.greenrobot:eventbus-java:3.3.1
+--- me.drakeet.support:toastcompat:1.1.0
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.github.bumptech.glide:glide:4.11.0
|    +--- com.github.bumptech.glide:gifdecoder:4.11.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- com.github.bumptech.glide:disklrucache:4.11.0
|    +--- com.github.bumptech.glide:annotations:4.11.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.vectordrawable:vectordrawable-animated:1.0.0 -> 1.1.0 (*)
|    \--- androidx.exifinterface:exifinterface:1.0.0 -> 1.3.3
+--- com.google.code.gson:gson:2.8.6
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1
|    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.4.1
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
+--- com.squareup.retrofit2:retrofit:2.9.0
|    \--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.3 (*)
+--- com.squareup.retrofit2:converter-gson:2.9.0
|    +--- com.squareup.retrofit2:retrofit:2.9.0 (*)
|    \--- com.google.code.gson:gson:2.8.5 -> 2.8.6
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21 (*)
+--- com.android.support:support-annotations:28.0.0 -> androidx.annotation:annotation:1.2.0
+--- cn.hutool:hutool-crypto:5.8.16
|    \--- cn.hutool:hutool-core:5.8.16
+--- com.squareup.retrofit2:converter-scalars:2.0.0
|    \--- com.squareup.retrofit2:retrofit:2.0.0 -> 2.9.0 (*)
+--- androidx.lifecycle:lifecycle-livedata-ktx:2.3.0
|    +--- androidx.lifecycle:lifecycle-livedata:2.3.0 (*)
|    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.3.0
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1 (*)
+--- io.github.lucksiege:pictureselector:v3.10.6
|    +--- androidx.appcompat:appcompat:1.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.2.1 (*)
|    +--- androidx.activity:activity:1.3.1 (*)
|    +--- androidx.fragment:fragment:1.3.1 -> 1.3.6 (*)
|    +--- androidx.exifinterface:exifinterface:1.3.3
|    +--- androidx.viewpager2:viewpager2:1.0.0 (*)
|    \--- androidx.constraintlayout:constraintlayout:2.1.4
+--- io.github.lucksiege:compress:v3.10.6
+--- com.aliyun.dpa:oss-android-sdk:2.9.19
|    \--- com.squareup.okhttp3:okhttp:3.11.0 -> 4.9.3 (*)
+--- com.afollestad.material-dialogs:core:3.3.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.3.61 -> 1.8.21 (*)
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    \--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
+--- com.afollestad.material-dialogs:bottomsheets:3.2.1
|    \--- com.afollestad.material-dialogs:core:3.2.1 -> 3.3.0 (*)
+--- com.jakewharton.rxbinding2:rxbinding:2.0.0
|    +--- io.reactivex.rxjava2:rxjava:2.0.2
|    +--- io.reactivex.rxjava2:rxandroid:2.0.0
|    |    \--- io.reactivex.rxjava2:rxjava:2.0.0 -> 2.0.2
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.amazonaws:aws-android-sdk-s3:2.73.0
|    +--- com.amazonaws:aws-android-sdk-core:2.73.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- com.amazonaws:aws-android-sdk-kms:2.73.0
|    |    \--- com.amazonaws:aws-android-sdk-core:2.73.0 (*)
|    \--- androidx.appcompat:appcompat:1.2.0 -> 1.3.1 (*)
+--- com.android.support.constraint:constraint-layout:2.0.4 -> androidx.constraintlayout:constraintlayout:2.1.4
+--- androidx.multidex:multidex:2.0.1
+--- org.jetbrains.kotlin:kotlin-parcelize-runtime:{strictly 1.8.21} -> 1.8.21 (c)
+--- androidx.databinding:databinding-common:{strictly 4.2.2} -> 4.2.2 (c)
+--- androidx.databinding:databinding-runtime:{strictly 4.2.2} -> 4.2.2 (c)
+--- androidx.annotation:annotation:{strictly 1.2.0} -> 1.2.0 (c)
+--- androidx.databinding:databinding-adapters:{strictly 4.2.2} -> 4.2.2 (c)
+--- androidx.appcompat:appcompat:{strictly 1.3.1} -> 1.3.1 (c)
+--- com.google.android.material:material:{strictly 1.4.0} -> 1.4.0 (c)
+--- androidx.constraintlayout:constraintlayout:{strictly 2.1.4} -> 2.1.4 (c)
+--- androidx.core:core-ktx:{strictly 1.6.0} -> 1.6.0 (c)
+--- com.squareup.okhttp3:okhttp:{strictly 4.9.3} -> 4.9.3 (c)
+--- com.squareup.okhttp3:logging-interceptor:{strictly 4.9.3} -> 4.9.3 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:{strictly 1.8.21} -> 1.8.21 (c)
+--- com.github.tbruyelle:rxpermissions:{strictly 0.12} -> 0.12 (c)
+--- io.reactivex.rxjava3:rxjava:{strictly 3.0.6} -> 3.0.6 (c)
+--- androidx.swiperefreshlayout:swiperefreshlayout:{strictly 1.1.0} -> 1.1.0 (c)
+--- org.greenrobot:eventbus:{strictly 3.3.1} -> 3.3.1 (c)
+--- me.drakeet.support:toastcompat:{strictly 1.1.0} -> 1.1.0 (c)
+--- com.github.bumptech.glide:glide:{strictly 4.11.0} -> 4.11.0 (c)
+--- com.google.code.gson:gson:{strictly 2.8.6} -> 2.8.6 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:{strictly 1.4.1} -> 1.4.1 (c)
+--- com.squareup.retrofit2:retrofit:{strictly 2.9.0} -> 2.9.0 (c)
+--- com.squareup.retrofit2:converter-gson:{strictly 2.9.0} -> 2.9.0 (c)
+--- cn.hutool:hutool-crypto:{strictly 5.8.16} -> 5.8.16 (c)
+--- com.squareup.retrofit2:converter-scalars:{strictly 2.0.0} -> 2.0.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-ktx:{strictly 2.3.0} -> 2.3.0 (c)
+--- io.github.lucksiege:pictureselector:{strictly v3.10.6} -> v3.10.6 (c)
+--- io.github.lucksiege:compress:{strictly v3.10.6} -> v3.10.6 (c)
+--- com.aliyun.dpa:oss-android-sdk:{strictly 2.9.19} -> 2.9.19 (c)
+--- com.afollestad.material-dialogs:core:{strictly 3.3.0} -> 3.3.0 (c)
+--- com.afollestad.material-dialogs:bottomsheets:{strictly 3.2.1} -> 3.2.1 (c)
+--- com.jakewharton.rxbinding2:rxbinding:{strictly 2.0.0} -> 2.0.0 (c)
+--- com.amazonaws:aws-android-sdk-s3:{strictly 2.73.0} -> 2.73.0 (c)
+--- androidx.multidex:multidex:{strictly 2.0.1} -> 2.0.1 (c)
+--- androidx.databinding:viewbinding:{strictly 4.2.2} -> 4.2.2 (c)
+--- androidx.lifecycle:lifecycle-runtime:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.collection:collection:{strictly 1.1.0} -> 1.1.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib:{strictly 1.8.21} -> 1.8.21 (c)
+--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:{strictly 1.8.21} -> 1.8.21 (c)
+--- androidx.core:core:{strictly 1.6.0} -> 1.6.0 (c)
+--- androidx.cursoradapter:cursoradapter:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.activity:activity:{strictly 1.3.1} -> 1.3.1 (c)
+--- androidx.fragment:fragment:{strictly 1.3.6} -> 1.3.6 (c)
+--- androidx.appcompat:appcompat-resources:{strictly 1.3.1} -> 1.3.1 (c)
+--- androidx.drawerlayout:drawerlayout:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.savedstate:savedstate:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.cardview:cardview:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.coordinatorlayout:coordinatorlayout:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.dynamicanimation:dynamicanimation:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.annotation:annotation-experimental:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.recyclerview:recyclerview:{strictly 1.2.1} -> 1.2.1 (c)
+--- androidx.transition:transition:{strictly 1.2.0} -> 1.2.0 (c)
+--- androidx.vectordrawable:vectordrawable:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.viewpager2:viewpager2:{strictly 1.0.0} -> 1.0.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:{strictly 1.4.10} -> 1.8.21 (c)
+--- androidx.interpolator:interpolator:{strictly 1.0.0} -> 1.0.0 (c)
+--- org.greenrobot:eventbus-java:{strictly 3.3.1} -> 3.3.1 (c)
+--- com.github.bumptech.glide:gifdecoder:{strictly 4.11.0} -> 4.11.0 (c)
+--- com.github.bumptech.glide:disklrucache:{strictly 4.11.0} -> 4.11.0 (c)
+--- com.github.bumptech.glide:annotations:{strictly 4.11.0} -> 4.11.0 (c)
+--- androidx.vectordrawable:vectordrawable-animated:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.exifinterface:exifinterface:{strictly 1.3.3} -> 1.3.3 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-core:{strictly 1.4.1} -> 1.4.1 (c)
+--- cn.hutool:hutool-core:{strictly 5.8.16} -> 5.8.16 (c)
+--- androidx.lifecycle:lifecycle-livedata:{strictly 2.3.0} -> 2.3.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-core-ktx:{strictly 2.3.0} -> 2.3.0 (c)
+--- io.reactivex.rxjava2:rxjava:{strictly 2.0.2} -> 2.0.2 (c)
+--- io.reactivex.rxjava2:rxandroid:{strictly 2.0.0} -> 2.0.0 (c)
+--- com.amazonaws:aws-android-sdk-core:{strictly 2.73.0} -> 2.73.0 (c)
+--- com.amazonaws:aws-android-sdk-kms:{strictly 2.73.0} -> 2.73.0 (c)
+--- androidx.lifecycle:lifecycle-common:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.arch.core:core-common:{strictly 2.1.0} -> 2.1.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-common:{strictly 1.8.21} -> 1.8.21 (c)
+--- org.jetbrains:annotations:{strictly 13.0} -> 13.0 (c)
+--- androidx.versionedparcelable:versionedparcelable:{strictly 1.1.1} -> 1.1.1 (c)
+--- androidx.lifecycle:lifecycle-viewmodel:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.lifecycle:lifecycle-viewmodel-savedstate:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.viewpager:viewpager:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.loader:loader:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-core:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.customview:customview:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.legacy:legacy-support-core-utils:{strictly 1.0.0} -> 1.0.0 (c)
+--- com.squareup.okio:okio:{strictly 2.8.0} -> 2.8.0 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:{strictly 1.4.1} -> 1.4.1 (c)
+--- androidx.arch.core:core-runtime:{strictly 2.1.0} -> 2.1.0 (c)
+--- androidx.documentfile:documentfile:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.localbroadcastmanager:localbroadcastmanager:{strictly 1.0.0} -> 1.0.0 (c)
\--- androidx.print:print:{strictly 1.0.0} -> 1.0.0 (c)

atestDebugUnitTestCompileOnly (n)
No dependencies

atestDebugUnitTestCompileOnlyDependenciesMetadata
No dependencies

atestDebugUnitTestImplementation (n)
No dependencies

atestDebugUnitTestImplementationDependenciesMetadata
No dependencies

atestDebugUnitTestIntransitiveDependenciesMetadata
No dependencies

atestDebugUnitTestRuntimeClasspath - Runtime classpath of /atestDebugUnitTest.
+--- project :app (*)
+--- org.jetbrains.kotlin:kotlin-parcelize-runtime:1.8.21
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.8.21
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-runtime:4.2.2
|    +--- androidx.databinding:viewbinding:4.2.2
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1
|    |    +--- androidx.arch.core:core-runtime:2.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    \--- androidx.arch.core:core-common:2.1.0
|    |    |         \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.lifecycle:lifecycle-common:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.arch.core:core-common:2.1.0 (*)
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.collection:collection:1.0.0 -> 1.1.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-adapters:4.2.2
|    +--- androidx.databinding:databinding-common:4.2.2
|    \--- androidx.databinding:databinding-runtime:4.2.2 (*)
+--- androidx.appcompat:appcompat:1.3.1
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.5.0 -> 1.6.0
|    |    +--- androidx.annotation:annotation:1.2.0
|    |    +--- androidx.annotation:annotation-experimental:1.1.0
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.activity:activity:1.2.4 -> 1.3.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.savedstate:savedstate:1.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    +--- androidx.arch.core:core-common:2.0.1 -> 2.1.0 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-common:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1
|    |    |    |    +--- androidx.arch.core:core-common:2.1.0 (*)
|    |    |    |    +--- androidx.arch.core:core-runtime:2.1.0 (*)
|    |    |    |    \--- androidx.lifecycle:lifecycle-common:2.3.1 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.tracing:tracing:1.0.0
|    |         \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.fragment:fragment:1.3.6
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.2.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    +--- androidx.viewpager:viewpager:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    \--- androidx.customview:customview:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |         \--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.loader:loader:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.3.0
|    |    |    |    +--- androidx.arch.core:core-common:2.1.0 (*)
|    |    |    |    +--- androidx.arch.core:core-runtime:2.1.0 (*)
|    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.activity:activity:1.2.4 -> 1.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 (*)
|    |    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.appcompat:appcompat-resources:1.3.1
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    \--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |         +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |         +--- androidx.interpolator:interpolator:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.collection:collection:1.1.0 (*)
|    +--- androidx.drawerlayout:drawerlayout:1.0.0
|    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    \--- androidx.customview:customview:1.0.0 (*)
|    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.lifecycle:lifecycle-runtime:2.3.1 (*)
|    \--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
+--- com.google.android.material:material:1.4.0
|    +--- androidx.annotation:annotation:1.0.1 -> 1.2.0
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    +--- androidx.cardview:cardview:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.coordinatorlayout:coordinatorlayout:1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.customview:customview:1.0.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.constraintlayout:constraintlayout:2.0.1 -> 2.1.4
|    |    +--- androidx.appcompat:appcompat:1.2.0 -> 1.3.1 (*)
|    |    +--- androidx.core:core:1.3.2 -> 1.6.0 (*)
|    |    \--- androidx.constraintlayout:constraintlayout-core:1.0.4
|    +--- androidx.core:core:1.5.0 -> 1.6.0 (*)
|    +--- androidx.dynamicanimation:dynamicanimation:1.0.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.legacy:legacy-support-core-utils:1.0.0
|    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |         +--- androidx.documentfile:documentfile:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.loader:loader:1.0.0 (*)
|    |         +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.print:print:1.0.0
|    |              \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.0.0 -> 1.2.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.3.2 -> 1.6.0 (*)
|    |    +--- androidx.customview:customview:1.0.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.transition:transition:1.2.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    \--- androidx.viewpager2:viewpager2:1.0.0
|         +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|         +--- androidx.fragment:fragment:1.1.0 -> 1.3.6 (*)
|         +--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
|         +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|         \--- androidx.collection:collection:1.1.0 (*)
+--- androidx.constraintlayout:constraintlayout:2.1.2 -> 2.1.4 (*)
+--- androidx.core:core-ktx:1.6.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.5.10 -> 1.8.21 (*)
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.core:core:1.6.0 (*)
+--- com.squareup.okhttp3:okhttp:4.9.3
|    +--- com.squareup.okio:okio:2.8.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 1.8.21 (*)
+--- com.squareup.okhttp3:logging-interceptor:4.9.3
|    +--- com.squareup.okhttp3:okhttp:4.9.3 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 1.8.21 (*)
|         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.10 -> 1.8.21
|              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- com.github.tbruyelle:rxpermissions:0.12
|    +--- io.reactivex.rxjava3:rxjava:3.0.4 -> 3.0.6
|    +--- androidx.fragment:fragment:1.2.5 -> 1.3.6 (*)
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
+--- io.reactivex.rxjava3:rxjava:3.0.6
+--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    \--- androidx.interpolator:interpolator:1.0.0 (*)
+--- org.greenrobot:eventbus:3.3.1
|    \--- org.greenrobot:eventbus-java:3.3.1
+--- me.drakeet.support:toastcompat:1.1.0
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.github.bumptech.glide:glide:4.11.0
|    +--- com.github.bumptech.glide:gifdecoder:4.11.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- com.github.bumptech.glide:disklrucache:4.11.0
|    +--- com.github.bumptech.glide:annotations:4.11.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.vectordrawable:vectordrawable-animated:1.0.0 -> 1.1.0 (*)
|    \--- androidx.exifinterface:exifinterface:1.0.0 -> 1.3.3
|         \--- androidx.annotation:annotation:1.2.0
+--- com.google.code.gson:gson:2.8.6
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1
|    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.4.1
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
+--- com.squareup.retrofit2:retrofit:2.9.0
|    \--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.3 (*)
+--- com.squareup.retrofit2:converter-gson:2.9.0
|    +--- com.squareup.retrofit2:retrofit:2.9.0 (*)
|    \--- com.google.code.gson:gson:2.8.5 -> 2.8.6
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21 (*)
+--- com.android.support:support-annotations:28.0.0 -> androidx.annotation:annotation:1.2.0
+--- cn.hutool:hutool-crypto:5.8.16
|    \--- cn.hutool:hutool-core:5.8.16
+--- com.squareup.retrofit2:converter-scalars:2.0.0
|    \--- com.squareup.retrofit2:retrofit:2.0.0 -> 2.9.0 (*)
+--- androidx.lifecycle:lifecycle-livedata-ktx:2.3.0
|    +--- androidx.lifecycle:lifecycle-livedata:2.3.0 (*)
|    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.3.0
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1 (*)
+--- io.github.lucksiege:pictureselector:v3.10.6
|    +--- androidx.appcompat:appcompat:1.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.2.1 (*)
|    +--- androidx.activity:activity:1.3.1 (*)
|    +--- androidx.fragment:fragment:1.3.1 -> 1.3.6 (*)
|    +--- androidx.exifinterface:exifinterface:1.3.3 (*)
|    +--- androidx.viewpager2:viewpager2:1.0.0 (*)
|    \--- androidx.constraintlayout:constraintlayout:2.1.4 (*)
+--- io.github.lucksiege:compress:v3.10.6
+--- com.aliyun.dpa:oss-android-sdk:2.9.19
|    \--- com.squareup.okhttp3:okhttp:3.11.0 -> 4.9.3 (*)
+--- com.afollestad.material-dialogs:core:3.3.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.3.61 -> 1.4.10 (*)
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    \--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
+--- com.afollestad.material-dialogs:bottomsheets:3.2.1
|    +--- com.afollestad.material-dialogs:core:3.2.1 -> 3.3.0 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.3.61 -> 1.4.10 (*)
|    \--- com.google.android.material:material:1.1.0 -> 1.4.0 (*)
+--- com.jakewharton.rxbinding2:rxbinding:2.0.0
|    +--- io.reactivex.rxjava2:rxjava:2.0.2
|    +--- io.reactivex.rxjava2:rxandroid:2.0.0
|    |    \--- io.reactivex.rxjava2:rxjava:2.0.0 -> 2.0.2
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.amazonaws:aws-android-sdk-s3:2.73.0
|    +--- com.amazonaws:aws-android-sdk-core:2.73.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- com.amazonaws:aws-android-sdk-kms:2.73.0
|    |    \--- com.amazonaws:aws-android-sdk-core:2.73.0 (*)
|    \--- androidx.appcompat:appcompat:1.2.0 -> 1.3.1 (*)
+--- com.android.support.constraint:constraint-layout:2.0.4 -> androidx.constraintlayout:constraintlayout:2.1.4 (*)
\--- androidx.multidex:multidex:2.0.1

atestDebugUnitTestRuntimeOnly (n)
No dependencies

atestDebugWearApp - Link to a wear app to embed for object 'atestDebug'. (n)
No dependencies

atestDebugWearBundling - Resolved Configuration for wear app bundling for variant: atestDebug
No dependencies

atestImplementation - Implementation only dependencies for 'atest' sources. (n)
No dependencies

atestImplementationDependenciesMetadata
No dependencies

atestIntransitiveDependenciesMetadata
No dependencies

atestProvided - Provided dependencies for 'atest' sources (deprecated: use 'atestCompileOnly' instead). (n)
No dependencies

atestReleaseAabPublication - Bundle Publication for atestRelease (n)
No dependencies

atestReleaseAnnotationProcessor - Classpath for the annotation processor for 'atestRelease'. (n)
No dependencies

atestReleaseAnnotationProcessorClasspath - Resolved configuration for annotation-processor for variant: atestRelease
\--- androidx.databinding:databinding-compiler:4.2.2
     +--- androidx.databinding:databinding-compiler-common:4.2.2
     |    +--- androidx.databinding:databinding-common:4.2.2
     |    +--- com.android.databinding:baseLibrary:4.2.2
     |    +--- org.antlr:antlr4:4.5.3
     |    +--- commons-io:commons-io:2.4
     |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
     |    +--- com.google.guava:guava:28.1-jre
     |    |    +--- com.google.guava:failureaccess:1.0.1
     |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
     |    |    +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    +--- org.checkerframework:checker-qual:2.8.1
     |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
     |    |    +--- com.google.j2objc:j2objc-annotations:1.3
     |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
     |    +--- com.squareup:javapoet:1.10.0
     |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
     |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
     |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
     |    |    |    \--- org.jetbrains:annotations:13.0
     |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
     |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
     |    +--- com.google.code.gson:gson:2.8.6
     |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
     |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
     |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
     |    |    +--- org.glassfish.jaxb:txw2:2.3.2
     |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
     |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
     |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
     |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
     |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
     |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
     |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
     |    +--- com.android.tools:annotations:27.2.2
     |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
     |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
     |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
     +--- androidx.databinding:databinding-common:4.2.2
     +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
     +--- com.google.auto:auto-common:0.10
     |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
     +--- commons-io:commons-io:2.4
     +--- commons-codec:commons-codec:1.10
     +--- org.antlr:antlr4:4.5.3
     \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3

atestReleaseApi - API dependencies for 'atestRelease' sources. (n)
No dependencies

atestReleaseApiDependenciesMetadata
No dependencies

atestReleaseApiElements - API elements for atestRelease (n)
No dependencies

atestReleaseApk - Apk dependencies for 'atestRelease' sources (deprecated: use 'atestReleaseRuntimeOnly' instead). (n)
No dependencies

atestReleaseApkPublication - APK publication for atestRelease (n)
No dependencies

atestReleaseCompilationApi - API dependencies for /atestRelease (n)
No dependencies

atestReleaseCompilationCompileOnly - Compile only dependencies for /atestRelease. (n)
No dependencies

atestReleaseCompilationImplementation - Implementation only dependencies for /atestRelease. (n)
No dependencies

atestReleaseCompilationRuntimeOnly - Runtime only dependencies for /atestRelease. (n)
No dependencies

atestReleaseCompile - Dependencies for compilation (deprecated, use 'atestReleaseCompilationImplementation ' instead). (n)
No dependencies

atestReleaseCompileClasspath - Compile classpath for /atestRelease.
+--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-runtime:4.2.2
|    +--- androidx.databinding:viewbinding:4.2.2
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1
|    |    +--- androidx.lifecycle:lifecycle-common:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.arch.core:core-common:2.1.0
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.collection:collection:1.0.0 -> 1.1.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-adapters:4.2.2
|    +--- androidx.databinding:databinding-common:4.2.2
|    \--- androidx.databinding:databinding-runtime:4.2.2 (*)
+--- org.jetbrains.kotlin:kotlin-parcelize-runtime:1.8.21
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.8.21
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- androidx.databinding:databinding-common:{strictly 4.2.2} -> 4.2.2 (c)
+--- androidx.databinding:databinding-runtime:{strictly 4.2.2} -> 4.2.2 (c)
+--- androidx.databinding:databinding-adapters:{strictly 4.2.2} -> 4.2.2 (c)
+--- org.jetbrains.kotlin:kotlin-parcelize-runtime:{strictly 1.8.21} -> 1.8.21 (c)
+--- androidx.appcompat:appcompat:1.3.1
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.5.0 -> 1.6.0
|    |    +--- androidx.annotation:annotation:1.2.0
|    |    +--- androidx.annotation:annotation-experimental:1.1.0
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    |    \--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |         +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |         \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.activity:activity:1.2.4 -> 1.3.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.savedstate:savedstate:1.1.0
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1
|    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1
|    |         |    \--- androidx.lifecycle:lifecycle-common:2.3.1 (*)
|    |         \--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    +--- androidx.fragment:fragment:1.3.6
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.2.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    +--- androidx.viewpager:viewpager:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    \--- androidx.customview:customview:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |         \--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.loader:loader:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.3.0
|    |    |    |    +--- androidx.arch.core:core-runtime:2.1.0
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    |    |    \--- androidx.arch.core:core-common:2.1.0 (*)
|    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.activity:activity:1.2.4 -> 1.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 (*)
|    |    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.appcompat:appcompat-resources:1.3.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    \--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |         +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |         +--- androidx.interpolator:interpolator:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.collection:collection:1.1.0 (*)
|    +--- androidx.drawerlayout:drawerlayout:1.0.0
|    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    \--- androidx.customview:customview:1.0.0 (*)
|    \--- androidx.savedstate:savedstate:1.1.0 (*)
+--- com.google.android.material:material:1.4.0
|    +--- androidx.annotation:annotation:1.0.1 -> 1.2.0
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    +--- androidx.cardview:cardview:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.coordinatorlayout:coordinatorlayout:1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.customview:customview:1.0.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.constraintlayout:constraintlayout:2.0.1 -> 2.1.4
|    +--- androidx.core:core:1.5.0 -> 1.6.0 (*)
|    +--- androidx.dynamicanimation:dynamicanimation:1.0.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.legacy:legacy-support-core-utils:1.0.0
|    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |         +--- androidx.documentfile:documentfile:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.loader:loader:1.0.0 (*)
|    |         +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.print:print:1.0.0
|    |              \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.0.0 -> 1.2.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.3.2 -> 1.6.0 (*)
|    |    \--- androidx.customview:customview:1.0.0 (*)
|    +--- androidx.transition:transition:1.2.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    \--- androidx.viewpager2:viewpager2:1.0.0
|         +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|         +--- androidx.fragment:fragment:1.1.0 -> 1.3.6 (*)
|         +--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
|         +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|         \--- androidx.collection:collection:1.1.0 (*)
+--- androidx.constraintlayout:constraintlayout:2.1.2 -> 2.1.4
+--- androidx.core:core-ktx:1.6.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.5.10 -> 1.8.21 (*)
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.core:core:1.6.0 (*)
+--- com.squareup.okhttp3:okhttp:4.9.3
|    +--- com.squareup.okio:okio:2.8.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 1.8.21 (*)
+--- com.squareup.okhttp3:logging-interceptor:4.9.3
|    +--- com.squareup.okhttp3:okhttp:4.9.3 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.21
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
|         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21
|              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- com.github.tbruyelle:rxpermissions:0.12
+--- io.reactivex.rxjava3:rxjava:3.0.6
+--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    \--- androidx.interpolator:interpolator:1.0.0 (*)
+--- org.greenrobot:eventbus:3.3.1
|    \--- org.greenrobot:eventbus-java:3.3.1
+--- me.drakeet.support:toastcompat:1.1.0
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.github.bumptech.glide:glide:4.11.0
|    +--- com.github.bumptech.glide:gifdecoder:4.11.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- com.github.bumptech.glide:disklrucache:4.11.0
|    +--- com.github.bumptech.glide:annotations:4.11.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.vectordrawable:vectordrawable-animated:1.0.0 -> 1.1.0 (*)
|    \--- androidx.exifinterface:exifinterface:1.0.0 -> 1.3.3
+--- com.google.code.gson:gson:2.8.6
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1
|    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.4.1
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
+--- com.squareup.retrofit2:retrofit:2.9.0
|    \--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.3 (*)
+--- com.squareup.retrofit2:converter-gson:2.9.0
|    +--- com.squareup.retrofit2:retrofit:2.9.0 (*)
|    \--- com.google.code.gson:gson:2.8.5 -> 2.8.6
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21 (*)
+--- com.android.support:support-annotations:28.0.0 -> androidx.annotation:annotation:1.2.0
+--- cn.hutool:hutool-crypto:5.8.16
|    \--- cn.hutool:hutool-core:5.8.16
+--- com.squareup.retrofit2:converter-scalars:2.0.0
|    \--- com.squareup.retrofit2:retrofit:2.0.0 -> 2.9.0 (*)
+--- androidx.lifecycle:lifecycle-livedata-ktx:2.3.0
|    +--- androidx.lifecycle:lifecycle-livedata:2.3.0 (*)
|    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.3.0
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1 (*)
+--- io.github.lucksiege:pictureselector:v3.10.6
|    +--- androidx.appcompat:appcompat:1.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.2.1 (*)
|    +--- androidx.activity:activity:1.3.1 (*)
|    +--- androidx.fragment:fragment:1.3.1 -> 1.3.6 (*)
|    +--- androidx.exifinterface:exifinterface:1.3.3
|    +--- androidx.viewpager2:viewpager2:1.0.0 (*)
|    \--- androidx.constraintlayout:constraintlayout:2.1.4
+--- io.github.lucksiege:compress:v3.10.6
+--- com.aliyun.dpa:oss-android-sdk:2.9.19
|    \--- com.squareup.okhttp3:okhttp:3.11.0 -> 4.9.3 (*)
+--- com.afollestad.material-dialogs:core:3.3.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.3.61 -> 1.8.21 (*)
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    \--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
+--- com.afollestad.material-dialogs:bottomsheets:3.2.1
|    \--- com.afollestad.material-dialogs:core:3.2.1 -> 3.3.0 (*)
+--- com.jakewharton.rxbinding2:rxbinding:2.0.0
|    +--- io.reactivex.rxjava2:rxjava:2.0.2
|    +--- io.reactivex.rxjava2:rxandroid:2.0.0
|    |    \--- io.reactivex.rxjava2:rxjava:2.0.0 -> 2.0.2
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.amazonaws:aws-android-sdk-s3:2.73.0
|    +--- com.amazonaws:aws-android-sdk-core:2.73.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- com.amazonaws:aws-android-sdk-kms:2.73.0
|    |    \--- com.amazonaws:aws-android-sdk-core:2.73.0 (*)
|    \--- androidx.appcompat:appcompat:1.2.0 -> 1.3.1 (*)
+--- com.android.support.constraint:constraint-layout:2.0.4 -> androidx.constraintlayout:constraintlayout:2.1.4
+--- androidx.multidex:multidex:2.0.1
+--- androidx.appcompat:appcompat:{strictly 1.3.1} -> 1.3.1 (c)
+--- com.google.android.material:material:{strictly 1.4.0} -> 1.4.0 (c)
+--- androidx.constraintlayout:constraintlayout:{strictly 2.1.4} -> 2.1.4 (c)
+--- androidx.core:core-ktx:{strictly 1.6.0} -> 1.6.0 (c)
+--- com.squareup.okhttp3:okhttp:{strictly 4.9.3} -> 4.9.3 (c)
+--- com.squareup.okhttp3:logging-interceptor:{strictly 4.9.3} -> 4.9.3 (c)
+--- com.github.tbruyelle:rxpermissions:{strictly 0.12} -> 0.12 (c)
+--- io.reactivex.rxjava3:rxjava:{strictly 3.0.6} -> 3.0.6 (c)
+--- androidx.swiperefreshlayout:swiperefreshlayout:{strictly 1.1.0} -> 1.1.0 (c)
+--- org.greenrobot:eventbus:{strictly 3.3.1} -> 3.3.1 (c)
+--- me.drakeet.support:toastcompat:{strictly 1.1.0} -> 1.1.0 (c)
+--- com.github.bumptech.glide:glide:{strictly 4.11.0} -> 4.11.0 (c)
+--- com.google.code.gson:gson:{strictly 2.8.6} -> 2.8.6 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:{strictly 1.4.1} -> 1.4.1 (c)
+--- com.squareup.retrofit2:retrofit:{strictly 2.9.0} -> 2.9.0 (c)
+--- com.squareup.retrofit2:converter-gson:{strictly 2.9.0} -> 2.9.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:{strictly 1.8.21} -> 1.8.21 (c)
+--- androidx.annotation:annotation:{strictly 1.2.0} -> 1.2.0 (c)
+--- cn.hutool:hutool-crypto:{strictly 5.8.16} -> 5.8.16 (c)
+--- com.squareup.retrofit2:converter-scalars:{strictly 2.0.0} -> 2.0.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-ktx:{strictly 2.3.0} -> 2.3.0 (c)
+--- io.github.lucksiege:pictureselector:{strictly v3.10.6} -> v3.10.6 (c)
+--- io.github.lucksiege:compress:{strictly v3.10.6} -> v3.10.6 (c)
+--- com.aliyun.dpa:oss-android-sdk:{strictly 2.9.19} -> 2.9.19 (c)
+--- com.afollestad.material-dialogs:core:{strictly 3.3.0} -> 3.3.0 (c)
+--- com.afollestad.material-dialogs:bottomsheets:{strictly 3.2.1} -> 3.2.1 (c)
+--- com.jakewharton.rxbinding2:rxbinding:{strictly 2.0.0} -> 2.0.0 (c)
+--- com.amazonaws:aws-android-sdk-s3:{strictly 2.73.0} -> 2.73.0 (c)
+--- androidx.multidex:multidex:{strictly 2.0.1} -> 2.0.1 (c)
+--- androidx.databinding:viewbinding:{strictly 4.2.2} -> 4.2.2 (c)
+--- androidx.lifecycle:lifecycle-runtime:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.collection:collection:{strictly 1.1.0} -> 1.1.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib:{strictly 1.8.21} -> 1.8.21 (c)
+--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:{strictly 1.8.21} -> 1.8.21 (c)
+--- androidx.core:core:{strictly 1.6.0} -> 1.6.0 (c)
+--- androidx.cursoradapter:cursoradapter:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.activity:activity:{strictly 1.3.1} -> 1.3.1 (c)
+--- androidx.fragment:fragment:{strictly 1.3.6} -> 1.3.6 (c)
+--- androidx.appcompat:appcompat-resources:{strictly 1.3.1} -> 1.3.1 (c)
+--- androidx.drawerlayout:drawerlayout:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.savedstate:savedstate:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.cardview:cardview:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.coordinatorlayout:coordinatorlayout:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.dynamicanimation:dynamicanimation:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.annotation:annotation-experimental:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.recyclerview:recyclerview:{strictly 1.2.1} -> 1.2.1 (c)
+--- androidx.transition:transition:{strictly 1.2.0} -> 1.2.0 (c)
+--- androidx.vectordrawable:vectordrawable:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.viewpager2:viewpager2:{strictly 1.0.0} -> 1.0.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:{strictly 1.8.21} -> 1.8.21 (c)
+--- androidx.interpolator:interpolator:{strictly 1.0.0} -> 1.0.0 (c)
+--- org.greenrobot:eventbus-java:{strictly 3.3.1} -> 3.3.1 (c)
+--- com.github.bumptech.glide:gifdecoder:{strictly 4.11.0} -> 4.11.0 (c)
+--- com.github.bumptech.glide:disklrucache:{strictly 4.11.0} -> 4.11.0 (c)
+--- com.github.bumptech.glide:annotations:{strictly 4.11.0} -> 4.11.0 (c)
+--- androidx.vectordrawable:vectordrawable-animated:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.exifinterface:exifinterface:{strictly 1.3.3} -> 1.3.3 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-core:{strictly 1.4.1} -> 1.4.1 (c)
+--- cn.hutool:hutool-core:{strictly 5.8.16} -> 5.8.16 (c)
+--- androidx.lifecycle:lifecycle-livedata:{strictly 2.3.0} -> 2.3.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-core-ktx:{strictly 2.3.0} -> 2.3.0 (c)
+--- io.reactivex.rxjava2:rxjava:{strictly 2.0.2} -> 2.0.2 (c)
+--- io.reactivex.rxjava2:rxandroid:{strictly 2.0.0} -> 2.0.0 (c)
+--- com.amazonaws:aws-android-sdk-core:{strictly 2.73.0} -> 2.73.0 (c)
+--- com.amazonaws:aws-android-sdk-kms:{strictly 2.73.0} -> 2.73.0 (c)
+--- androidx.lifecycle:lifecycle-common:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.arch.core:core-common:{strictly 2.1.0} -> 2.1.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-common:{strictly 1.8.21} -> 1.8.21 (c)
+--- org.jetbrains:annotations:{strictly 13.0} -> 13.0 (c)
+--- androidx.versionedparcelable:versionedparcelable:{strictly 1.1.1} -> 1.1.1 (c)
+--- androidx.lifecycle:lifecycle-viewmodel:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.lifecycle:lifecycle-viewmodel-savedstate:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.viewpager:viewpager:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.loader:loader:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-core:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.customview:customview:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.legacy:legacy-support-core-utils:{strictly 1.0.0} -> 1.0.0 (c)
+--- com.squareup.okio:okio:{strictly 2.8.0} -> 2.8.0 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:{strictly 1.4.1} -> 1.4.1 (c)
+--- androidx.arch.core:core-runtime:{strictly 2.1.0} -> 2.1.0 (c)
+--- androidx.documentfile:documentfile:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.localbroadcastmanager:localbroadcastmanager:{strictly 1.0.0} -> 1.0.0 (c)
\--- androidx.print:print:{strictly 1.0.0} -> 1.0.0 (c)

atestReleaseCompileOnly - Compile only dependencies for 'atestRelease' sources. (n)
No dependencies

atestReleaseCompileOnlyDependenciesMetadata
No dependencies

atestReleaseImplementation - Implementation only dependencies for 'atestRelease' sources. (n)
No dependencies

atestReleaseImplementationDependenciesMetadata
No dependencies

atestReleaseIntransitiveDependenciesMetadata
No dependencies

atestReleaseProvided - Provided dependencies for 'atestRelease' sources (deprecated: use 'atestReleaseCompileOnly' instead). (n)
No dependencies

atestReleaseReverseMetadataValues - Metadata Values dependencies for the base Split
No dependencies

atestReleaseRuntimeClasspath - Runtime classpath of /atestRelease.
+--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-runtime:4.2.2
|    +--- androidx.databinding:viewbinding:4.2.2
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1
|    |    +--- androidx.arch.core:core-runtime:2.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    \--- androidx.arch.core:core-common:2.1.0
|    |    |         \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.lifecycle:lifecycle-common:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.arch.core:core-common:2.1.0 (*)
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.collection:collection:1.0.0 -> 1.1.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-adapters:4.2.2
|    +--- androidx.databinding:databinding-common:4.2.2
|    \--- androidx.databinding:databinding-runtime:4.2.2 (*)
+--- org.jetbrains.kotlin:kotlin-parcelize-runtime:1.8.21
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.8.21
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- androidx.appcompat:appcompat:1.3.1
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.5.0 -> 1.6.0
|    |    +--- androidx.annotation:annotation:1.2.0
|    |    +--- androidx.annotation:annotation-experimental:1.1.0
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.activity:activity:1.2.4 -> 1.3.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.savedstate:savedstate:1.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    +--- androidx.arch.core:core-common:2.0.1 -> 2.1.0 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-common:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1
|    |    |    |    +--- androidx.arch.core:core-common:2.1.0 (*)
|    |    |    |    +--- androidx.arch.core:core-runtime:2.1.0 (*)
|    |    |    |    \--- androidx.lifecycle:lifecycle-common:2.3.1 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.tracing:tracing:1.0.0
|    |         \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.fragment:fragment:1.3.6
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.2.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    +--- androidx.viewpager:viewpager:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    \--- androidx.customview:customview:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |         \--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.loader:loader:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.3.0
|    |    |    |    +--- androidx.arch.core:core-common:2.1.0 (*)
|    |    |    |    +--- androidx.arch.core:core-runtime:2.1.0 (*)
|    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.activity:activity:1.2.4 -> 1.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 (*)
|    |    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.appcompat:appcompat-resources:1.3.1
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    \--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |         +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |         +--- androidx.interpolator:interpolator:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.collection:collection:1.1.0 (*)
|    +--- androidx.drawerlayout:drawerlayout:1.0.0
|    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    \--- androidx.customview:customview:1.0.0 (*)
|    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.lifecycle:lifecycle-runtime:2.3.1 (*)
|    \--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
+--- com.google.android.material:material:1.4.0
|    +--- androidx.annotation:annotation:1.0.1 -> 1.2.0
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    +--- androidx.cardview:cardview:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.coordinatorlayout:coordinatorlayout:1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.customview:customview:1.0.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.constraintlayout:constraintlayout:2.0.1 -> 2.1.4
|    |    +--- androidx.appcompat:appcompat:1.2.0 -> 1.3.1 (*)
|    |    +--- androidx.core:core:1.3.2 -> 1.6.0 (*)
|    |    \--- androidx.constraintlayout:constraintlayout-core:1.0.4
|    +--- androidx.core:core:1.5.0 -> 1.6.0 (*)
|    +--- androidx.dynamicanimation:dynamicanimation:1.0.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.legacy:legacy-support-core-utils:1.0.0
|    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |         +--- androidx.documentfile:documentfile:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.loader:loader:1.0.0 (*)
|    |         +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.print:print:1.0.0
|    |              \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.0.0 -> 1.2.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.3.2 -> 1.6.0 (*)
|    |    +--- androidx.customview:customview:1.0.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.transition:transition:1.2.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    \--- androidx.viewpager2:viewpager2:1.0.0
|         +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|         +--- androidx.fragment:fragment:1.1.0 -> 1.3.6 (*)
|         +--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
|         +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|         \--- androidx.collection:collection:1.1.0 (*)
+--- androidx.constraintlayout:constraintlayout:2.1.2 -> 2.1.4 (*)
+--- androidx.core:core-ktx:1.6.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.5.10 -> 1.8.21 (*)
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.core:core:1.6.0 (*)
+--- com.squareup.okhttp3:okhttp:4.9.3
|    +--- com.squareup.okio:okio:2.8.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 1.8.21 (*)
+--- com.squareup.okhttp3:logging-interceptor:4.9.3
|    +--- com.squareup.okhttp3:okhttp:4.9.3 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.21
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
|         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21
|              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- com.github.tbruyelle:rxpermissions:0.12
|    +--- io.reactivex.rxjava3:rxjava:3.0.4 -> 3.0.6
|    +--- androidx.fragment:fragment:1.2.5 -> 1.3.6 (*)
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
+--- io.reactivex.rxjava3:rxjava:3.0.6
+--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    \--- androidx.interpolator:interpolator:1.0.0 (*)
+--- org.greenrobot:eventbus:3.3.1
|    \--- org.greenrobot:eventbus-java:3.3.1
+--- me.drakeet.support:toastcompat:1.1.0
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.github.bumptech.glide:glide:4.11.0
|    +--- com.github.bumptech.glide:gifdecoder:4.11.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- com.github.bumptech.glide:disklrucache:4.11.0
|    +--- com.github.bumptech.glide:annotations:4.11.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.vectordrawable:vectordrawable-animated:1.0.0 -> 1.1.0 (*)
|    \--- androidx.exifinterface:exifinterface:1.0.0 -> 1.3.3
|         \--- androidx.annotation:annotation:1.2.0
+--- com.google.code.gson:gson:2.8.6
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1
|    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.4.1
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
+--- com.squareup.retrofit2:retrofit:2.9.0
|    \--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.3 (*)
+--- com.squareup.retrofit2:converter-gson:2.9.0
|    +--- com.squareup.retrofit2:retrofit:2.9.0 (*)
|    \--- com.google.code.gson:gson:2.8.5 -> 2.8.6
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21 (*)
+--- com.android.support:support-annotations:28.0.0 -> androidx.annotation:annotation:1.2.0
+--- cn.hutool:hutool-crypto:5.8.16
|    \--- cn.hutool:hutool-core:5.8.16
+--- com.squareup.retrofit2:converter-scalars:2.0.0
|    \--- com.squareup.retrofit2:retrofit:2.0.0 -> 2.9.0 (*)
+--- androidx.lifecycle:lifecycle-livedata-ktx:2.3.0
|    +--- androidx.lifecycle:lifecycle-livedata:2.3.0 (*)
|    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.3.0
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1 (*)
+--- io.github.lucksiege:pictureselector:v3.10.6
|    +--- androidx.appcompat:appcompat:1.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.2.1 (*)
|    +--- androidx.activity:activity:1.3.1 (*)
|    +--- androidx.fragment:fragment:1.3.1 -> 1.3.6 (*)
|    +--- androidx.exifinterface:exifinterface:1.3.3 (*)
|    +--- androidx.viewpager2:viewpager2:1.0.0 (*)
|    \--- androidx.constraintlayout:constraintlayout:2.1.4 (*)
+--- io.github.lucksiege:compress:v3.10.6
+--- com.aliyun.dpa:oss-android-sdk:2.9.19
|    \--- com.squareup.okhttp3:okhttp:3.11.0 -> 4.9.3 (*)
+--- com.afollestad.material-dialogs:core:3.3.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.3.61 -> 1.8.21 (*)
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    \--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
+--- com.afollestad.material-dialogs:bottomsheets:3.2.1
|    +--- com.afollestad.material-dialogs:core:3.2.1 -> 3.3.0 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.3.61 -> 1.8.21 (*)
|    \--- com.google.android.material:material:1.1.0 -> 1.4.0 (*)
+--- com.jakewharton.rxbinding2:rxbinding:2.0.0
|    +--- io.reactivex.rxjava2:rxjava:2.0.2
|    +--- io.reactivex.rxjava2:rxandroid:2.0.0
|    |    \--- io.reactivex.rxjava2:rxjava:2.0.0 -> 2.0.2
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.amazonaws:aws-android-sdk-s3:2.73.0
|    +--- com.amazonaws:aws-android-sdk-core:2.73.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- com.amazonaws:aws-android-sdk-kms:2.73.0
|    |    \--- com.amazonaws:aws-android-sdk-core:2.73.0 (*)
|    \--- androidx.appcompat:appcompat:1.2.0 -> 1.3.1 (*)
+--- com.android.support.constraint:constraint-layout:2.0.4 -> androidx.constraintlayout:constraintlayout:2.1.4 (*)
\--- androidx.multidex:multidex:2.0.1

atestReleaseRuntimeElements - Runtime elements for atestRelease (n)
No dependencies

atestReleaseRuntimeOnly - Runtime only dependencies for 'atestRelease' sources. (n)
No dependencies

atestReleaseUnitTestAnnotationProcessorClasspath - Resolved configuration for annotation-processor for variant: atestReleaseUnitTest
No dependencies

atestReleaseUnitTestApi (n)
No dependencies

atestReleaseUnitTestApiDependenciesMetadata
No dependencies

atestReleaseUnitTestCompilationApi - API dependencies for /atestReleaseUnitTest (n)
No dependencies

atestReleaseUnitTestCompilationCompileOnly - Compile only dependencies for /atestReleaseUnitTest. (n)
No dependencies

atestReleaseUnitTestCompilationImplementation - Implementation only dependencies for /atestReleaseUnitTest. (n)
No dependencies

atestReleaseUnitTestCompilationRuntimeOnly - Runtime only dependencies for /atestReleaseUnitTest. (n)
No dependencies

atestReleaseUnitTestCompileClasspath - Compile classpath for /atestReleaseUnitTest.
+--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-runtime:4.2.2
|    +--- androidx.databinding:viewbinding:4.2.2
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1
|    |    +--- androidx.lifecycle:lifecycle-common:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.arch.core:core-common:2.1.0
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.collection:collection:1.0.0 -> 1.1.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-adapters:4.2.2
|    +--- androidx.databinding:databinding-common:4.2.2
|    \--- androidx.databinding:databinding-runtime:4.2.2 (*)
+--- project :app (*)
+--- org.jetbrains.kotlin:kotlin-parcelize-runtime:1.8.21
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.8.21
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- androidx.appcompat:appcompat:1.3.1
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.5.0 -> 1.6.0
|    |    +--- androidx.annotation:annotation:1.2.0
|    |    +--- androidx.annotation:annotation-experimental:1.1.0
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    |    \--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |         +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |         \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.activity:activity:1.2.4 -> 1.3.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.savedstate:savedstate:1.1.0
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1
|    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1
|    |         |    \--- androidx.lifecycle:lifecycle-common:2.3.1 (*)
|    |         \--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    +--- androidx.fragment:fragment:1.3.6
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.2.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    +--- androidx.viewpager:viewpager:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    \--- androidx.customview:customview:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |         \--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.loader:loader:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.3.0
|    |    |    |    +--- androidx.arch.core:core-runtime:2.1.0
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    |    |    \--- androidx.arch.core:core-common:2.1.0 (*)
|    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.activity:activity:1.2.4 -> 1.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 (*)
|    |    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.appcompat:appcompat-resources:1.3.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    \--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |         +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |         +--- androidx.interpolator:interpolator:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.collection:collection:1.1.0 (*)
|    +--- androidx.drawerlayout:drawerlayout:1.0.0
|    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    \--- androidx.customview:customview:1.0.0 (*)
|    \--- androidx.savedstate:savedstate:1.1.0 (*)
+--- com.google.android.material:material:1.4.0
|    +--- androidx.annotation:annotation:1.0.1 -> 1.2.0
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    +--- androidx.cardview:cardview:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.coordinatorlayout:coordinatorlayout:1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.customview:customview:1.0.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.constraintlayout:constraintlayout:2.0.1 -> 2.1.4
|    +--- androidx.core:core:1.5.0 -> 1.6.0 (*)
|    +--- androidx.dynamicanimation:dynamicanimation:1.0.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.legacy:legacy-support-core-utils:1.0.0
|    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |         +--- androidx.documentfile:documentfile:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.loader:loader:1.0.0 (*)
|    |         +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.print:print:1.0.0
|    |              \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.0.0 -> 1.2.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.3.2 -> 1.6.0 (*)
|    |    \--- androidx.customview:customview:1.0.0 (*)
|    +--- androidx.transition:transition:1.2.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    \--- androidx.viewpager2:viewpager2:1.0.0
|         +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|         +--- androidx.fragment:fragment:1.1.0 -> 1.3.6 (*)
|         +--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
|         +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|         \--- androidx.collection:collection:1.1.0 (*)
+--- androidx.constraintlayout:constraintlayout:2.1.2 -> 2.1.4
+--- androidx.core:core-ktx:1.6.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.5.10 -> 1.8.21 (*)
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.core:core:1.6.0 (*)
+--- com.squareup.okhttp3:okhttp:4.9.3
|    +--- com.squareup.okio:okio:2.8.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 1.8.21 (*)
+--- com.squareup.okhttp3:logging-interceptor:4.9.3
|    +--- com.squareup.okhttp3:okhttp:4.9.3 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.21
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
|         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21
|              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- com.github.tbruyelle:rxpermissions:0.12
+--- io.reactivex.rxjava3:rxjava:3.0.6
+--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    \--- androidx.interpolator:interpolator:1.0.0 (*)
+--- org.greenrobot:eventbus:3.3.1
|    \--- org.greenrobot:eventbus-java:3.3.1
+--- me.drakeet.support:toastcompat:1.1.0
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.github.bumptech.glide:glide:4.11.0
|    +--- com.github.bumptech.glide:gifdecoder:4.11.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- com.github.bumptech.glide:disklrucache:4.11.0
|    +--- com.github.bumptech.glide:annotations:4.11.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.vectordrawable:vectordrawable-animated:1.0.0 -> 1.1.0 (*)
|    \--- androidx.exifinterface:exifinterface:1.0.0 -> 1.3.3
+--- com.google.code.gson:gson:2.8.6
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1
|    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.4.1
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
+--- com.squareup.retrofit2:retrofit:2.9.0
|    \--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.3 (*)
+--- com.squareup.retrofit2:converter-gson:2.9.0
|    +--- com.squareup.retrofit2:retrofit:2.9.0 (*)
|    \--- com.google.code.gson:gson:2.8.5 -> 2.8.6
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21 (*)
+--- com.android.support:support-annotations:28.0.0 -> androidx.annotation:annotation:1.2.0
+--- cn.hutool:hutool-crypto:5.8.16
|    \--- cn.hutool:hutool-core:5.8.16
+--- com.squareup.retrofit2:converter-scalars:2.0.0
|    \--- com.squareup.retrofit2:retrofit:2.0.0 -> 2.9.0 (*)
+--- androidx.lifecycle:lifecycle-livedata-ktx:2.3.0
|    +--- androidx.lifecycle:lifecycle-livedata:2.3.0 (*)
|    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.3.0
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1 (*)
+--- io.github.lucksiege:pictureselector:v3.10.6
|    +--- androidx.appcompat:appcompat:1.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.2.1 (*)
|    +--- androidx.activity:activity:1.3.1 (*)
|    +--- androidx.fragment:fragment:1.3.1 -> 1.3.6 (*)
|    +--- androidx.exifinterface:exifinterface:1.3.3
|    +--- androidx.viewpager2:viewpager2:1.0.0 (*)
|    \--- androidx.constraintlayout:constraintlayout:2.1.4
+--- io.github.lucksiege:compress:v3.10.6
+--- com.aliyun.dpa:oss-android-sdk:2.9.19
|    \--- com.squareup.okhttp3:okhttp:3.11.0 -> 4.9.3 (*)
+--- com.afollestad.material-dialogs:core:3.3.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.3.61 -> 1.8.21 (*)
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    \--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
+--- com.afollestad.material-dialogs:bottomsheets:3.2.1
|    \--- com.afollestad.material-dialogs:core:3.2.1 -> 3.3.0 (*)
+--- com.jakewharton.rxbinding2:rxbinding:2.0.0
|    +--- io.reactivex.rxjava2:rxjava:2.0.2
|    +--- io.reactivex.rxjava2:rxandroid:2.0.0
|    |    \--- io.reactivex.rxjava2:rxjava:2.0.0 -> 2.0.2
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.amazonaws:aws-android-sdk-s3:2.73.0
|    +--- com.amazonaws:aws-android-sdk-core:2.73.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- com.amazonaws:aws-android-sdk-kms:2.73.0
|    |    \--- com.amazonaws:aws-android-sdk-core:2.73.0 (*)
|    \--- androidx.appcompat:appcompat:1.2.0 -> 1.3.1 (*)
+--- com.android.support.constraint:constraint-layout:2.0.4 -> androidx.constraintlayout:constraintlayout:2.1.4
+--- androidx.multidex:multidex:2.0.1
+--- org.jetbrains.kotlin:kotlin-parcelize-runtime:{strictly 1.8.21} -> 1.8.21 (c)
+--- androidx.databinding:databinding-common:{strictly 4.2.2} -> 4.2.2 (c)
+--- androidx.databinding:databinding-runtime:{strictly 4.2.2} -> 4.2.2 (c)
+--- androidx.annotation:annotation:{strictly 1.2.0} -> 1.2.0 (c)
+--- androidx.databinding:databinding-adapters:{strictly 4.2.2} -> 4.2.2 (c)
+--- androidx.appcompat:appcompat:{strictly 1.3.1} -> 1.3.1 (c)
+--- com.google.android.material:material:{strictly 1.4.0} -> 1.4.0 (c)
+--- androidx.constraintlayout:constraintlayout:{strictly 2.1.4} -> 2.1.4 (c)
+--- androidx.core:core-ktx:{strictly 1.6.0} -> 1.6.0 (c)
+--- com.squareup.okhttp3:okhttp:{strictly 4.9.3} -> 4.9.3 (c)
+--- com.squareup.okhttp3:logging-interceptor:{strictly 4.9.3} -> 4.9.3 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:{strictly 1.8.21} -> 1.8.21 (c)
+--- com.github.tbruyelle:rxpermissions:{strictly 0.12} -> 0.12 (c)
+--- io.reactivex.rxjava3:rxjava:{strictly 3.0.6} -> 3.0.6 (c)
+--- androidx.swiperefreshlayout:swiperefreshlayout:{strictly 1.1.0} -> 1.1.0 (c)
+--- org.greenrobot:eventbus:{strictly 3.3.1} -> 3.3.1 (c)
+--- me.drakeet.support:toastcompat:{strictly 1.1.0} -> 1.1.0 (c)
+--- com.github.bumptech.glide:glide:{strictly 4.11.0} -> 4.11.0 (c)
+--- com.google.code.gson:gson:{strictly 2.8.6} -> 2.8.6 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:{strictly 1.4.1} -> 1.4.1 (c)
+--- com.squareup.retrofit2:retrofit:{strictly 2.9.0} -> 2.9.0 (c)
+--- com.squareup.retrofit2:converter-gson:{strictly 2.9.0} -> 2.9.0 (c)
+--- cn.hutool:hutool-crypto:{strictly 5.8.16} -> 5.8.16 (c)
+--- com.squareup.retrofit2:converter-scalars:{strictly 2.0.0} -> 2.0.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-ktx:{strictly 2.3.0} -> 2.3.0 (c)
+--- io.github.lucksiege:pictureselector:{strictly v3.10.6} -> v3.10.6 (c)
+--- io.github.lucksiege:compress:{strictly v3.10.6} -> v3.10.6 (c)
+--- com.aliyun.dpa:oss-android-sdk:{strictly 2.9.19} -> 2.9.19 (c)
+--- com.afollestad.material-dialogs:core:{strictly 3.3.0} -> 3.3.0 (c)
+--- com.afollestad.material-dialogs:bottomsheets:{strictly 3.2.1} -> 3.2.1 (c)
+--- com.jakewharton.rxbinding2:rxbinding:{strictly 2.0.0} -> 2.0.0 (c)
+--- com.amazonaws:aws-android-sdk-s3:{strictly 2.73.0} -> 2.73.0 (c)
+--- androidx.multidex:multidex:{strictly 2.0.1} -> 2.0.1 (c)
+--- androidx.databinding:viewbinding:{strictly 4.2.2} -> 4.2.2 (c)
+--- androidx.lifecycle:lifecycle-runtime:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.collection:collection:{strictly 1.1.0} -> 1.1.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib:{strictly 1.8.21} -> 1.8.21 (c)
+--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:{strictly 1.8.21} -> 1.8.21 (c)
+--- androidx.core:core:{strictly 1.6.0} -> 1.6.0 (c)
+--- androidx.cursoradapter:cursoradapter:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.activity:activity:{strictly 1.3.1} -> 1.3.1 (c)
+--- androidx.fragment:fragment:{strictly 1.3.6} -> 1.3.6 (c)
+--- androidx.appcompat:appcompat-resources:{strictly 1.3.1} -> 1.3.1 (c)
+--- androidx.drawerlayout:drawerlayout:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.savedstate:savedstate:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.cardview:cardview:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.coordinatorlayout:coordinatorlayout:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.dynamicanimation:dynamicanimation:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.annotation:annotation-experimental:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.recyclerview:recyclerview:{strictly 1.2.1} -> 1.2.1 (c)
+--- androidx.transition:transition:{strictly 1.2.0} -> 1.2.0 (c)
+--- androidx.vectordrawable:vectordrawable:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.viewpager2:viewpager2:{strictly 1.0.0} -> 1.0.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:{strictly 1.4.10} -> 1.8.21 (c)
+--- androidx.interpolator:interpolator:{strictly 1.0.0} -> 1.0.0 (c)
+--- org.greenrobot:eventbus-java:{strictly 3.3.1} -> 3.3.1 (c)
+--- com.github.bumptech.glide:gifdecoder:{strictly 4.11.0} -> 4.11.0 (c)
+--- com.github.bumptech.glide:disklrucache:{strictly 4.11.0} -> 4.11.0 (c)
+--- com.github.bumptech.glide:annotations:{strictly 4.11.0} -> 4.11.0 (c)
+--- androidx.vectordrawable:vectordrawable-animated:{strictly 1.1.0} -> 1.1.0 (c)
+--- androidx.exifinterface:exifinterface:{strictly 1.3.3} -> 1.3.3 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-core:{strictly 1.4.1} -> 1.4.1 (c)
+--- cn.hutool:hutool-core:{strictly 5.8.16} -> 5.8.16 (c)
+--- androidx.lifecycle:lifecycle-livedata:{strictly 2.3.0} -> 2.3.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-core-ktx:{strictly 2.3.0} -> 2.3.0 (c)
+--- io.reactivex.rxjava2:rxjava:{strictly 2.0.2} -> 2.0.2 (c)
+--- io.reactivex.rxjava2:rxandroid:{strictly 2.0.0} -> 2.0.0 (c)
+--- com.amazonaws:aws-android-sdk-core:{strictly 2.73.0} -> 2.73.0 (c)
+--- com.amazonaws:aws-android-sdk-kms:{strictly 2.73.0} -> 2.73.0 (c)
+--- androidx.lifecycle:lifecycle-common:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.arch.core:core-common:{strictly 2.1.0} -> 2.1.0 (c)
+--- org.jetbrains.kotlin:kotlin-stdlib-common:{strictly 1.8.21} -> 1.8.21 (c)
+--- org.jetbrains:annotations:{strictly 13.0} -> 13.0 (c)
+--- androidx.versionedparcelable:versionedparcelable:{strictly 1.1.1} -> 1.1.1 (c)
+--- androidx.lifecycle:lifecycle-viewmodel:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.lifecycle:lifecycle-viewmodel-savedstate:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.viewpager:viewpager:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.loader:loader:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.lifecycle:lifecycle-livedata-core:{strictly 2.3.1} -> 2.3.1 (c)
+--- androidx.customview:customview:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.legacy:legacy-support-core-utils:{strictly 1.0.0} -> 1.0.0 (c)
+--- com.squareup.okio:okio:{strictly 2.8.0} -> 2.8.0 (c)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:{strictly 1.4.1} -> 1.4.1 (c)
+--- androidx.arch.core:core-runtime:{strictly 2.1.0} -> 2.1.0 (c)
+--- androidx.documentfile:documentfile:{strictly 1.0.0} -> 1.0.0 (c)
+--- androidx.localbroadcastmanager:localbroadcastmanager:{strictly 1.0.0} -> 1.0.0 (c)
\--- androidx.print:print:{strictly 1.0.0} -> 1.0.0 (c)

atestReleaseUnitTestCompileOnly (n)
No dependencies

atestReleaseUnitTestCompileOnlyDependenciesMetadata
No dependencies

atestReleaseUnitTestImplementation (n)
No dependencies

atestReleaseUnitTestImplementationDependenciesMetadata
No dependencies

atestReleaseUnitTestIntransitiveDependenciesMetadata
No dependencies

atestReleaseUnitTestRuntimeClasspath - Runtime classpath of /atestReleaseUnitTest.
+--- project :app (*)
+--- org.jetbrains.kotlin:kotlin-parcelize-runtime:1.8.21
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    \--- org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.8.21
|         \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-runtime:4.2.2
|    +--- androidx.databinding:viewbinding:4.2.2
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1
|    |    +--- androidx.arch.core:core-runtime:2.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    \--- androidx.arch.core:core-common:2.1.0
|    |    |         \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.lifecycle:lifecycle-common:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.arch.core:core-common:2.1.0 (*)
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.collection:collection:1.0.0 -> 1.1.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-adapters:4.2.2
|    +--- androidx.databinding:databinding-common:4.2.2
|    \--- androidx.databinding:databinding-runtime:4.2.2 (*)
+--- androidx.appcompat:appcompat:1.3.1
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.5.0 -> 1.6.0
|    |    +--- androidx.annotation:annotation:1.2.0
|    |    +--- androidx.annotation:annotation-experimental:1.1.0
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.activity:activity:1.2.4 -> 1.3.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.savedstate:savedstate:1.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    +--- androidx.arch.core:core-common:2.0.1 -> 2.1.0 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-common:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1
|    |    |    |    +--- androidx.arch.core:core-common:2.1.0 (*)
|    |    |    |    +--- androidx.arch.core:core-runtime:2.1.0 (*)
|    |    |    |    \--- androidx.lifecycle:lifecycle-common:2.3.1 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.tracing:tracing:1.0.0
|    |         \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.fragment:fragment:1.3.6
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.2.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    +--- androidx.viewpager:viewpager:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    \--- androidx.customview:customview:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |         \--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.loader:loader:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.3.0
|    |    |    |    +--- androidx.arch.core:core-common:2.1.0 (*)
|    |    |    |    +--- androidx.arch.core:core-runtime:2.1.0 (*)
|    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.activity:activity:1.2.4 -> 1.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 (*)
|    |    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.appcompat:appcompat-resources:1.3.1
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    \--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |         +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |         +--- androidx.interpolator:interpolator:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.collection:collection:1.1.0 (*)
|    +--- androidx.drawerlayout:drawerlayout:1.0.0
|    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    \--- androidx.customview:customview:1.0.0 (*)
|    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.lifecycle:lifecycle-runtime:2.3.1 (*)
|    \--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
+--- com.google.android.material:material:1.4.0
|    +--- androidx.annotation:annotation:1.0.1 -> 1.2.0
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    +--- androidx.cardview:cardview:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.coordinatorlayout:coordinatorlayout:1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.customview:customview:1.0.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.constraintlayout:constraintlayout:2.0.1 -> 2.1.4
|    |    +--- androidx.appcompat:appcompat:1.2.0 -> 1.3.1 (*)
|    |    +--- androidx.core:core:1.3.2 -> 1.6.0 (*)
|    |    \--- androidx.constraintlayout:constraintlayout-core:1.0.4
|    +--- androidx.core:core:1.5.0 -> 1.6.0 (*)
|    +--- androidx.dynamicanimation:dynamicanimation:1.0.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.legacy:legacy-support-core-utils:1.0.0
|    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |         +--- androidx.documentfile:documentfile:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.loader:loader:1.0.0 (*)
|    |         +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.print:print:1.0.0
|    |              \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.0.0 -> 1.2.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.3.2 -> 1.6.0 (*)
|    |    +--- androidx.customview:customview:1.0.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.transition:transition:1.2.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    \--- androidx.viewpager2:viewpager2:1.0.0
|         +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|         +--- androidx.fragment:fragment:1.1.0 -> 1.3.6 (*)
|         +--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
|         +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|         \--- androidx.collection:collection:1.1.0 (*)
+--- androidx.constraintlayout:constraintlayout:2.1.2 -> 2.1.4 (*)
+--- androidx.core:core-ktx:1.6.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.5.10 -> 1.8.21 (*)
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.core:core:1.6.0 (*)
+--- com.squareup.okhttp3:okhttp:4.9.3
|    +--- com.squareup.okio:okio:2.8.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 1.8.21 (*)
+--- com.squareup.okhttp3:logging-interceptor:4.9.3
|    +--- com.squareup.okhttp3:okhttp:4.9.3 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 1.8.21 (*)
|         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.10 -> 1.8.21
|              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- com.github.tbruyelle:rxpermissions:0.12
|    +--- io.reactivex.rxjava3:rxjava:3.0.4 -> 3.0.6
|    +--- androidx.fragment:fragment:1.2.5 -> 1.3.6 (*)
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
+--- io.reactivex.rxjava3:rxjava:3.0.6
+--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    \--- androidx.interpolator:interpolator:1.0.0 (*)
+--- org.greenrobot:eventbus:3.3.1
|    \--- org.greenrobot:eventbus-java:3.3.1
+--- me.drakeet.support:toastcompat:1.1.0
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.github.bumptech.glide:glide:4.11.0
|    +--- com.github.bumptech.glide:gifdecoder:4.11.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- com.github.bumptech.glide:disklrucache:4.11.0
|    +--- com.github.bumptech.glide:annotations:4.11.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.vectordrawable:vectordrawable-animated:1.0.0 -> 1.1.0 (*)
|    \--- androidx.exifinterface:exifinterface:1.0.0 -> 1.3.3
|         \--- androidx.annotation:annotation:1.2.0
+--- com.google.code.gson:gson:2.8.6
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1
|    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.4.1
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
+--- com.squareup.retrofit2:retrofit:2.9.0
|    \--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.3 (*)
+--- com.squareup.retrofit2:converter-gson:2.9.0
|    +--- com.squareup.retrofit2:retrofit:2.9.0 (*)
|    \--- com.google.code.gson:gson:2.8.5 -> 2.8.6
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21 (*)
+--- com.android.support:support-annotations:28.0.0 -> androidx.annotation:annotation:1.2.0
+--- cn.hutool:hutool-crypto:5.8.16
|    \--- cn.hutool:hutool-core:5.8.16
+--- com.squareup.retrofit2:converter-scalars:2.0.0
|    \--- com.squareup.retrofit2:retrofit:2.0.0 -> 2.9.0 (*)
+--- androidx.lifecycle:lifecycle-livedata-ktx:2.3.0
|    +--- androidx.lifecycle:lifecycle-livedata:2.3.0 (*)
|    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.3.0
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1 (*)
+--- io.github.lucksiege:pictureselector:v3.10.6
|    +--- androidx.appcompat:appcompat:1.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.2.1 (*)
|    +--- androidx.activity:activity:1.3.1 (*)
|    +--- androidx.fragment:fragment:1.3.1 -> 1.3.6 (*)
|    +--- androidx.exifinterface:exifinterface:1.3.3 (*)
|    +--- androidx.viewpager2:viewpager2:1.0.0 (*)
|    \--- androidx.constraintlayout:constraintlayout:2.1.4 (*)
+--- io.github.lucksiege:compress:v3.10.6
+--- com.aliyun.dpa:oss-android-sdk:2.9.19
|    \--- com.squareup.okhttp3:okhttp:3.11.0 -> 4.9.3 (*)
+--- com.afollestad.material-dialogs:core:3.3.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.3.61 -> 1.4.10 (*)
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    \--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
+--- com.afollestad.material-dialogs:bottomsheets:3.2.1
|    +--- com.afollestad.material-dialogs:core:3.2.1 -> 3.3.0 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.3.61 -> 1.4.10 (*)
|    \--- com.google.android.material:material:1.1.0 -> 1.4.0 (*)
+--- com.jakewharton.rxbinding2:rxbinding:2.0.0
|    +--- io.reactivex.rxjava2:rxjava:2.0.2
|    +--- io.reactivex.rxjava2:rxandroid:2.0.0
|    |    \--- io.reactivex.rxjava2:rxjava:2.0.0 -> 2.0.2
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.amazonaws:aws-android-sdk-s3:2.73.0
|    +--- com.amazonaws:aws-android-sdk-core:2.73.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- com.amazonaws:aws-android-sdk-kms:2.73.0
|    |    \--- com.amazonaws:aws-android-sdk-core:2.73.0 (*)
|    \--- androidx.appcompat:appcompat:1.2.0 -> 1.3.1 (*)
+--- com.android.support.constraint:constraint-layout:2.0.4 -> androidx.constraintlayout:constraintlayout:2.1.4 (*)
\--- androidx.multidex:multidex:2.0.1

atestReleaseUnitTestRuntimeOnly (n)
No dependencies

atestReleaseWearApp - Link to a wear app to embed for object 'atestRelease'. (n)
No dependencies

atestReleaseWearBundling - Resolved Configuration for wear app bundling for variant: atestRelease
No dependencies

atestRuntimeOnly - Runtime only dependencies for 'atest' sources. (n)
No dependencies

atestWearApp - Link to a wear app to embed for object 'atest'. (n)
No dependencies

compile - Compile dependencies for 'main' sources (deprecated: use 'implementation' instead).
No dependencies

compileOnly - Compile only dependencies for 'main' sources. (n)
No dependencies

compileOnlyDependenciesMetadata
No dependencies

coreLibraryDesugaring - Configuration to desugar libraries
No dependencies

debugAnnotationProcessor - Classpath for the annotation processor for 'debug'. (n)
No dependencies

debugApi - API dependencies for 'debug' sources. (n)
No dependencies

debugApiDependenciesMetadata
No dependencies

debugApk - Apk dependencies for 'debug' sources (deprecated: use 'debugRuntimeOnly' instead). (n)
No dependencies

debugCompile - Compile dependencies for 'debug' sources (deprecated: use 'debugImplementation' instead). (n)
No dependencies

debugCompileOnly - Compile only dependencies for 'debug' sources. (n)
No dependencies

debugCompileOnlyDependenciesMetadata
No dependencies

debugImplementation - Implementation only dependencies for 'debug' sources. (n)
No dependencies

debugImplementationDependenciesMetadata
No dependencies

debugIntransitiveDependenciesMetadata
No dependencies

debugProvided - Provided dependencies for 'debug' sources (deprecated: use 'debugCompileOnly' instead). (n)
No dependencies

debugRuntimeOnly - Runtime only dependencies for 'debug' sources. (n)
No dependencies

debugWearApp - Link to a wear app to embed for object 'debug'. (n)
No dependencies

default - Configuration for default artifacts. (n)
No dependencies

implementation - Implementation only dependencies for 'main' sources. (n)
+--- unspecified (n)
+--- androidx.appcompat:appcompat:1.3.1 (n)
+--- com.google.android.material:material:1.4.0 (n)
+--- androidx.constraintlayout:constraintlayout:2.1.2 (n)
+--- androidx.core:core-ktx:1.6.0 (n)
+--- com.squareup.okhttp3:okhttp:4.9.3 (n)
+--- com.squareup.okhttp3:logging-interceptor:4.9.3 (n)
+--- com.github.tbruyelle:rxpermissions:0.12 (n)
+--- io.reactivex.rxjava3:rxjava:3.0.6 (n)
+--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0 (n)
+--- org.greenrobot:eventbus:3.3.1 (n)
+--- me.drakeet.support:toastcompat:1.1.0 (n)
+--- com.github.bumptech.glide:glide:4.11.0 (n)
+--- com.google.code.gson:gson:2.8.6 (n)
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1 (n)
+--- com.squareup.retrofit2:retrofit:2.9.0 (n)
+--- com.squareup.retrofit2:converter-gson:2.9.0 (n)
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21 (n)
+--- com.android.support:support-annotations:28.0.0 (n)
+--- cn.hutool:hutool-crypto:5.8.16 (n)
+--- com.squareup.retrofit2:converter-scalars:2.0.0 (n)
+--- androidx.lifecycle:lifecycle-livedata-ktx:2.3.0 (n)
+--- io.github.lucksiege:pictureselector:v3.10.6 (n)
+--- io.github.lucksiege:compress:v3.10.6 (n)
+--- com.aliyun.dpa:oss-android-sdk:2.9.19 (n)
+--- com.afollestad.material-dialogs:core:3.3.0 (n)
+--- com.afollestad.material-dialogs:bottomsheets:3.2.1 (n)
+--- com.jakewharton.rxbinding2:rxbinding:2.0.0 (n)
+--- com.amazonaws:aws-android-sdk-s3:2.73.0 (n)
+--- com.android.support.constraint:constraint-layout:2.0.4 (n)
+--- androidx.multidex:multidex:2.0.1 (n)
+--- unspecified (n)
+--- unspecified (n)
\--- unspecified (n)

implementationDependenciesMetadata
+--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-runtime:4.2.2
|    +--- androidx.databinding:viewbinding:4.2.2
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1
|    |    +--- androidx.lifecycle:lifecycle-common:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.arch.core:core-common:2.1.0
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.collection:collection:1.0.0 -> 1.1.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.databinding:databinding-common:4.2.2
+--- androidx.databinding:databinding-adapters:4.2.2
|    +--- androidx.databinding:databinding-common:4.2.2
|    \--- androidx.databinding:databinding-runtime:4.2.2 (*)
+--- androidx.appcompat:appcompat:1.3.1
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.5.0 -> 1.6.0
|    |    +--- androidx.annotation:annotation:1.2.0
|    |    +--- androidx.annotation:annotation-experimental:1.1.0
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    |    \--- androidx.versionedparcelable:versionedparcelable:1.1.1
|    |         +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |         \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.cursoradapter:cursoradapter:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.activity:activity:1.2.4 -> 1.3.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.lifecycle:lifecycle-runtime:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.savedstate:savedstate:1.1.0
|    |    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    \--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1
|    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |         +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1
|    |         |    \--- androidx.lifecycle:lifecycle-common:2.3.1 (*)
|    |         \--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    +--- androidx.fragment:fragment:1.3.6
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.2.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.1.0 (*)
|    |    +--- androidx.viewpager:viewpager:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    \--- androidx.customview:customview:1.0.0
|    |    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |         \--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.loader:loader:1.0.0
|    |    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    |    +--- androidx.lifecycle:lifecycle-livedata:2.0.0 -> 2.3.0
|    |    |    |    +--- androidx.arch.core:core-runtime:2.1.0
|    |    |    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    |    |    \--- androidx.arch.core:core-common:2.1.0 (*)
|    |    |    |    \--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    |    \--- androidx.lifecycle:lifecycle-viewmodel:2.0.0 -> 2.3.1 (*)
|    |    +--- androidx.activity:activity:1.2.4 -> 1.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel:2.3.1 (*)
|    |    +--- androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1 (*)
|    |    +--- androidx.savedstate:savedstate:1.1.0 (*)
|    |    \--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.appcompat:appcompat-resources:1.3.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    +--- androidx.vectordrawable:vectordrawable:1.1.0
|    |    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    |    \--- androidx.collection:collection:1.1.0 (*)
|    |    \--- androidx.vectordrawable:vectordrawable-animated:1.1.0
|    |         +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    |         +--- androidx.interpolator:interpolator:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.collection:collection:1.1.0 (*)
|    +--- androidx.drawerlayout:drawerlayout:1.0.0
|    |    +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    \--- androidx.customview:customview:1.0.0 (*)
|    \--- androidx.savedstate:savedstate:1.1.0 (*)
+--- com.google.android.material:material:1.4.0
|    +--- androidx.annotation:annotation:1.0.1 -> 1.2.0
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    +--- androidx.cardview:cardview:1.0.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.coordinatorlayout:coordinatorlayout:1.1.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    |    +--- androidx.customview:customview:1.0.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.constraintlayout:constraintlayout:2.0.1 -> 2.1.4
|    +--- androidx.core:core:1.5.0 -> 1.6.0 (*)
|    +--- androidx.dynamicanimation:dynamicanimation:1.0.0
|    |    +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |    +--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    |    \--- androidx.legacy:legacy-support-core-utils:1.0.0
|    |         +--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.core:core:1.0.0 -> 1.6.0 (*)
|    |         +--- androidx.documentfile:documentfile:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         +--- androidx.loader:loader:1.0.0 (*)
|    |         +--- androidx.localbroadcastmanager:localbroadcastmanager:1.0.0
|    |         |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    |         \--- androidx.print:print:1.0.0
|    |              \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- androidx.annotation:annotation-experimental:1.0.0 -> 1.1.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.lifecycle:lifecycle-runtime:2.0.0 -> 2.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.0.0 -> 1.2.1
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.3.2 -> 1.6.0 (*)
|    |    \--- androidx.customview:customview:1.0.0 (*)
|    +--- androidx.transition:transition:1.2.0
|    |    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    |    +--- androidx.core:core:1.0.1 -> 1.6.0 (*)
|    |    \--- androidx.collection:collection:1.0.0 -> 1.1.0 (*)
|    +--- androidx.vectordrawable:vectordrawable:1.1.0 (*)
|    \--- androidx.viewpager2:viewpager2:1.0.0
|         +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|         +--- androidx.fragment:fragment:1.1.0 -> 1.3.6 (*)
|         +--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
|         +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|         \--- androidx.collection:collection:1.1.0 (*)
+--- androidx.constraintlayout:constraintlayout:2.1.2 -> 2.1.4
+--- androidx.core:core-ktx:1.6.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.5.10 -> 1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    \--- androidx.core:core:1.6.0 (*)
+--- com.squareup.okhttp3:okhttp:4.9.3
|    +--- com.squareup.okio:okio:2.8.0
|    |    \--- com.squareup.okio:okio-metadata:2.8.0
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.10 -> 1.8.21 (*)
+--- com.squareup.okhttp3:logging-interceptor:4.9.3
|    +--- com.squareup.okhttp3:okhttp:4.9.3 (*)
|    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.10 -> 1.8.21
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
|         \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21
|              \--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
+--- com.github.tbruyelle:rxpermissions:0.12
+--- io.reactivex.rxjava3:rxjava:3.0.6
+--- androidx.swiperefreshlayout:swiperefreshlayout:1.1.0
|    +--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- androidx.core:core:1.1.0 -> 1.6.0 (*)
|    \--- androidx.interpolator:interpolator:1.0.0 (*)
+--- org.greenrobot:eventbus:3.3.1
|    \--- org.greenrobot:eventbus-java:3.3.1
+--- me.drakeet.support:toastcompat:1.1.0
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.github.bumptech.glide:glide:4.11.0
|    +--- com.github.bumptech.glide:gifdecoder:4.11.0
|    |    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
|    +--- com.github.bumptech.glide:disklrucache:4.11.0
|    +--- com.github.bumptech.glide:annotations:4.11.0
|    +--- androidx.fragment:fragment:1.0.0 -> 1.3.6 (*)
|    +--- androidx.vectordrawable:vectordrawable-animated:1.0.0 -> 1.1.0 (*)
|    \--- androidx.exifinterface:exifinterface:1.0.0 -> 1.3.3
+--- com.google.code.gson:gson:2.8.6
+--- org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1
|    +--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1
|    |    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core-metadata:1.4.1
|    |         +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    |         \--- org.jetbrains.kotlinx:atomicfu:0.14.4
|    |              \--- org.jetbrains.kotlinx:atomicfu-common:0.14.4
|    |                   \--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.0 -> 1.8.21
|    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.0 -> 1.8.21 (*)
+--- com.squareup.retrofit2:retrofit:2.9.0
|    \--- com.squareup.okhttp3:okhttp:3.14.9 -> 4.9.3 (*)
+--- com.squareup.retrofit2:converter-gson:2.9.0
|    +--- com.squareup.retrofit2:retrofit:2.9.0 (*)
|    \--- com.google.code.gson:gson:2.8.5 -> 2.8.6
+--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.21 (*)
+--- com.android.support:support-annotations:28.0.0 -> androidx.annotation:annotation:1.2.0
+--- cn.hutool:hutool-crypto:5.8.16
|    \--- cn.hutool:hutool-core:5.8.16
+--- com.squareup.retrofit2:converter-scalars:2.0.0
|    \--- com.squareup.retrofit2:retrofit:2.0.0 -> 2.9.0 (*)
+--- androidx.lifecycle:lifecycle-livedata-ktx:2.3.0
|    +--- androidx.lifecycle:lifecycle-livedata:2.3.0 (*)
|    +--- androidx.lifecycle:lifecycle-livedata-core-ktx:2.3.0
|    |    +--- androidx.lifecycle:lifecycle-livedata-core:2.3.0 -> 2.3.1 (*)
|    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.20 -> 1.8.21 (*)
|    \--- org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1 (*)
+--- io.github.lucksiege:pictureselector:v3.10.6
|    +--- androidx.appcompat:appcompat:1.3.1 (*)
|    +--- androidx.recyclerview:recyclerview:1.2.1 (*)
|    +--- androidx.activity:activity:1.3.1 (*)
|    +--- androidx.fragment:fragment:1.3.1 -> 1.3.6 (*)
|    +--- androidx.exifinterface:exifinterface:1.3.3
|    +--- androidx.viewpager2:viewpager2:1.0.0 (*)
|    \--- androidx.constraintlayout:constraintlayout:2.1.4
+--- io.github.lucksiege:compress:v3.10.6
+--- com.aliyun.dpa:oss-android-sdk:2.9.19
|    \--- com.squareup.okhttp3:okhttp:3.11.0 -> 4.9.3 (*)
+--- com.afollestad.material-dialogs:core:3.3.0
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.3.61 -> 1.8.21 (*)
|    +--- androidx.appcompat:appcompat:1.1.0 -> 1.3.1 (*)
|    \--- androidx.recyclerview:recyclerview:1.1.0 -> 1.2.1 (*)
+--- com.afollestad.material-dialogs:bottomsheets:3.2.1
|    \--- com.afollestad.material-dialogs:core:3.2.1 -> 3.3.0 (*)
+--- com.jakewharton.rxbinding2:rxbinding:2.0.0
|    +--- io.reactivex.rxjava2:rxjava:2.0.2
|    +--- io.reactivex.rxjava2:rxandroid:2.0.0
|    |    \--- io.reactivex.rxjava2:rxjava:2.0.0 -> 2.0.2
|    \--- androidx.annotation:annotation:1.0.0 -> 1.2.0
+--- com.amazonaws:aws-android-sdk-s3:2.73.0
|    +--- com.amazonaws:aws-android-sdk-core:2.73.0
|    |    \--- androidx.annotation:annotation:1.1.0 -> 1.2.0
|    +--- com.amazonaws:aws-android-sdk-kms:2.73.0
|    |    \--- com.amazonaws:aws-android-sdk-core:2.73.0 (*)
|    \--- androidx.appcompat:appcompat:1.2.0 -> 1.3.1 (*)
+--- com.android.support.constraint:constraint-layout:2.0.4 -> androidx.constraintlayout:constraintlayout:2.1.4
\--- androidx.multidex:multidex:2.0.1

intransitiveDependenciesMetadata
No dependencies

kapt
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptAndroidTest
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptAndroidTestAtest
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptAndroidTestAtestDebug
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptAndroidTestDebug
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptAndroidTestRelease
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptAtest
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptAtestDebug
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptAtestRelease
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptClasspath_kaptAtestDebugAndroidTestKotlin
No dependencies

kaptClasspath_kaptAtestDebugKotlin
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptClasspath_kaptAtestDebugUnitTestKotlin
No dependencies

kaptClasspath_kaptAtestReleaseKotlin
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptClasspath_kaptAtestReleaseUnitTestKotlin
No dependencies

kaptDebug
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptRelease
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptTest
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptTestAtest
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptTestAtestDebug
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptTestAtestRelease
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptTestDebug
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kaptTestRelease
+--- com.github.bumptech.glide:compiler:4.11.0
|    \--- com.github.bumptech.glide:annotations:4.11.0
+--- com.android.databinding:compiler:3.1.4 -> androidx.databinding:databinding-compiler:4.2.2
|    +--- androidx.databinding:databinding-compiler-common:4.2.2
|    |    +--- androidx.databinding:databinding-common:4.2.2
|    |    +--- com.android.databinding:baseLibrary:4.2.2
|    |    +--- org.antlr:antlr4:4.5.3
|    |    +--- commons-io:commons-io:2.4
|    |    +--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
|    |    +--- com.google.guava:guava:28.1-jre
|    |    |    +--- com.google.guava:failureaccess:1.0.1
|    |    |    +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    +--- org.checkerframework:checker-qual:2.8.1
|    |    |    +--- com.google.errorprone:error_prone_annotations:2.3.2
|    |    |    +--- com.google.j2objc:j2objc-annotations:1.3
|    |    |    \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
|    |    +--- com.squareup:javapoet:1.10.0
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
|    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
|    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
|    |    |    |    \--- org.jetbrains:annotations:13.0
|    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
|    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
|    |    +--- com.google.code.gson:gson:2.8.6
|    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
|    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
|    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
|    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
|    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
|    |    +--- com.android.tools:annotations:27.2.2
|    |    \--- com.android.tools.build.jetifier:jetifier-core:1.0.0-beta09
|    |         +--- com.google.code.gson:gson:2.8.0 -> 2.8.6
|    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.3.60 -> 1.4.31 (*)
|    +--- androidx.databinding:databinding-common:4.2.2
|    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
|    +--- com.google.auto:auto-common:0.10
|    |    \--- com.google.guava:guava:23.5-jre -> 28.1-jre (*)
|    +--- commons-io:commons-io:2.4
|    +--- commons-codec:commons-codec:1.10
|    +--- org.antlr:antlr4:4.5.3
|    \--- com.googlecode.juniversalchardet:juniversalchardet:1.0.3
\--- androidx.databinding:databinding-compiler:4.2.2 (*)

kotlinCompilerClasspath
\--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.8.21
     +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
     |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
     |    \--- org.jetbrains:annotations:13.0
     +--- org.jetbrains.kotlin:kotlin-script-runtime:1.8.21
     +--- org.jetbrains.kotlin:kotlin-reflect:1.6.10
     +--- org.jetbrains.kotlin:kotlin-daemon-embeddable:1.8.21
     +--- org.jetbrains.intellij.deps:trove4j:1.0.20200330
     \--- net.java.dev.jna:jna:5.6.0

kotlinCompilerPluginClasspath
No dependencies

kotlinCompilerPluginClasspathAtestDebug - Kotlin compiler plugins for compilation
+--- org.jetbrains.kotlin:kotlin-annotation-processing-gradle:1.8.21
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    \--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.8.21
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
|         +--- org.jetbrains.kotlin:kotlin-script-runtime:1.8.21
|         +--- org.jetbrains.kotlin:kotlin-reflect:1.6.10
|         +--- org.jetbrains.kotlin:kotlin-daemon-embeddable:1.8.21
|         +--- org.jetbrains.intellij.deps:trove4j:1.0.20200330
|         \--- net.java.dev.jna:jna:5.6.0
\--- org.jetbrains.kotlin:kotlin-parcelize-compiler:1.8.21
     \--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.8.21 (*)

kotlinCompilerPluginClasspathAtestDebugAndroidTest - Kotlin compiler plugins for compilation
+--- org.jetbrains.kotlin:kotlin-annotation-processing-gradle:1.8.21
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    \--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.8.21
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
|         +--- org.jetbrains.kotlin:kotlin-script-runtime:1.8.21
|         +--- org.jetbrains.kotlin:kotlin-reflect:1.6.10
|         +--- org.jetbrains.kotlin:kotlin-daemon-embeddable:1.8.21
|         +--- org.jetbrains.intellij.deps:trove4j:1.0.20200330
|         \--- net.java.dev.jna:jna:5.6.0
\--- org.jetbrains.kotlin:kotlin-parcelize-compiler:1.8.21
     \--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.8.21 (*)

kotlinCompilerPluginClasspathAtestDebugUnitTest - Kotlin compiler plugins for compilation
+--- org.jetbrains.kotlin:kotlin-annotation-processing-gradle:1.8.21
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    \--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.8.21
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
|         +--- org.jetbrains.kotlin:kotlin-script-runtime:1.8.21
|         +--- org.jetbrains.kotlin:kotlin-reflect:1.6.10
|         +--- org.jetbrains.kotlin:kotlin-daemon-embeddable:1.8.21
|         +--- org.jetbrains.intellij.deps:trove4j:1.0.20200330
|         \--- net.java.dev.jna:jna:5.6.0
\--- org.jetbrains.kotlin:kotlin-parcelize-compiler:1.8.21
     \--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.8.21 (*)

kotlinCompilerPluginClasspathAtestRelease - Kotlin compiler plugins for compilation
+--- org.jetbrains.kotlin:kotlin-annotation-processing-gradle:1.8.21
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    \--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.8.21
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
|         +--- org.jetbrains.kotlin:kotlin-script-runtime:1.8.21
|         +--- org.jetbrains.kotlin:kotlin-reflect:1.6.10
|         +--- org.jetbrains.kotlin:kotlin-daemon-embeddable:1.8.21
|         +--- org.jetbrains.intellij.deps:trove4j:1.0.20200330
|         \--- net.java.dev.jna:jna:5.6.0
\--- org.jetbrains.kotlin:kotlin-parcelize-compiler:1.8.21
     \--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.8.21 (*)

kotlinCompilerPluginClasspathAtestReleaseUnitTest - Kotlin compiler plugins for compilation
+--- org.jetbrains.kotlin:kotlin-annotation-processing-gradle:1.8.21
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    \--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.8.21
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
|         +--- org.jetbrains.kotlin:kotlin-script-runtime:1.8.21
|         +--- org.jetbrains.kotlin:kotlin-reflect:1.6.10
|         +--- org.jetbrains.kotlin:kotlin-daemon-embeddable:1.8.21
|         +--- org.jetbrains.intellij.deps:trove4j:1.0.20200330
|         \--- net.java.dev.jna:jna:5.6.0
\--- org.jetbrains.kotlin:kotlin-parcelize-compiler:1.8.21
     \--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.8.21 (*)

kotlinKaptWorkerDependencies
+--- org.jetbrains.kotlin:kotlin-annotation-processing-gradle:1.8.21
|    +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
|    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
|    |    \--- org.jetbrains:annotations:13.0
|    \--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.8.21
|         +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
|         +--- org.jetbrains.kotlin:kotlin-script-runtime:1.8.21
|         +--- org.jetbrains.kotlin:kotlin-reflect:1.6.10
|         +--- org.jetbrains.kotlin:kotlin-daemon-embeddable:1.8.21
|         +--- org.jetbrains.intellij.deps:trove4j:1.0.20200330
|         \--- net.java.dev.jna:jna:5.6.0
\--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)

kotlinKlibCommonizerClasspath
\--- org.jetbrains.kotlin:kotlin-klib-commonizer-embeddable:1.8.21
     +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21
     |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21
     |    \--- org.jetbrains:annotations:13.0
     \--- org.jetbrains.kotlin:kotlin-compiler-embeddable:1.8.21
          +--- org.jetbrains.kotlin:kotlin-stdlib:1.8.21 (*)
          +--- org.jetbrains.kotlin:kotlin-script-runtime:1.8.21
          +--- org.jetbrains.kotlin:kotlin-reflect:1.6.10
          +--- org.jetbrains.kotlin:kotlin-daemon-embeddable:1.8.21
          +--- org.jetbrains.intellij.deps:trove4j:1.0.20200330
          \--- net.java.dev.jna:jna:5.6.0

kotlinNativeCompilerPluginClasspath
No dependencies

lintChecks - Configuration to apply external lint check jar
No dependencies

lintClassPath - The lint embedded classpath
\--- com.android.tools.lint:lint-gradle:27.2.2
     +--- com.android.tools:sdk-common:27.2.2
     |    +--- com.android.tools:sdklib:27.2.2
     |    |    +--- com.android.tools.layoutlib:layoutlib-api:27.2.2
     |    |    |    +--- com.android.tools:common:27.2.2
     |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31
     |    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31
     |    |    |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-common:1.4.31
     |    |    |    |    |    |    \--- org.jetbrains:annotations:13.0
     |    |    |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.4.31
     |    |    |    |    |         \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
     |    |    |    |    +--- com.android.tools:annotations:27.2.2
     |    |    |    |    \--- com.google.guava:guava:28.1-jre
     |    |    |    |         +--- com.google.guava:failureaccess:1.0.1
     |    |    |    |         +--- com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava
     |    |    |    |         +--- com.google.code.findbugs:jsr305:3.0.2
     |    |    |    |         +--- org.checkerframework:checker-qual:2.8.1
     |    |    |    |         +--- com.google.errorprone:error_prone_annotations:2.3.2
     |    |    |    |         +--- com.google.j2objc:j2objc-annotations:1.3
     |    |    |    |         \--- org.codehaus.mojo:animal-sniffer-annotations:1.18
     |    |    |    +--- net.sf.kxml:kxml2:2.3.0
     |    |    |    +--- com.android.tools:annotations:27.2.2
     |    |    |    \--- org.jetbrains:annotations:13.0
     |    |    +--- com.android.tools:dvlib:27.2.2
     |    |    |    \--- com.android.tools:common:27.2.2 (*)
     |    |    +--- com.android.tools:repository:27.2.2
     |    |    |    +--- com.android.tools:common:27.2.2 (*)
     |    |    |    +--- com.sun.activation:javax.activation:1.2.0
     |    |    |    +--- org.apache.commons:commons-compress:1.12
     |    |    |    +--- org.glassfish.jaxb:jaxb-runtime:2.3.2
     |    |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2
     |    |    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
     |    |    |    |    +--- org.glassfish.jaxb:txw2:2.3.2
     |    |    |    |    +--- com.sun.istack:istack-commons-runtime:3.0.8
     |    |    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
     |    |    |    |    +--- org.jvnet.staxex:stax-ex:1.8.1
     |    |    |    |    |    +--- jakarta.activation:jakarta.activation-api:1.2.1
     |    |    |    |    |    \--- jakarta.xml.bind:jakarta.xml.bind-api:2.3.2 (*)
     |    |    |    |    +--- com.sun.xml.fastinfoset:FastInfoset:1.2.16
     |    |    |    |    \--- jakarta.activation:jakarta.activation-api:1.2.1
     |    |    |    +--- com.google.jimfs:jimfs:1.1
     |    |    |    |    \--- com.google.guava:guava:18.0 -> 28.1-jre (*)
     |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
     |    |    +--- com.google.code.gson:gson:2.8.6
     |    |    +--- org.apache.commons:commons-compress:1.12
     |    |    +--- org.apache.httpcomponents:httpmime:4.5.6
     |    |    |    \--- org.apache.httpcomponents:httpclient:4.5.6
     |    |    |         +--- org.apache.httpcomponents:httpcore:4.4.10
     |    |    |         +--- commons-logging:commons-logging:1.2
     |    |    |         \--- commons-codec:commons-codec:1.10
     |    |    \--- org.apache.httpcomponents:httpcore:4.4.10
     |    +--- com.android.tools.build:builder-test-api:4.2.2
     |    |    \--- com.android.tools.ddms:ddmlib:27.2.2
     |    |         +--- com.android.tools:common:27.2.2 (*)
     |    |         +--- net.sf.kxml:kxml2:2.3.0
     |    |         \--- com.google.protobuf:protobuf-java:3.10.0
     |    +--- com.android.tools.build:builder-model:4.2.2
     |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
     |    |    \--- com.android.tools:annotations:27.2.2
     |    +--- com.android.tools.ddms:ddmlib:27.2.2 (*)
     |    +--- com.android.tools.analytics-library:shared:27.2.2
     |    |    +--- com.android.tools.analytics-library:protos:27.2.2
     |    |    |    \--- com.google.protobuf:protobuf-java:3.10.0
     |    |    +--- com.android.tools:annotations:27.2.2
     |    |    +--- com.android.tools:common:27.2.2 (*)
     |    |    +--- com.google.guava:guava:28.1-jre (*)
     |    |    +--- com.google.code.gson:gson:2.8.6
     |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
     |    |    \--- net.java.dev.jna:jna-platform:5.6.0
     |    |         \--- net.java.dev.jna:jna:5.6.0
     |    +--- org.bouncycastle:bcpkix-jdk15on:1.56
     |    |    \--- org.bouncycastle:bcprov-jdk15on:1.56
     |    +--- org.bouncycastle:bcprov-jdk15on:1.56
     |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
     |    +--- org.jetbrains.kotlin:kotlin-reflect:1.4.31
     |    |    \--- org.jetbrains.kotlin:kotlin-stdlib:1.4.31 (*)
     |    +--- com.google.protobuf:protobuf-java:3.10.0
     |    +--- javax.inject:javax.inject:1
     |    +--- org.jetbrains.intellij.deps:trove4j:1.0.20181211
     |    +--- com.android.tools.build:aapt2-proto:4.1.0-alpha01-6193524
     |    |    \--- com.google.protobuf:protobuf-java:3.10.0
     |    \--- xerces:xercesImpl:2.12.0
     |         \--- xml-apis:xml-apis:1.4.01
     +--- com.android.tools.build:builder:4.2.2
     |    +--- com.android.tools.build:builder-model:4.2.2 (*)
     |    +--- com.android.tools.build:builder-test-api:4.2.2 (*)
     |    +--- com.android.tools:sdklib:27.2.2 (*)
     |    +--- com.android.tools:sdk-common:27.2.2 (*)
     |    +--- com.android.tools:common:27.2.2 (*)
     |    +--- com.android.tools.build:manifest-merger:27.2.2
     |    |    +--- com.android.tools:common:27.2.2 (*)
     |    |    +--- com.android.tools:sdklib:27.2.2 (*)
     |    |    +--- com.android.tools:sdk-common:27.2.2 (*)
     |    |    +--- com.google.code.gson:gson:2.8.6
     |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
     |    |    \--- net.sf.kxml:kxml2:2.3.0
     |    +--- com.android.tools.ddms:ddmlib:27.2.2 (*)
     |    +--- com.android:zipflinger:4.2.2
     |    |    \--- com.android.tools:annotations:27.2.2
     |    +--- com.android:signflinger:4.2.2
     |    |    +--- com.android.tools.build:apksig:4.2.2
     |    |    \--- com.android:zipflinger:4.2.2 (*)
     |    +--- com.android.tools.analytics-library:protos:27.2.2 (*)
     |    +--- com.android.tools.analytics-library:tracker:27.2.2
     |    |    +--- com.android.tools:annotations:27.2.2
     |    |    +--- com.android.tools:common:27.2.2 (*)
     |    |    +--- com.android.tools.analytics-library:protos:27.2.2 (*)
     |    |    +--- com.android.tools.analytics-library:shared:27.2.2 (*)
     |    |    +--- com.google.protobuf:protobuf-java:3.10.0
     |    |    +--- com.google.guava:guava:28.1-jre (*)
     |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
     |    +--- com.android.tools.build:apksig:4.2.2
     |    +--- com.android.tools.build:apkzlib:4.2.2
     |    |    +--- com.google.code.findbugs:jsr305:1.3.9 -> 3.0.2
     |    |    +--- com.google.guava:guava:23.0 -> 28.1-jre (*)
     |    |    +--- org.bouncycastle:bcpkix-jdk15on:1.56 (*)
     |    |    +--- org.bouncycastle:bcprov-jdk15on:1.56
     |    |    \--- com.android.tools.build:apksig:4.2.2
     |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
     |    +--- com.squareup:javawriter:2.5.0
     |    +--- org.bouncycastle:bcpkix-jdk15on:1.56 (*)
     |    +--- org.bouncycastle:bcprov-jdk15on:1.56
     |    +--- org.ow2.asm:asm:7.0
     |    +--- org.ow2.asm:asm-tree:7.0
     |    |    \--- org.ow2.asm:asm:7.0
     |    +--- javax.inject:javax.inject:1
     |    +--- org.ow2.asm:asm-commons:7.0
     |    |    +--- org.ow2.asm:asm:7.0
     |    |    +--- org.ow2.asm:asm-tree:7.0 (*)
     |    |    \--- org.ow2.asm:asm-analysis:7.0
     |    |         \--- org.ow2.asm:asm-tree:7.0 (*)
     |    +--- org.ow2.asm:asm-util:7.0
     |    |    +--- org.ow2.asm:asm:7.0
     |    |    +--- org.ow2.asm:asm-tree:7.0 (*)
     |    |    \--- org.ow2.asm:asm-analysis:7.0 (*)
     |    +--- it.unimi.dsi:fastutil:8.4.0
     |    +--- net.sf.jopt-simple:jopt-simple:4.9
     |    \--- com.googlecode.json-simple:json-simple:1.1
     +--- com.android.tools.build:builder-model:4.2.2 (*)
     +--- com.android.tools.external.com-intellij:intellij-core:27.2.2
     |    \--- org.jetbrains.intellij.deps:trove4j:1.0.20181211
     +--- com.android.tools.external.com-intellij:kotlin-compiler:27.2.2
     +--- com.android.tools.external.org-jetbrains:uast:27.2.2
     +--- com.android.tools.build:manifest-merger:27.2.2 (*)
     +--- com.android.tools.lint:lint:27.2.2
     |    +--- com.android.tools.lint:lint-checks:27.2.2
     |    |    +--- com.android.tools.lint:lint-api:27.2.2
     |    |    |    +--- com.android.tools.build:builder-model:4.2.2 (*)
     |    |    |    +--- com.android.tools:sdk-common:27.2.2 (*)
     |    |    |    +--- com.android.tools.lint:lint-model:27.2.2
     |    |    |    |    +--- com.android.tools:common:27.2.2 (*)
     |    |    |    |    +--- com.android.tools:sdk-common:27.2.2 (*)
     |    |    |    |    +--- com.android.tools.build:builder-model:4.2.2 (*)
     |    |    |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
     |    |    |    |    \--- net.sf.kxml:kxml2:2.3.0
     |    |    |    +--- com.google.guava:guava:28.1-jre (*)
     |    |    |    +--- com.android.tools.external.com-intellij:intellij-core:27.2.2 (*)
     |    |    |    +--- com.android.tools.external.com-intellij:kotlin-compiler:27.2.2
     |    |    |    +--- com.android.tools.external.org-jetbrains:uast:27.2.2
     |    |    |    +--- com.android.tools.build:manifest-merger:27.2.2 (*)
     |    |    |    +--- org.ow2.asm:asm:7.0
     |    |    |    +--- org.ow2.asm:asm-tree:7.0 (*)
     |    |    |    +--- org.jetbrains.kotlin:kotlin-reflect:1.4.31 (*)
     |    |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
     |    |    +--- com.google.guava:guava:28.1-jre (*)
     |    |    +--- com.android.tools.external.com-intellij:intellij-core:27.2.2 (*)
     |    |    +--- com.android.tools.external.com-intellij:kotlin-compiler:27.2.2
     |    |    +--- com.android.tools.external.org-jetbrains:uast:27.2.2
     |    |    +--- org.ow2.asm:asm-analysis:7.0 (*)
     |    |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
     |    +--- com.google.guava:guava:28.1-jre (*)
     |    +--- com.android.tools.external.org-jetbrains:uast:27.2.2
     |    +--- com.android.tools.external.com-intellij:kotlin-compiler:27.2.2
     |    +--- com.android.tools.build:manifest-merger:27.2.2 (*)
     |    +--- com.android.tools.analytics-library:shared:27.2.2 (*)
     |    +--- com.android.tools.analytics-library:protos:27.2.2 (*)
     |    +--- com.android.tools.analytics-library:tracker:27.2.2 (*)
     |    +--- org.jetbrains.kotlin:kotlin-reflect:1.4.31 (*)
     |    \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
     +--- com.android.tools.lint:lint-gradle-api:27.2.2
     |    +--- com.android.tools:sdklib:27.2.2 (*)
     |    +--- com.android.tools.lint:lint-model:27.2.2 (*)
     |    +--- com.android.tools.build:gradle-api:4.2.2
     |    |    +--- com.android.tools.build:builder-test-api:4.2.2 (*)
     |    |    +--- com.google.guava:guava:28.1-jre (*)
     |    |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
     |    |    \--- org.ow2.asm:asm:7.0
     |    +--- org.jetbrains.kotlin:kotlin-reflect:1.4.31 (*)
     |    +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)
     |    \--- com.google.guava:guava:28.1-jre (*)
     +--- com.android:zipflinger:4.2.2 (*)
     +--- org.codehaus.groovy:groovy-all:2.4.15
     +--- org.jetbrains.kotlin:kotlin-reflect:1.4.31 (*)
     \--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.4.31 (*)

lintPublish - Configuration to publish external lint check jar
No dependencies

provided - Provided dependencies for 'main' sources (deprecated: use 'compileOnly' instead). (n)
No dependencies

releaseAnnotationProcessor - Classpath for the annotation processor for 'release'. (n)
No dependencies

releaseApi - API dependencies for 'release' sources. (n)
No dependencies

releaseApiDependenciesMetadata
No dependencies

releaseApk - Apk dependencies for 'release' sources (deprecated: use 'releaseRuntimeOnly' instead). (n)
No dependencies

releaseCompile - Compile dependencies for 'release' sources (deprecated: use 'releaseImplementation' instead). (n)
No dependencies

releaseCompileOnly - Compile only dependencies for 'release' sources. (n)
No dependencies

releaseCompileOnlyDependenciesMetadata
No dependencies

releaseImplementation - Implementation only dependencies for 'release' sources. (n)
No dependencies

releaseImplementationDependenciesMetadata
No dependencies

releaseIntransitiveDependenciesMetadata
No dependencies

releaseProvided - Provided dependencies for 'release' sources (deprecated: use 'releaseCompileOnly' instead). (n)
No dependencies

releaseRuntimeOnly - Runtime only dependencies for 'release' sources. (n)
No dependencies

releaseWearApp - Link to a wear app to embed for object 'release'. (n)
No dependencies

runtimeOnly - Runtime only dependencies for 'main' sources. (n)
No dependencies

testAnnotationProcessor - Classpath for the annotation processor for 'test'. (n)
No dependencies

testApi - API dependencies for 'test' sources. (n)
No dependencies

testApiDependenciesMetadata
No dependencies

testApk - Apk dependencies for 'test' sources (deprecated: use 'testRuntimeOnly' instead). (n)
No dependencies

testAtestAnnotationProcessor - Classpath for the annotation processor for 'testAtest'. (n)
No dependencies

testAtestApi - API dependencies for 'testAtest' sources. (n)
No dependencies

testAtestApiDependenciesMetadata
No dependencies

testAtestApk - Apk dependencies for 'testAtest' sources (deprecated: use 'testAtestRuntimeOnly' instead). (n)
No dependencies

testAtestCompile - Compile dependencies for 'testAtest' sources (deprecated: use 'testAtestImplementation' instead). (n)
No dependencies

testAtestCompileOnly - Compile only dependencies for 'testAtest' sources. (n)
No dependencies

testAtestCompileOnlyDependenciesMetadata
No dependencies

testAtestDebugAnnotationProcessor - Classpath for the annotation processor for 'testAtestDebug'. (n)
No dependencies

testAtestDebugApi - API dependencies for 'testAtestDebug' sources. (n)
No dependencies

testAtestDebugApiDependenciesMetadata
No dependencies

testAtestDebugApk - Apk dependencies for 'testAtestDebug' sources (deprecated: use 'testAtestDebugRuntimeOnly' instead). (n)
No dependencies

testAtestDebugCompile - Compile dependencies for 'testAtestDebug' sources (deprecated: use 'testAtestDebugImplementation' instead). (n)
No dependencies

testAtestDebugCompileOnly - Compile only dependencies for 'testAtestDebug' sources. (n)
No dependencies

testAtestDebugCompileOnlyDependenciesMetadata
No dependencies

testAtestDebugImplementation - Implementation only dependencies for 'testAtestDebug' sources. (n)
No dependencies

testAtestDebugImplementationDependenciesMetadata
No dependencies

testAtestDebugIntransitiveDependenciesMetadata
No dependencies

testAtestDebugProvided - Provided dependencies for 'testAtestDebug' sources (deprecated: use 'testAtestDebugCompileOnly' instead). (n)
No dependencies

testAtestDebugRuntimeOnly - Runtime only dependencies for 'testAtestDebug' sources. (n)
No dependencies

testAtestDebugWearApp - Link to a wear app to embed for object 'testAtestDebug'. (n)
No dependencies

testAtestImplementation - Implementation only dependencies for 'testAtest' sources. (n)
No dependencies

testAtestImplementationDependenciesMetadata
No dependencies

testAtestIntransitiveDependenciesMetadata
No dependencies

testAtestProvided - Provided dependencies for 'testAtest' sources (deprecated: use 'testAtestCompileOnly' instead). (n)
No dependencies

testAtestReleaseAnnotationProcessor - Classpath for the annotation processor for 'testAtestRelease'. (n)
No dependencies

testAtestReleaseApi - API dependencies for 'testAtestRelease' sources. (n)
No dependencies

testAtestReleaseApiDependenciesMetadata
No dependencies

testAtestReleaseApk - Apk dependencies for 'testAtestRelease' sources (deprecated: use 'testAtestReleaseRuntimeOnly' instead). (n)
No dependencies

testAtestReleaseCompile - Compile dependencies for 'testAtestRelease' sources (deprecated: use 'testAtestReleaseImplementation' instead). (n)
No dependencies

testAtestReleaseCompileOnly - Compile only dependencies for 'testAtestRelease' sources. (n)
No dependencies

testAtestReleaseCompileOnlyDependenciesMetadata
No dependencies

testAtestReleaseImplementation - Implementation only dependencies for 'testAtestRelease' sources. (n)
No dependencies

testAtestReleaseImplementationDependenciesMetadata
No dependencies

testAtestReleaseIntransitiveDependenciesMetadata
No dependencies

testAtestReleaseProvided - Provided dependencies for 'testAtestRelease' sources (deprecated: use 'testAtestReleaseCompileOnly' instead). (n)
No dependencies

testAtestReleaseRuntimeOnly - Runtime only dependencies for 'testAtestRelease' sources. (n)
No dependencies

testAtestReleaseWearApp - Link to a wear app to embed for object 'testAtestRelease'. (n)
No dependencies

testAtestRuntimeOnly - Runtime only dependencies for 'testAtest' sources. (n)
No dependencies

testAtestWearApp - Link to a wear app to embed for object 'testAtest'. (n)
No dependencies

testCompile - Compile dependencies for 'test' sources (deprecated: use 'testImplementation' instead).
No dependencies

testCompileOnly - Compile only dependencies for 'test' sources. (n)
No dependencies

testCompileOnlyDependenciesMetadata
No dependencies

testDebugAnnotationProcessor - Classpath for the annotation processor for 'testDebug'. (n)
No dependencies

testDebugApi - API dependencies for 'testDebug' sources. (n)
No dependencies

testDebugApiDependenciesMetadata
No dependencies

testDebugApk - Apk dependencies for 'testDebug' sources (deprecated: use 'testDebugRuntimeOnly' instead). (n)
No dependencies

testDebugCompile - Compile dependencies for 'testDebug' sources (deprecated: use 'testDebugImplementation' instead). (n)
No dependencies

testDebugCompileOnly - Compile only dependencies for 'testDebug' sources. (n)
No dependencies

testDebugCompileOnlyDependenciesMetadata
No dependencies

testDebugImplementation - Implementation only dependencies for 'testDebug' sources. (n)
No dependencies

testDebugImplementationDependenciesMetadata
No dependencies

testDebugIntransitiveDependenciesMetadata
No dependencies

testDebugProvided - Provided dependencies for 'testDebug' sources (deprecated: use 'testDebugCompileOnly' instead). (n)
No dependencies

testDebugRuntimeOnly - Runtime only dependencies for 'testDebug' sources. (n)
No dependencies

testDebugWearApp - Link to a wear app to embed for object 'testDebug'. (n)
No dependencies

testImplementation - Implementation only dependencies for 'test' sources. (n)
No dependencies

testImplementationDependenciesMetadata
No dependencies

testIntransitiveDependenciesMetadata
No dependencies

testProvided - Provided dependencies for 'test' sources (deprecated: use 'testCompileOnly' instead). (n)
No dependencies

testReleaseAnnotationProcessor - Classpath for the annotation processor for 'testRelease'. (n)
No dependencies

testReleaseApi - API dependencies for 'testRelease' sources. (n)
No dependencies

testReleaseApiDependenciesMetadata
No dependencies

testReleaseApk - Apk dependencies for 'testRelease' sources (deprecated: use 'testReleaseRuntimeOnly' instead). (n)
No dependencies

testReleaseCompile - Compile dependencies for 'testRelease' sources (deprecated: use 'testReleaseImplementation' instead). (n)
No dependencies

testReleaseCompileOnly - Compile only dependencies for 'testRelease' sources. (n)
No dependencies

testReleaseCompileOnlyDependenciesMetadata
No dependencies

testReleaseImplementation - Implementation only dependencies for 'testRelease' sources. (n)
No dependencies

testReleaseImplementationDependenciesMetadata
No dependencies

testReleaseIntransitiveDependenciesMetadata
No dependencies

testReleaseProvided - Provided dependencies for 'testRelease' sources (deprecated: use 'testReleaseCompileOnly' instead). (n)
No dependencies

testReleaseRuntimeOnly - Runtime only dependencies for 'testRelease' sources. (n)
No dependencies

testReleaseWearApp - Link to a wear app to embed for object 'testRelease'. (n)
No dependencies

testRuntimeOnly - Runtime only dependencies for 'test' sources. (n)
No dependencies

testWearApp - Link to a wear app to embed for object 'test'. (n)
No dependencies

wearApp - Link to a wear app to embed for object 'main'. (n)
No dependencies

(c) - dependency constraint
(*) - dependencies omitted (listed previously)

(n) - Not resolved (configuration is not meant to be resolved)

A web-based, searchable dependency report is available by adding the --scan option.

BUILD SUCCESSFUL in 1s
7 actionable tasks: 1 executed, 6 up-to-date
