# This is a sample Python script.

# Press ⌃R to execute it or replace it with your code.
# Press Double ⇧ to search everywhere for classes, files, tool windows, actions, and settings.

# Press the green button in the gutter to run the script.
import os
import random
import shutil
import subprocess
import sys

from ReadTextUtil import ReadTextUtil

lowercaseLetters = ['z', 'y', 'x', 'w', 'v', 'u', 't', 's', 'r', 'q', 'p', 'o', 'n', 'm', 'l', 'k', 'j', 'i', 'h', 'g',
                    'f', 'e', 'd', 'c', 'b', 'a']


def creatMethodText(readTextUtil: ReadTextUtil, filePath):
    file = open(filePath, "w")
    valueList = []
    for index in range(6000):
        value = readTextUtil.getProguardValue()
        valueList.append(value)

    valueList2 = {}.fromkeys(valueList).keys()
    for item in valueList2:
        file.write(item)
        file.write("\n")
    file.close()


def get_apk(dirPath):
    if not os.path.exists(dirPath):
        return
    file_list = []
    for root, dirs, files in os.walk(dirPath):
        for fileObject in files:
            base_name = os.path.basename(fileObject)
            if base_name.endswith(".apk"):
                file_list.append(os.path.join(root, fileObject))
    return file_list

def delapk(dirPath):
    if not os.path.exists(dirPath):
        return
    for root, dirs, files in os.walk(dirPath):
        for fileObject in files:
            base_name = os.path.basename(fileObject)
            if base_name.endswith(".apk"):
                os.remove(os.path.join(root, fileObject))


if __name__ == '__main__':
    readTextUtil = ReadTextUtil()

    args = sys.argv

    for action in args:
        print("action-->" + action)

    packNumberList = []
    appNameList = []

    if len(args) <= 1:
        packNumberList = ["p01"]
    else:
        del args[0]
        packNumberList = args

    for item in packNumberList:
        print("pack-->" + item)

    homePath = os.getenv('HOME')
    projectPath = os.path.abspath(__file__).replace("/main.py","")
    gradlePath = projectPath + "/app/build.gradle"

    appVersionCode = 0

    fileR = open(gradlePath)
    allLines = fileR.readlines()

    fileR.close()

    for item in allLines:
        if "//all versionCode" in item:
            appVersionCode = int(item.replace("//all versionCode", "").replace(" ", "").replace("\n", ""))

    appVersionCodeNew = appVersionCode + 1

    versionNameList = list(str(appVersionCodeNew))
    versionName = ""
    for index in range(len(versionNameList)):
        print("index-->" + str(index))
        print("versionNameList[index]-->" + versionNameList[index])
        if index == 0:
            versionName = versionName + versionNameList[index]
        else:
            versionName = versionName + "." + versionNameList[index]

    print("versionName-->" + versionName)
    print("appVersionCode-->" + str(appVersionCode))

    packAction = "sh gradlew clean"

    if len(packNumberList) > 0:

        fileW = open(gradlePath, "w")

        for item in allLines:
            hasAdd = False
            for packNumber in packNumberList:
                if "//" + packNumber + " keyAlias" in item:
                    print("keyAlias-->" + item)
                    fileW.write("                keyAlias '" + packNumber + str(
                        appVersionCodeNew) + "'//" + packNumber + " keyAlias\n")
                    print("line1-->" + "                keyAlias '" + packNumber + str(
                        appVersionCodeNew) + "'//" + packNumber + " keyAlias\n")
                    hasAdd = True
                elif ".jks')//" + packNumber + " storeFile" in item or ".keystore')//" + packNumber + " storeFile" in item:
                    print("storeFile-->" + item)
                    fileW.write("                storeFile file('../keystore/" + packNumber + str(
                        appVersionCodeNew) + ".keystore')//" + packNumber + " storeFile\n")
                    print("line2-->" + "                storeFile file('../keystore/" + packNumber + str(
                        appVersionCodeNew) + ".keystore')//" + packNumber + " storeFile\n")
                    hasAdd = True

            if "//versionCode" in item:
                fileW.write("            versionCode " + str(appVersionCodeNew) + "//" + "versionCode\n")
            elif "//versionName" in item:
                fileW.write("            versionName \"" + versionName + "\"//" + "versionName\n")
            elif "//all versionCode" in item:
                fileW.write("    //all versionCode " + str(appVersionCodeNew) + "\n")
            elif hasAdd:
                print("xxx")
            else:
                fileW.write(item)

        fileW.close()

        for packNumber in packNumberList:

            appName = ""

            for item in allLines:
                if "//" + packNumber + " packageName" in item:
                    appName = item.replace("{//" + packNumber + " packageName", "").replace(" ", "").replace("\n", "")

            print("appName-->" + appName)

            appNameList.append(appName)

            keyAlias = packNumber + str(appVersionCodeNew)

            keystorePath = projectPath + "/keystore"
            keyActionShell = "keytool -genkey -alias " + packNumber + str(
                appVersionCodeNew) + " -keyalg RSA -keysize 2048 -validity 36500 -keystore " + packNumber + str(
                appVersionCodeNew) + ".keystore"

            os.chdir(keystorePath)

            key_pwd = "123456789"

            keyName1 = ''.join(random.sample(lowercaseLetters, random.randint(3, 10)))
            keyName2 = ''.join(random.sample(lowercaseLetters, random.randint(3, 10)))
            keyName3 = ''.join(random.sample(lowercaseLetters, random.randint(3, 10)))
            keyName4 = ''.join(random.sample(lowercaseLetters, random.randint(3, 10)))
            keyName5 = ''.join(random.sample(lowercaseLetters, random.randint(3, 10)))

            areaCode = random.randint(10, 999)

            allShellAction = "echo '" + key_pwd + "\n" + key_pwd + "\n" + keyName1 + "\n" + keyName2 + "\n" + keyName3 + "\n" + keyName4 + "\n" + keyName5 + "\n" + str(
                areaCode) + "\ny\n" + key_pwd + "\n" + key_pwd + "\n' | " + keyActionShell

            print("allShellAction-->:" + allShellAction)

            os.system(allShellAction)

            creatMethodText(readTextUtil, projectPath + "/app/src/" + appName + "/" + appName + ".txt")

            os.chdir(projectPath)

            packAction = packAction + " assemble" + appName + "release"

    print("packAction-->" + packAction)
    os.system(packAction)

    apkPath = projectPath + "/app/build/outputs/apk"

    apkPathList = get_apk(apkPath)

    if os.path.exists(apkPath) is True:
        delapk(homePath + "/Desktop/apk")

    for item in apkPathList:
        print("apk--success--path-->" + item)
        shutil.move(item, homePath + "/Desktop/apk")

    os.system("open "+homePath + "/Desktop/apk")

# See PyCharm help at https://www.jetbrains.com/help/pycharm/
