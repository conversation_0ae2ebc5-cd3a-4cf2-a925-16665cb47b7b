dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
        maven {
            url 'https://maven.accuauth.com/repository/maven-releases/'
            // If your gradle version is greater than 7.0, you need to add the following configuration to allow pulling aar via http
            allowInsecureProtocol = true
        }
    }
}
rootProject.name = "<PERSON>Qi"
include ':app'


